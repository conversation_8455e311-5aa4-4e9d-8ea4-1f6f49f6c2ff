#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创业板动态因子策略TICK回测系统 - 历史数据批量下载脚本
自动下载所有创业板股票的18个月历史数据
"""

import os
import sys
import time
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
import threading
from concurrent.futures import ThreadPoolExecutor
# import psutil  # 可选依赖

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.quantization.data_sources.historical_data_downloader import HistoricalDataDownloader
from src.quantization.data_sources.smart_data_downloader import SmartDataDownloader
from src.quantization.data_sources.data_storage import DataStorage


class BatchDownloadManager:
    """批量下载管理器"""
    
    def __init__(self):
        """初始化批量下载管理器"""
        self.logger = self._setup_logger()

        # 初始化核心组件
        self.smart_downloader = SmartDataDownloader()
        self.historical_downloader = HistoricalDataDownloader()
        self.data_storage = DataStorage()
        
        # 下载配置
        self.max_threads = 6  # 初始线程数
        self.min_threads = 2  # 最小线程数
        self.max_retries = 3  # 最大重试次数
        self.batch_size = 50  # 批次大小
        
        # 统计信息
        self.stats = {
            'total_stocks': 0,
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'success_rate': 0.0,
            'start_time': None,
            'end_time': None,
            'duration': 0,
            'errors': []
        }
        
        # 线程锁
        self.stats_lock = threading.Lock()
        
        self.logger.info("批量下载管理器初始化完成")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('BatchDownloadManager')
        logger.setLevel(logging.INFO)
        
        # 创建文件处理器
        log_file = f"batch_download_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 设置格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def get_chinext_stocks(self) -> List[str]:
        """获取所有创业板股票代码"""
        try:
            self.logger.info("正在获取创业板股票列表...")
            # 使用备用方法获取创业板股票列表
            stocks = self._get_chinext_stocks_backup()

            self.logger.info(f"成功获取 {len(stocks)} 只创业板股票")
            return stocks[:1000]  # 限制最多1000只股票

        except Exception as e:
            self.logger.error(f"获取创业板股票列表失败: {e}")
            # 返回一些常见的创业板股票作为备用
            return [f"30{str(i).zfill(4)}.SZ" for i in range(1, 101)]

    def _get_chinext_stocks_backup(self) -> List[str]:
        """备用方法获取创业板股票列表"""
        # 生成创业板股票代码范围
        stocks = []
        for i in range(1, 1001):
            code = f"30{str(i).zfill(4)}.SZ"
            if self._is_valid_stock_code(code):
                stocks.append(code)

        self.logger.info(f"备用方法生成 {len(stocks)} 只创业板股票代码")
        return stocks
    
    def _is_valid_stock_code(self, code: str) -> bool:
        """验证股票代码是否有效"""
        try:
            # 简单验证：创业板股票代码格式
            if not code.endswith('.SZ'):
                return False
            
            stock_num = code.split('.')[0]
            if not stock_num.startswith('30') or len(stock_num) != 6:
                return False
            
            return True
        except:
            return False
    
    def calculate_date_range(self) -> tuple:
        """计算18个月的日期范围"""
        end_date = datetime(2024, 6, 30)  # 2024年6月30日
        start_date = datetime(2023, 1, 1)  # 2023年1月1日
        
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        self.logger.info(f"下载日期范围: {start_str} 至 {end_str}")
        return start_str, end_str
    
    def monitor_system_resources(self):
        """监控系统资源并动态调整"""
        try:
            # 简化的资源监控（不依赖psutil）
            import os

            # 基于线程数动态调整
            if self.max_threads > 6:
                self.max_threads = max(self.min_threads, self.max_threads - 1)
                self.logger.info(f"调整线程数至 {self.max_threads}")
            elif self.max_threads < 4:
                self.max_threads = min(8, self.max_threads + 1)
                self.logger.info(f"提高线程数至 {self.max_threads}")

        except Exception as e:
            self.logger.error(f"系统资源监控失败: {e}")
    
    def update_stats(self, completed: int = 0, failed: int = 0, error: str = None):
        """更新统计信息"""
        with self.stats_lock:
            self.stats['completed_tasks'] += completed
            self.stats['failed_tasks'] += failed
            
            if error:
                self.stats['errors'].append({
                    'time': datetime.now().isoformat(),
                    'error': error
                })
            
            total_processed = self.stats['completed_tasks'] + self.stats['failed_tasks']
            if total_processed > 0:
                self.stats['success_rate'] = (self.stats['completed_tasks'] / total_processed) * 100
    
    def print_progress(self):
        """打印下载进度"""
        with self.stats_lock:
            total_processed = self.stats['completed_tasks'] + self.stats['failed_tasks']
            if self.stats['total_tasks'] > 0:
                progress = (total_processed / self.stats['total_tasks']) * 100
                
                print(f"\r下载进度: {progress:.1f}% "
                      f"({total_processed}/{self.stats['total_tasks']}) "
                      f"成功: {self.stats['completed_tasks']} "
                      f"失败: {self.stats['failed_tasks']} "
                      f"成功率: {self.stats['success_rate']:.1f}%", end='')
    
    def batch_download_stocks(self, stock_codes: List[str], start_date: str, end_date: str) -> bool:
        """批量下载股票数据"""
        try:
            self.logger.info(f"开始批量下载 {len(stock_codes)} 只股票的历史数据")
            self.logger.info(f"日期范围: {start_date} 至 {end_date}")
            
            # 更新统计信息
            self.stats['total_stocks'] = len(stock_codes)
            self.stats['start_time'] = datetime.now()
            
            # 计算总任务数（每只股票 × 2种数据类型）
            data_types = ['minute', 'daily']
            self.stats['total_tasks'] = len(stock_codes) * len(data_types)
            
            self.logger.info(f"预计总任务数: {self.stats['total_tasks']}")
            
            # 分批处理股票
            for i in range(0, len(stock_codes), self.batch_size):
                batch_stocks = stock_codes[i:i + self.batch_size]
                batch_num = i // self.batch_size + 1
                total_batches = (len(stock_codes) + self.batch_size - 1) // self.batch_size
                
                self.logger.info(f"处理第 {batch_num}/{total_batches} 批股票 ({len(batch_stocks)} 只)")
                
                # 监控系统资源
                self.monitor_system_resources()
                
                # 下载当前批次的数据
                success = self._download_batch(batch_stocks, start_date, end_date, data_types)
                
                if not success:
                    self.logger.warning(f"第 {batch_num} 批下载出现问题，继续下一批")
                
                # 短暂休息，避免过度占用资源
                time.sleep(2)
            
            # 完成统计
            self.stats['end_time'] = datetime.now()
            self.stats['duration'] = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
            
            self.logger.info("批量下载完成")
            return True
            
        except Exception as e:
            self.logger.error(f"批量下载失败: {e}")
            return False
    
    def _download_batch(self, stock_codes: List[str], start_date: str, end_date: str, data_types: List[str]) -> bool:
        """下载一批股票数据"""
        try:
            for data_type in data_types:
                self.logger.info(f"下载 {data_type} 数据...")
                
                # 使用历史数据下载器进行批量下载
                result = self.historical_downloader.batch_download(
                    stock_codes=stock_codes,
                    start_date=start_date,
                    end_date=end_date,
                    data_types=[data_type],
                    resume=True  # 启用断点续传
                )
                
                if result:
                    self.update_stats(completed=len(stock_codes))
                    self.logger.info(f"{data_type} 数据下载成功")
                else:
                    self.update_stats(failed=len(stock_codes), error=f"{data_type} 数据下载失败")
                    self.logger.warning(f"{data_type} 数据下载失败")
                
                # 更新进度显示
                self.print_progress()
            
            return True
            
        except Exception as e:
            error_msg = f"批次下载失败: {e}"
            self.logger.error(error_msg)
            self.update_stats(failed=len(stock_codes) * len(data_types), error=error_msg)
            return False

    def generate_download_report(self) -> Dict[str, Any]:
        """生成下载完成报告"""
        report = {
            'summary': {
                'total_stocks': self.stats['total_stocks'],
                'total_tasks': self.stats['total_tasks'],
                'completed_tasks': self.stats['completed_tasks'],
                'failed_tasks': self.stats['failed_tasks'],
                'success_rate': self.stats['success_rate'],
                'duration_seconds': self.stats['duration'],
                'duration_formatted': str(timedelta(seconds=int(self.stats['duration'])))
            },
            'performance': {
                'tasks_per_second': self.stats['completed_tasks'] / max(self.stats['duration'], 1),
                'average_time_per_task': self.stats['duration'] / max(self.stats['total_tasks'], 1)
            },
            'errors': self.stats['errors'][-10:],  # 最近10个错误
            'timestamp': datetime.now().isoformat()
        }

        return report

    def save_report(self, report: Dict[str, Any]):
        """保存下载报告"""
        try:
            report_file = f"download_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            self.logger.info(f"下载报告已保存: {report_file}")

        except Exception as e:
            self.logger.error(f"保存报告失败: {e}")

    def print_final_report(self, report: Dict[str, Any]):
        """打印最终报告"""
        print("\n" + "="*80)
        print("创业板动态因子策略TICK回测系统 - 历史数据批量下载完成报告")
        print("="*80)

        summary = report['summary']
        performance = report['performance']

        print(f"📊 下载统计:")
        print(f"   总股票数: {summary['total_stocks']}")
        print(f"   总任务数: {summary['total_tasks']}")
        print(f"   成功任务: {summary['completed_tasks']}")
        print(f"   失败任务: {summary['failed_tasks']}")
        print(f"   成功率: {summary['success_rate']:.2f}%")
        print(f"   总耗时: {summary['duration_formatted']}")

        print(f"\n⚡ 性能指标:")
        print(f"   任务处理速度: {performance['tasks_per_second']:.2f} 任务/秒")
        print(f"   平均任务耗时: {performance['average_time_per_task']:.2f} 秒/任务")

        if report['errors']:
            print(f"\n❌ 最近错误 (显示最近{len(report['errors'])}个):")
            for i, error in enumerate(report['errors'][-5:], 1):
                print(f"   {i}. {error['time']}: {error['error']}")

        # 质量评估
        if summary['success_rate'] >= 95:
            print(f"\n✅ 质量评估: 优秀 (成功率 {summary['success_rate']:.2f}% ≥ 95%)")
        elif summary['success_rate'] >= 90:
            print(f"\n⚠️  质量评估: 良好 (成功率 {summary['success_rate']:.2f}% ≥ 90%)")
        else:
            print(f"\n❌ 质量评估: 需要改进 (成功率 {summary['success_rate']:.2f}% < 90%)")

        print("="*80)

    def run_batch_download(self):
        """运行批量下载主流程"""
        try:
            print("🚀 启动创业板动态因子策略TICK回测系统 - 历史数据批量下载")
            print("="*80)

            # 1. 获取创业板股票列表
            stock_codes = self.get_chinext_stocks()
            if not stock_codes:
                self.logger.error("无法获取股票列表，下载终止")
                return False

            # 2. 计算日期范围
            start_date, end_date = self.calculate_date_range()

            # 3. 执行批量下载
            success = self.batch_download_stocks(stock_codes, start_date, end_date)

            # 4. 生成和保存报告
            report = self.generate_download_report()
            self.save_report(report)
            self.print_final_report(report)

            return success

        except Exception as e:
            self.logger.error(f"批量下载主流程失败: {e}")
            return False


def main():
    """主函数"""
    try:
        # 创建批量下载管理器
        manager = BatchDownloadManager()

        # 运行批量下载
        success = manager.run_batch_download()

        if success:
            print("\n🎉 批量下载任务完成！")
            return 0
        else:
            print("\n❌ 批量下载任务失败！")
            return 1

    except KeyboardInterrupt:
        print("\n⏹️  用户中断下载")
        return 2
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        return 3


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

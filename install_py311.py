"""
A股数据采集框架 - Python 3.11专用安装脚本

专为Python 3.11环境设计的安装脚本
解决numpy版本兼容性和sqlite3导入问题
"""

import sys
import subprocess
import os
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    
    version = sys.version_info
    print(f"   当前版本: Python {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    if version.major == 3 and version.minor == 11:
        print("✅ Python 3.11 - 使用兼容性安装")
    elif version >= (3, 12):
        print("⚠️  Python 3.12+ - 可能存在兼容性问题")
    else:
        print("✅ Python版本兼容")
    
    return True

def install_packages_for_py311():
    """为Python 3.11安装兼容的包"""
    print("\n📦 安装Python 3.11兼容的依赖包...")
    
    # Python 3.11兼容的包版本
    packages = [
        "numpy>=1.23.0",      # 支持Python 3.11
        "pandas>=1.5.0",      # 支持Python 3.11
        "requests>=2.28.0",   # 网络请求
        "beautifulsoup4>=4.11.0",  # HTML解析
        "lxml>=4.9.0",        # XML解析
        "python-dateutil>=2.8.0",  # 日期处理
    ]
    
    # 可选包（可能安装失败但不影响基本功能）
    optional_packages = [
        "akshare>=1.9.0",     # A股数据源
        "matplotlib>=3.6.0",  # 图表
    ]
    
    success_count = 0
    failed_packages = []
    
    print("安装核心依赖包...")
    for package in packages:
        try:
            print(f"  📦 安装 {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package, "--upgrade"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"  ✅ {package} 安装成功")
                success_count += 1
            else:
                print(f"  ❌ {package} 安装失败")
                print(f"     错误: {result.stderr.strip()}")
                failed_packages.append(package)
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ {package} 安装超时")
            failed_packages.append(package)
        except Exception as e:
            print(f"  ❌ {package} 安装异常: {e}")
            failed_packages.append(package)
    
    print(f"\n安装可选依赖包...")
    for package in optional_packages:
        try:
            print(f"  📦 安装 {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package, "--upgrade"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"  ✅ {package} 安装成功")
                success_count += 1
            else:
                print(f"  ⚠️  {package} 安装失败（可选包）")
                print(f"     错误: {result.stderr.strip()}")
                
        except Exception as e:
            print(f"  ⚠️  {package} 安装异常（可选包）: {e}")
    
    total_core = len(packages)
    total_optional = len(optional_packages)
    
    print(f"\n📊 安装结果:")
    print(f"   核心包: {total_core - len(failed_packages)}/{total_core} 成功")
    print(f"   可选包: 已尝试安装")
    
    if failed_packages:
        print(f"\n❌ 安装失败的核心包: {failed_packages}")
        print(f"   可以尝试手动安装:")
        for pkg in failed_packages:
            print(f"   pip install {pkg}")
        return False
    else:
        print("✅ 所有核心包安装成功")
        return True

def test_imports():
    """测试关键模块导入"""
    print("\n🧪 测试关键模块导入...")
    
    test_modules = [
        ('numpy', 'np'),
        ('pandas', 'pd'),
        ('requests', None),
        ('bs4', 'BeautifulSoup'),
        ('sqlite3', None),
        ('json', None),
        ('pickle', None)
    ]
    
    success_count = 0
    
    for module_name, import_as in test_modules:
        try:
            if import_as:
                if module_name == 'bs4':
                    from bs4 import BeautifulSoup
                else:
                    exec(f"import {module_name} as {import_as}")
            else:
                exec(f"import {module_name}")
            
            print(f"  ✅ {module_name}")
            success_count += 1
        except ImportError as e:
            print(f"  ❌ {module_name}: {e}")
        except Exception as e:
            print(f"  ⚠️  {module_name}: {e}")
    
    print(f"✅ 模块导入测试: {success_count}/{len(test_modules)} 成功")
    return success_count >= len(test_modules) * 0.8

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建项目目录...")
    
    directories = [
        "data",
        "data/cache",
        "logs", 
        "results",
        "config"
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            print(f"  ✅ {directory}")
        except Exception as e:
            print(f"  ❌ {directory}: {e}")
    
    return True

def create_config_file():
    """创建配置文件"""
    print("\n⚙️  创建配置文件...")
    
    config_content = """# A股数据采集框架配置文件
# 适用于Python 3.11环境

# 数据库设置
DATABASE_URL=sqlite:///data/ashare_data.db

# 缓存设置
CACHE_ENABLED=true
CACHE_TTL_DAYS=7
CACHE_MAX_SIZE_MB=500

# 限流设置
AKSHARE_RATE_LIMIT=1.0
WEB_SCRAPER_RATE_LIMIT=2.0
MAX_RETRIES=3

# 日志设置
LOG_LEVEL=INFO

# 环境设置
ENVIRONMENT=default
"""
    
    try:
        with open("config_py311.env", "w", encoding="utf-8") as f:
            f.write(config_content)
        print("  ✅ 配置文件创建成功: config_py311.env")
        return True
    except Exception as e:
        print(f"  ❌ 配置文件创建失败: {e}")
        return False

def run_basic_test():
    """运行基础测试"""
    print("\n🧪 运行基础功能测试...")
    
    try:
        # 测试基础导入
        print("  测试框架导入...")
        
        # 测试配置
        sys.path.insert(0, '.')
        from data_acquisition.config.settings import Config
        config = Config()
        print("  ✅ 配置模块正常")
        
        # 测试股票代码工具
        from data_acquisition.utils.stock_codes import normalize_stock_code
        result = normalize_stock_code('000001')
        if result == '000001.SZ':
            print("  ✅ 股票代码工具正常")
        else:
            print(f"  ⚠️  股票代码工具异常: {result}")
        
        # 测试数据库
        from data_acquisition.storage.database import DatabaseManager
        db = DatabaseManager(config)
        print("  ✅ 数据库模块正常")
        
        print("✅ 基础功能测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 基础功能测试失败: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "="*60)
    print("🎉 Python 3.11环境安装完成！")
    print("="*60)
    
    print("\n📚 接下来可以:")
    print("  1. 运行基础测试:")
    print("     python test_basic_cn.py")
    
    print("\n  2. 快速体验框架:")
    print("     python quick_start_cn.py")
    
    print("\n  3. 查看使用示例:")
    print("     python examples/basic_usage_cn.py")
    
    print("\n⚙️  配置说明:")
    print("  - 配置文件: config_py311.env")
    print("  - 数据目录: data/")
    print("  - 日志目录: logs/")
    
    print("\n⚠️  注意事项:")
    print("  - 如果akshare安装失败，可以只使用网络爬虫功能")
    print("  - 建议在虚拟环境中使用框架")
    print("  - 遇到问题请查看日志文件")

def main():
    """主安装函数"""
    print("🚀 A股数据采集框架 - Python 3.11专用安装")
    print("="*60)
    
    steps = [
        ("检查Python版本", check_python_version),
        ("安装依赖包", install_packages_for_py311),
        ("测试模块导入", test_imports),
        ("创建目录", create_directories),
        ("创建配置文件", create_config_file),
        ("运行基础测试", run_basic_test)
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        
        if not step_func():
            print(f"\n❌ 安装失败于: {step_name}")
            print("\n🔧 故障排除建议:")
            print("  1. 检查网络连接")
            print("  2. 更新pip: python -m pip install --upgrade pip")
            print("  3. 使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/")
            print("  4. 在虚拟环境中重试")
            return False
    
    show_next_steps()
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

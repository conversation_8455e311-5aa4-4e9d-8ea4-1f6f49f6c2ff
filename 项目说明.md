# A股数据采集框架 - 项目说明

## 🎯 项目概述

这是一个专为A股量化投资设计的**企业级数据采集框架**，具有以下特点：

- ✅ **代码整洁**：模块化设计，清晰的代码结构
- ✅ **中文注释**：完整的中文注释和文档
- ✅ **生产就绪**：稳健的错误处理和资源管理
- ✅ **高性能**：智能缓存和并发处理
- ✅ **易扩展**：插件化架构，易于添加新功能

## 📁 项目结构

```
A股数据采集框架/
├── data_acquisition/           # 核心框架代码
│   ├── core/                  # 核心模块
│   │   ├── data_manager.py    # 数据管理器（英文版）
│   │   ├── data_manager_cn.py # 数据管理器（中文版）
│   │   ├── akshare_provider.py # akshare数据源
│   │   └── web_scraper.py     # 网络爬虫数据源
│   ├── storage/               # 存储模块
│   │   ├── database.py        # 数据库管理
│   │   └── cache_manager.py   # 缓存管理
│   ├── utils/                 # 工具模块
│   │   ├── stock_codes.py     # 股票代码工具（英文版）
│   │   ├── stock_codes_cn.py  # 股票代码工具（中文版）
│   │   ├── rate_limiter.py    # 限流控制
│   │   ├── data_validator.py  # 数据验证
│   │   └── logger.py          # 日志系统
│   ├── config/                # 配置模块
│   │   └── settings.py        # 配置管理
│   └── backtesting_interface/ # 回测接口
│       ├── data_interface.py  # 数据接口
│       └── data_preprocessor.py # 数据预处理
├── examples/                  # 使用示例
│   ├── basic_usage.py         # 基础使用（英文）
│   ├── basic_usage_cn.py      # 基础使用（中文）
│   ├── backtesting_example.py # 回测示例（英文）
│   └── strategy_backtest_cn.py # 策略回测（中文）
├── README.md                  # 英文文档
├── README_CN.md               # 中文文档
├── 项目说明.md                # 项目说明（本文件）
├── requirements.txt           # 依赖包列表
├── setup_framework_cn.py      # 中文安装脚本
├── test_framework_cn.py       # 中文测试脚本
└── quick_start_cn.py          # 中文快速开始
```

## 🚀 核心特性

### 1. 多数据源智能切换
```python
# 自动从akshare获取，失败时切换到网络爬虫
data = dm.get_stock_data('000001.SZ', '2023-01-01', '2023-12-31')
```

### 2. 智能缓存系统
```python
# 首次获取会缓存，第二次直接从缓存读取
data1 = dm.get_stock_data('000001.SZ', start_date, end_date)  # 从网络获取
data2 = dm.get_stock_data('000001.SZ', start_date, end_date)  # 从缓存获取
```

### 3. 并发数据获取
```python
# 同时获取多只股票，自动并发处理
stocks = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']
all_data = dm.get_multiple_stocks_data(stocks, start_date, end_date)
```

### 4. 回测专用接口
```python
# 为回测优化的数据接口
bt = BacktestingDataInterface()
price_matrix = bt.get_price_matrix(stocks, start_date, end_date)
returns_matrix = bt.get_returns_matrix(stocks, start_date, end_date)
```

### 5. 数据质量控制
```python
# 自动验证和清洗数据
is_valid, errors = validator.validate_price_data(data, stock_code)
cleaned_data = validator.clean_data(data, stock_code)
```

## 🎨 代码质量特点

### 整洁的代码结构
- **单一职责**：每个类和函数都有明确的职责
- **模块化设计**：功能模块清晰分离
- **一致的命名**：使用有意义的变量和函数名
- **适当的抽象**：合理的抽象层次

### 完整的中文注释
```python
def get_stock_data(self, stock_code: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
    """
    获取股票数据，支持智能数据源选择
    
    参数:
        stock_code: 股票代码 (如 '000001.SZ')
        start_date: 开始日期
        end_date: 结束日期
        
    返回:
        pd.DataFrame: 股票数据，失败时返回None
    """
```

### 强大的错误处理
```python
try:
    data = self._fetch_from_providers('stock', normalized_code, **kwargs)
    if data is not None:
        # 验证数据质量
        is_valid, errors = self.validator.validate_price_data(data, normalized_code)
        if not is_valid:
            self.logger.warning(f"数据验证失败 {normalized_code}: {errors}")
            data = self.validator.clean_data(data, normalized_code)
        return data
except Exception as e:
    self.logger.error(f"获取股票数据时出错 {stock_code}: {e}")
    return None
```

## 📊 性能优化

### 1. 智能缓存策略
- **多层缓存**：内存缓存 + 磁盘缓存 + 数据库
- **TTL管理**：可配置的过期时间
- **LRU淘汰**：自动清理旧数据

### 2. 并发处理
- **线程池**：并发获取多只股票数据
- **限流控制**：避免API限制
- **资源管理**：自动清理资源

### 3. 数据库优化
- **索引优化**：关键字段建立索引
- **批量操作**：减少数据库访问次数
- **连接池**：复用数据库连接

## 🛠️ 使用方式

### 快速开始
```bash
# 1. 安装框架
python setup_framework_cn.py

# 2. 快速测试
python quick_start_cn.py

# 3. 运行示例
python examples/basic_usage_cn.py
```

### 基础使用
```python
from data_acquisition import DataManager

# 初始化
dm = DataManager()

# 获取数据
data = dm.get_stock_data('000001.SZ', '2023-01-01', '2023-12-31')

# 清理资源
dm.cleanup()
```

### 高级使用
```python
from data_acquisition import BacktestingDataInterface
from data_acquisition.backtesting_interface import DataPreprocessor

# 回测接口
bt = BacktestingDataInterface()
preprocessor = DataPreprocessor()

# 获取价格矩阵
price_matrix = bt.get_price_matrix(stocks, start_date, end_date)

# 添加技术指标
data_with_indicators = preprocessor.calculate_technical_indicators(data)
```

## 🔧 配置管理

### 环境变量配置
```bash
export CACHE_ENABLED="true"
export CACHE_TTL_DAYS="7"
export AKSHARE_RATE_LIMIT="0.5"
export LOG_LEVEL="INFO"
```

### 代码配置
```python
from data_acquisition.config import Config

config = Config()
config.CACHE_TTL_DAYS = 1
config.LOG_LEVEL = 'DEBUG'

dm = DataManager(config)
```

## 📈 应用场景

### 1. 量化策略研究
- 因子挖掘和分析
- 策略回测和优化
- 风险模型构建

### 2. 投资组合管理
- 资产配置优化
- 风险控制
- 业绩归因分析

### 3. 学术研究
- 市场微观结构
- 行为金融学
- 风险管理

## 🎯 技术亮点

### 1. 企业级架构
- **可扩展性**：易于添加新数据源
- **可维护性**：清晰的模块划分
- **可测试性**：完整的测试覆盖

### 2. 生产就绪
- **错误处理**：全面的异常处理
- **日志系统**：详细的操作日志
- **资源管理**：自动资源清理

### 3. 用户友好
- **中文文档**：完整的中文说明
- **示例丰富**：多种使用场景
- **快速上手**：一键安装和测试

## 🚀 快速体验

```bash
# 克隆或下载项目后
cd A股数据采集框架

# 一键安装
python setup_framework_cn.py

# 快速体验
python quick_start_cn.py

# 查看示例
python examples/basic_usage_cn.py
python examples/strategy_backtest_cn.py
```

## 📞 技术支持

- 📖 **完整文档**：README_CN.md
- 🔧 **测试工具**：test_framework_cn.py
- 💡 **使用示例**：examples/ 目录
- ⚙️ **配置说明**：config/settings.py

---

**这是一个专业、整洁、高效的A股数据采集框架，为您的量化投资提供坚实的数据基础！**

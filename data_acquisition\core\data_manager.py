"""
Central data manager for A-Share data acquisition framework
"""

import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime, date, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from .akshare_provider import AkshareProvider
from .web_scraper import WebScraper
from ..storage.database import DatabaseManager
from ..storage.cache_manager import CacheManager
from ..utils.data_validator import DataValidator
from ..utils.stock_codes import normalize_stock_code, validate_stock_codes
from ..utils.rate_limiter import MultiSourceRateLimiter
from ..config.settings import Config
from ..utils.logger import get_data_manager_logger

class DataManager:
    """Central coordinator for all data acquisition operations"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize data manager
        
        Args:
            config: Configuration instance
        """
        self.config = config or Config()
        self.logger = get_data_manager_logger(config)
        
        # Initialize components
        self.database = DatabaseManager(config)
        self.cache = CacheManager(config)
        self.validator = DataValidator(config)
        self.rate_limiter = MultiSourceRateLimiter(config)
        
        # Initialize data providers
        self.providers = {}
        self._init_providers()
        
        # Thread lock for thread-safe operations
        self._lock = threading.Lock()
        
        self.logger.info("Data manager initialized successfully")
    
    def _init_providers(self):
        """Initialize data providers"""
        try:
            # Primary provider: Akshare
            akshare_provider = AkshareProvider(self.config)
            if akshare_provider.is_available():
                self.providers['akshare'] = akshare_provider
                self.logger.info("Akshare provider initialized")
            else:
                self.logger.warning("Akshare provider not available")
            
            # Fallback provider: Web scraper
            web_scraper = WebScraper(self.config)
            if web_scraper.is_available():
                self.providers['web_scraper'] = web_scraper
                self.logger.info("Web scraper provider initialized")
            else:
                self.logger.warning("Web scraper provider not available")
            
            if not self.providers:
                self.logger.error("No data providers available!")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize providers: {e}")
    
    def get_stock_data(self, 
                      stock_code: str,
                      start_date: Union[str, date, datetime],
                      end_date: Union[str, date, datetime],
                      use_cache: bool = True,
                      force_update: bool = False) -> Optional[pd.DataFrame]:
        """
        Get stock data with intelligent source selection
        
        Args:
            stock_code: Stock code (e.g., '000001.SZ')
            start_date: Start date
            end_date: End date
            use_cache: Whether to use cached data
            force_update: Force update from source (ignore cache)
            
        Returns:
            pd.DataFrame: Stock data or None if failed
        """
        try:
            # Normalize stock code
            normalized_code = normalize_stock_code(stock_code)
            if not normalized_code:
                self.logger.error(f"Invalid stock code: {stock_code}")
                return None
            
            # Convert dates
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date).date()
            if isinstance(end_date, str):
                end_date = pd.to_datetime(end_date).date()
            
            # Check cache first (if enabled and not forcing update)
            if use_cache and not force_update:
                cached_data = self.cache.get_cached_data(
                    'stock', normalized_code, 
                    start_date=start_date, end_date=end_date
                )
                if cached_data is not None:
                    self.logger.debug(f"Using cached data for {normalized_code}")
                    return cached_data
            
            # Check database for existing data
            if not force_update:
                db_data = self.database.get_stock_data(normalized_code, start_date, end_date)
                if db_data is not None and not db_data.empty:
                    # Check if we have sufficient coverage
                    coverage = self._check_data_coverage(db_data, start_date, end_date)
                    if coverage['is_sufficient']:
                        self.logger.debug(f"Using database data for {normalized_code}")
                        
                        # Cache the data
                        if use_cache:
                            self.cache.cache_data(
                                db_data, 'stock', normalized_code,
                                start_date=start_date, end_date=end_date
                            )
                        
                        return db_data
            
            # Fetch from providers
            data = self._fetch_from_providers('stock', normalized_code, 
                                            start_date=start_date, end_date=end_date)
            
            if data is not None and not data.empty:
                # Validate data
                is_valid, errors = self.validator.validate_price_data(data, normalized_code)
                if not is_valid:
                    self.logger.warning(f"Data validation failed for {normalized_code}: {errors}")
                    # Clean the data
                    data = self.validator.clean_data(data, normalized_code)
                
                # Save to database
                self.database.save_stock_data(normalized_code, data, 'data_manager')
                
                # Cache the data
                if use_cache:
                    self.cache.cache_data(
                        data, 'stock', normalized_code,
                        start_date=start_date, end_date=end_date
                    )
                
                self.logger.info(f"Successfully retrieved {len(data)} records for {normalized_code}")
                return data
            
            self.logger.error(f"Failed to get data for {normalized_code}")
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting stock data for {stock_code}: {e}")
            return None
    
    def get_multiple_stocks_data(self,
                               stock_codes: List[str],
                               start_date: Union[str, date, datetime],
                               end_date: Union[str, date, datetime],
                               max_workers: int = 5,
                               use_cache: bool = True) -> Dict[str, pd.DataFrame]:
        """
        Get data for multiple stocks concurrently
        
        Args:
            stock_codes: List of stock codes
            start_date: Start date
            end_date: End date
            max_workers: Maximum number of concurrent workers
            use_cache: Whether to use cached data
            
        Returns:
            Dict[str, pd.DataFrame]: Dictionary of stock data
        """
        # Validate stock codes
        valid_codes, invalid_codes = validate_stock_codes(stock_codes)
        
        if invalid_codes:
            self.logger.warning(f"Invalid stock codes: {invalid_codes}")
        
        if not valid_codes:
            self.logger.error("No valid stock codes provided")
            return {}
        
        results = {}
        
        # Use ThreadPoolExecutor for concurrent data fetching
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit tasks
            future_to_code = {
                executor.submit(
                    self.get_stock_data, code, start_date, end_date, use_cache
                ): code for code in valid_codes
            }
            
            # Collect results
            for future in as_completed(future_to_code):
                stock_code = future_to_code[future]
                try:
                    data = future.result()
                    if data is not None:
                        results[stock_code] = data
                except Exception as e:
                    self.logger.error(f"Failed to get data for {stock_code}: {e}")
        
        self.logger.info(f"Retrieved data for {len(results)}/{len(valid_codes)} stocks")
        return results
    
    def get_stock_info(self, stock_code: str, use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """
        Get stock information
        
        Args:
            stock_code: Stock code
            use_cache: Whether to use cached data
            
        Returns:
            Dict: Stock information or None if failed
        """
        try:
            normalized_code = normalize_stock_code(stock_code)
            if not normalized_code:
                return None
            
            # Check database first
            info = self.database.get_stock_info(normalized_code)
            if info:
                return info
            
            # Fetch from providers
            info = self._fetch_from_providers('info', normalized_code)
            
            if info:
                # Save to database
                self.database.save_stock_info(normalized_code, info)
                return info
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting stock info for {stock_code}: {e}")
            return None
    
    def get_index_data(self,
                      index_code: str,
                      start_date: Union[str, date, datetime],
                      end_date: Union[str, date, datetime],
                      use_cache: bool = True) -> Optional[pd.DataFrame]:
        """
        Get market index data
        
        Args:
            index_code: Index code
            start_date: Start date
            end_date: End date
            use_cache: Whether to use cached data
            
        Returns:
            pd.DataFrame: Index data or None if failed
        """
        try:
            # Convert dates
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date).date()
            if isinstance(end_date, str):
                end_date = pd.to_datetime(end_date).date()
            
            # Check cache first
            if use_cache:
                cached_data = self.cache.get_cached_data(
                    'index', index_code,
                    start_date=start_date, end_date=end_date
                )
                if cached_data is not None:
                    return cached_data
            
            # Fetch from providers
            data = self._fetch_from_providers('index', index_code,
                                            start_date=start_date, end_date=end_date)
            
            if data is not None and not data.empty:
                # Validate data
                is_valid, errors = self.validator.validate_index_data(data, index_code)
                if not is_valid:
                    self.logger.warning(f"Index data validation failed for {index_code}: {errors}")
                
                # Cache the data
                if use_cache:
                    self.cache.cache_data(
                        data, 'index', index_code,
                        start_date=start_date, end_date=end_date
                    )
                
                return data
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting index data for {index_code}: {e}")
            return None
    
    def _fetch_from_providers(self, data_type: str, identifier: str, **kwargs) -> Optional[Union[pd.DataFrame, Dict]]:
        """
        Fetch data from available providers in priority order
        
        Args:
            data_type: Type of data ('stock', 'index', 'info', 'financial')
            identifier: Stock code or identifier
            **kwargs: Additional parameters
            
        Returns:
            Data from providers or None if all failed
        """
        # Provider priority order
        provider_order = ['akshare', 'web_scraper']
        
        for provider_name in provider_order:
            if provider_name not in self.providers:
                continue
            
            provider = self.providers[provider_name]
            
            try:
                if data_type == 'stock':
                    data = provider.get_stock_data(identifier, **kwargs)
                elif data_type == 'index':
                    data = provider.get_index_data(identifier, **kwargs)
                elif data_type == 'info':
                    data = provider.get_stock_info(identifier)
                elif data_type == 'financial':
                    data = provider.get_financial_data(identifier, **kwargs)
                else:
                    continue
                
                if data is not None and (isinstance(data, dict) or not data.empty):
                    self.logger.debug(f"Successfully fetched {data_type} data for {identifier} from {provider_name}")
                    return data
                    
            except Exception as e:
                self.logger.warning(f"Provider {provider_name} failed for {identifier}: {e}")
                continue
        
        self.logger.error(f"All providers failed for {data_type}:{identifier}")
        return None
    
    def _check_data_coverage(self, data: pd.DataFrame, start_date: date, end_date: date) -> Dict[str, Any]:
        """
        Check if existing data has sufficient coverage for the requested period
        
        Args:
            data: Existing data
            start_date: Requested start date
            end_date: Requested end date
            
        Returns:
            Dict: Coverage analysis
        """
        if data.empty:
            return {'is_sufficient': False, 'reason': 'No data'}
        
        data_start = data.index.min().date()
        data_end = data.index.max().date()
        
        # Check if data covers the requested period
        covers_start = data_start <= start_date
        covers_end = data_end >= end_date
        
        # Check for significant gaps
        expected_days = (end_date - start_date).days
        actual_days = len(data)
        coverage_ratio = actual_days / max(expected_days, 1)
        
        is_sufficient = covers_start and covers_end and coverage_ratio > 0.7  # 70% coverage threshold
        
        return {
            'is_sufficient': is_sufficient,
            'data_start': data_start,
            'data_end': data_end,
            'covers_start': covers_start,
            'covers_end': covers_end,
            'coverage_ratio': coverage_ratio,
            'actual_days': actual_days,
            'expected_days': expected_days
        }
    
    def get_available_stocks(self, exchange: Optional[str] = None) -> List[str]:
        """
        Get list of available stocks
        
        Args:
            exchange: Exchange filter ('SH', 'SZ', or None for all)
            
        Returns:
            List[str]: List of stock codes
        """
        # First try database
        db_stocks = self.database.get_available_stocks(exchange)
        if db_stocks:
            return db_stocks
        
        # Fallback to providers
        for provider_name, provider in self.providers.items():
            try:
                stock_list = provider.get_stock_list(exchange)
                if stock_list is not None and not stock_list.empty:
                    # Extract stock codes (assuming 'code' column exists)
                    if 'code' in stock_list.columns:
                        codes = stock_list['code'].tolist()
                        # Normalize codes
                        normalized_codes = [normalize_stock_code(code) for code in codes]
                        return [code for code in normalized_codes if code]
            except Exception as e:
                self.logger.warning(f"Failed to get stock list from {provider_name}: {e}")
        
        return []
    
    def update_stock_data(self, stock_code: str, days_back: int = 30) -> bool:
        """
        Update stock data for recent period
        
        Args:
            stock_code: Stock code
            days_back: Number of days to update
            
        Returns:
            bool: True if successful
        """
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=days_back)
            
            data = self.get_stock_data(stock_code, start_date, end_date, 
                                     use_cache=False, force_update=True)
            
            return data is not None and not data.empty
            
        except Exception as e:
            self.logger.error(f"Failed to update data for {stock_code}: {e}")
            return False
    
    def get_data_stats(self) -> Dict[str, Any]:
        """
        Get data acquisition statistics
        
        Returns:
            Dict: Statistics
        """
        try:
            stats = {
                'providers': {name: provider.get_provider_info() 
                            for name, provider in self.providers.items()},
                'cache': self.cache.get_cache_stats(),
                'available_stocks_count': len(self.get_available_stocks())
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get data stats: {e}")
            return {'error': str(e)}
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            # Cleanup cache
            self.cache.cleanup_expired_cache()
            
            # Close database connections
            self.database.close()
            
            # Close web scraper sessions
            if 'web_scraper' in self.providers:
                self.providers['web_scraper'].close()
            
            self.logger.info("Data manager cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TICK数据管理器
用于创业板动态因子策略的高频数据处理
"""

import os
import sqlite3
import pandas as pd
import akshare as ak
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from pathlib import Path
import time
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from tqdm import tqdm


class TickDataManager:
    """TICK数据管理器"""
    
    def __init__(self, db_path: str = "data/tick_data.db"):
        """
        初始化TICK数据管理器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # 初始化数据库
        self._init_database()
        
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建TICK数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tick_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    date TEXT NOT NULL,
                    time TEXT NOT NULL,
                    price REAL NOT NULL,
                    volume INTEGER NOT NULL,
                    amount REAL NOT NULL,
                    bid_price REAL,
                    ask_price REAL,
                    bid_volume INTEGER,
                    ask_volume INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, date, time)
                )
            """)
            
            # 创建索引
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_tick_stock_date 
                ON tick_data(stock_code, date)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_tick_time 
                ON tick_data(date, time)
            """)
            
            # 创建数据下载记录表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS download_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    date TEXT NOT NULL,
                    status TEXT NOT NULL,  -- 'success', 'failed', 'partial'
                    record_count INTEGER DEFAULT 0,
                    error_message TEXT,
                    download_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, date)
                )
            """)
            
            conn.commit()
            conn.close()
            
            self.logger.info("TICK数据库初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def download_tick_data(self, stock_code: str, date: str, force_update: bool = False) -> bool:
        """
        下载指定股票的TICK数据
        
        Args:
            stock_code: 股票代码
            date: 日期 (YYYY-MM-DD)
            force_update: 是否强制更新
            
        Returns:
            bool: 下载是否成功
        """
        try:
            # 检查是否已经下载过
            if not force_update and self._is_data_exists(stock_code, date):
                self.logger.debug(f"TICK数据已存在: {stock_code} {date}")
                return True
            
            # 转换股票代码格式
            symbol = stock_code.replace('.SZ', '').replace('.SH', '')
            
            # 尝试获取TICK数据
            try:
                # 使用akshare获取分时数据（作为TICK数据的替代）
                tick_data = ak.stock_zh_a_minute(symbol=symbol, period='1', adjust='')
                
                if tick_data.empty:
                    self.logger.warning(f"未获取到TICK数据: {stock_code} {date}")
                    self._record_download_status(stock_code, date, 'failed', 0, "数据为空")
                    return False
                
                # 过滤指定日期的数据
                tick_data['date'] = pd.to_datetime(tick_data.index).strftime('%Y-%m-%d')
                tick_data = tick_data[tick_data['date'] == date]
                
                if tick_data.empty:
                    self.logger.warning(f"指定日期无TICK数据: {stock_code} {date}")
                    self._record_download_status(stock_code, date, 'failed', 0, "指定日期无数据")
                    return False
                
                # 保存到数据库
                saved_count = self._save_tick_data(stock_code, date, tick_data)
                
                if saved_count > 0:
                    self._record_download_status(stock_code, date, 'success', saved_count)
                    self.logger.info(f"TICK数据下载成功: {stock_code} {date}, 记录数: {saved_count}")
                    return True
                else:
                    self._record_download_status(stock_code, date, 'failed', 0, "保存失败")
                    return False
                    
            except Exception as e:
                error_msg = f"获取TICK数据失败: {e}"
                self.logger.error(f"{stock_code} {date} - {error_msg}")
                self._record_download_status(stock_code, date, 'failed', 0, error_msg)
                return False
                
        except Exception as e:
            self.logger.error(f"下载TICK数据异常: {stock_code} {date} - {e}")
            return False
    
    def _is_data_exists(self, stock_code: str, date: str) -> bool:
        """检查数据是否已存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT COUNT(*) FROM tick_data 
                WHERE stock_code = ? AND date = ?
            """, (stock_code, date))
            
            count = cursor.fetchone()[0]
            conn.close()
            
            return count > 0
            
        except Exception:
            return False
    
    def _save_tick_data(self, stock_code: str, date: str, tick_data: pd.DataFrame) -> int:
        """保存TICK数据到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 先删除已存在的数据
            cursor.execute("""
                DELETE FROM tick_data 
                WHERE stock_code = ? AND date = ?
            """, (stock_code, date))
            
            saved_count = 0
            
            # 插入新数据
            for idx, row in tick_data.iterrows():
                try:
                    time_str = pd.to_datetime(idx).strftime('%H:%M:%S')
                    
                    cursor.execute("""
                        INSERT OR REPLACE INTO tick_data 
                        (stock_code, date, time, price, volume, amount)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        stock_code,
                        date,
                        time_str,
                        float(row.get('close', 0)),
                        int(row.get('volume', 0)),
                        float(row.get('volume', 0) * row.get('close', 0))
                    ))
                    
                    saved_count += 1
                    
                except Exception as e:
                    self.logger.warning(f"保存单条TICK数据失败: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            return saved_count
            
        except Exception as e:
            self.logger.error(f"保存TICK数据失败: {e}")
            return 0
    
    def _record_download_status(self, stock_code: str, date: str, status: str, 
                              record_count: int = 0, error_message: str = None):
        """记录下载状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO download_records 
                (stock_code, date, status, record_count, error_message)
                VALUES (?, ?, ?, ?, ?)
            """, (stock_code, date, status, record_count, error_message))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"记录下载状态失败: {e}")
    
    def batch_download_tick_data(self, stock_codes: List[str], dates: List[str], 
                                max_workers: int = 5) -> Dict[str, int]:
        """
        批量下载TICK数据
        
        Args:
            stock_codes: 股票代码列表
            dates: 日期列表
            max_workers: 最大并发数
            
        Returns:
            Dict[str, int]: 下载统计结果
        """
        results = {'success': 0, 'failed': 0, 'total': 0}
        
        # 生成下载任务
        tasks = []
        for stock_code in stock_codes:
            for date in dates:
                tasks.append((stock_code, date))
        
        results['total'] = len(tasks)
        
        self.logger.info(f"开始批量下载TICK数据，任务数: {len(tasks)}")
        
        # 使用线程池并行下载
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_task = {
                executor.submit(self.download_tick_data, stock_code, date): (stock_code, date)
                for stock_code, date in tasks
            }
            
            # 处理结果
            with tqdm(total=len(tasks), desc="下载TICK数据") as pbar:
                for future in as_completed(future_to_task):
                    stock_code, date = future_to_task[future]
                    try:
                        success = future.result()
                        if success:
                            results['success'] += 1
                        else:
                            results['failed'] += 1
                    except Exception as e:
                        self.logger.error(f"下载任务异常: {stock_code} {date} - {e}")
                        results['failed'] += 1
                    
                    pbar.update(1)
        
        self.logger.info(f"批量下载完成 - 成功: {results['success']}, 失败: {results['failed']}")
        return results
    
    def get_tick_data(self, stock_code: str, date: str) -> Optional[pd.DataFrame]:
        """
        获取TICK数据
        
        Args:
            stock_code: 股票代码
            date: 日期
            
        Returns:
            pd.DataFrame: TICK数据
        """
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = """
                SELECT date, time, price, volume, amount
                FROM tick_data 
                WHERE stock_code = ? AND date = ?
                ORDER BY time
            """
            
            df = pd.read_sql_query(query, conn, params=(stock_code, date))
            conn.close()
            
            if df.empty:
                return None
            
            # 创建datetime索引
            df['datetime'] = pd.to_datetime(df['date'] + ' ' + df['time'])
            df.set_index('datetime', inplace=True)
            df.drop(['date', 'time'], axis=1, inplace=True)
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取TICK数据失败: {stock_code} {date} - {e}")
            return None
    
    def get_download_statistics(self) -> Dict[str, int]:
        """获取下载统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计下载记录
            cursor.execute("""
                SELECT status, COUNT(*) as count
                FROM download_records
                GROUP BY status
            """)
            
            stats = dict(cursor.fetchall())
            
            # 统计总记录数
            cursor.execute("SELECT COUNT(*) FROM tick_data")
            total_records = cursor.fetchone()[0]
            
            conn.close()
            
            stats['total_records'] = total_records
            return stats
            
        except Exception as e:
            self.logger.error(f"获取下载统计失败: {e}")
            return {}

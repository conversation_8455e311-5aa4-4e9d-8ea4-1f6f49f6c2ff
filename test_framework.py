"""
Simple test script to verify the A-Share Data Acquisition Framework
"""

import sys
import os
from datetime import datetime, date, timedelta

def test_imports():
    """Test if all modules can be imported"""
    print("Testing imports...")
    
    try:
        # Test core imports
        from data_acquisition import DataManager, BacktestingDataInterface
        from data_acquisition.config import Config
        from data_acquisition.utils import normalize_stock_code, validate_stock_codes
        print("✓ Core imports successful")
        
        # Test individual modules
        from data_acquisition.core import AkshareProvider, WebScraper
        from data_acquisition.storage import DatabaseManager, CacheManager
        from data_acquisition.utils import DataValidator
        print("✓ Module imports successful")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_configuration():
    """Test configuration system"""
    print("\nTesting configuration...")
    
    try:
        from data_acquisition.config import Config, get_config
        
        # Test default config
        config = Config()
        print(f"✓ Default config created")
        print(f"  Cache enabled: {config.CACHE_ENABLED}")
        print(f"  Database URL: {config.DATABASE_URL}")
        
        # Test config factory
        dev_config = get_config('dev')
        prod_config = get_config('prod')
        print(f"✓ Config factory working")
        
        # Test directory creation
        config.create_directories()
        print(f"✓ Directories created")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False

def test_stock_codes():
    """Test stock code utilities"""
    print("\nTesting stock code utilities...")
    
    try:
        from data_acquisition.utils import (
            normalize_stock_code, 
            validate_stock_codes,
            StockCodeValidator
        )
        
        # Test normalization
        test_codes = ['000001', '600000.SH', '300001.SZ']
        for code in test_codes:
            normalized = normalize_stock_code(code)
            print(f"  {code} -> {normalized}")
        
        # Test validation
        valid, invalid = validate_stock_codes(['000001', '600000.SH', 'INVALID'])
        print(f"✓ Valid codes: {valid}")
        print(f"✓ Invalid codes: {invalid}")
        
        # Test validator
        validator = StockCodeValidator()
        print(f"✓ Code validator working")
        
        return True
        
    except Exception as e:
        print(f"✗ Stock code error: {e}")
        return False

def test_data_manager_init():
    """Test data manager initialization"""
    print("\nTesting data manager initialization...")
    
    try:
        from data_acquisition import DataManager
        
        # Initialize with default config
        dm = DataManager()
        print(f"✓ Data manager initialized")
        
        # Check providers
        print(f"  Available providers: {list(dm.providers.keys())}")
        
        # Check components
        print(f"  Database: {dm.database is not None}")
        print(f"  Cache: {dm.cache is not None}")
        print(f"  Validator: {dm.validator is not None}")
        
        # Get stats
        stats = dm.get_data_stats()
        print(f"✓ Data stats retrieved")
        
        # Cleanup
        dm.cleanup()
        print(f"✓ Cleanup successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Data manager error: {e}")
        return False

def test_backtesting_interface():
    """Test backtesting interface"""
    print("\nTesting backtesting interface...")
    
    try:
        from data_acquisition import BacktestingDataInterface
        
        # Initialize interface
        bt = BacktestingDataInterface()
        print(f"✓ Backtesting interface initialized")
        
        # Test validation
        valid, invalid = bt.validate_universe(['000001.SZ', '600000.SH', 'INVALID'])
        print(f"✓ Universe validation: {len(valid)} valid, {len(invalid)} invalid")
        
        # Test trading calendar
        start_date = date.today() - timedelta(days=30)
        end_date = date.today()
        calendar = bt.get_trading_calendar(start_date, end_date)
        print(f"✓ Trading calendar: {len(calendar)} trading days")
        
        # Cleanup
        bt.cleanup()
        print(f"✓ Cleanup successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Backtesting interface error: {e}")
        return False

def test_database():
    """Test database functionality"""
    print("\nTesting database...")
    
    try:
        from data_acquisition.storage import DatabaseManager
        import pandas as pd
        
        # Initialize database
        db = DatabaseManager()
        print(f"✓ Database initialized")
        
        # Test stock info
        test_info = {
            'name': 'Test Stock',
            'exchange': 'SZ',
            'sector': 'Technology'
        }
        
        success = db.save_stock_info('TEST.SZ', test_info)
        print(f"✓ Stock info save: {success}")
        
        retrieved_info = db.get_stock_info('TEST.SZ')
        print(f"✓ Stock info retrieve: {retrieved_info is not None}")
        
        # Test data coverage
        coverage = db.get_data_coverage('TEST.SZ')
        print(f"✓ Data coverage: {coverage}")
        
        return True
        
    except Exception as e:
        print(f"✗ Database error: {e}")
        return False

def test_cache():
    """Test cache functionality"""
    print("\nTesting cache...")
    
    try:
        from data_acquisition.storage import CacheManager
        import pandas as pd
        
        # Initialize cache
        cache = CacheManager()
        print(f"✓ Cache initialized")
        
        # Test caching
        test_data = pd.DataFrame({
            'close': [100, 101, 102, 103],
            'volume': [1000, 1100, 1200, 1300]
        })
        
        success = cache.cache_data(test_data, 'stock', 'TEST.SZ', 
                                 start_date=date.today(), end_date=date.today())
        print(f"✓ Data caching: {success}")
        
        # Test retrieval
        cached_data = cache.get_cached_data('stock', 'TEST.SZ',
                                          start_date=date.today(), end_date=date.today())
        print(f"✓ Data retrieval: {cached_data is not None}")
        
        # Test stats
        stats = cache.get_cache_stats()
        print(f"✓ Cache stats: {stats.get('total_entries', 0)} entries")
        
        return True
        
    except Exception as e:
        print(f"✗ Cache error: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("A-Share Data Acquisition Framework - Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_configuration,
        test_stock_codes,
        test_data_manager_init,
        test_backtesting_interface,
        test_database,
        test_cache
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Framework is ready to use.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)

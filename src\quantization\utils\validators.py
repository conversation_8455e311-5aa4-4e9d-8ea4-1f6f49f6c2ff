#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证模块

提供各种数据验证功能。
"""

import re
from typing import Union, Optional
from datetime import datetime, date


def validate_stock_code(stock_code: str) -> bool:
    """
    验证股票代码格式
    
    Args:
        stock_code: 股票代码
        
    Returns:
        是否为有效的股票代码
    """
    if not stock_code or not isinstance(stock_code, str):
        return False
    
    # 移除空格和转换为大写
    code = stock_code.strip().upper()
    
    # A股股票代码格式验证
    patterns = [
        r'^[0-9]{6}$',  # 6位数字
        r'^[0-9]{6}\.(SZ|SH)$',  # 6位数字.SZ/SH
    ]
    
    return any(re.match(pattern, code) for pattern in patterns)


def validate_date_format(date_str: str) -> bool:
    """
    验证日期格式
    
    Args:
        date_str: 日期字符串
        
    Returns:
        是否为有效的日期格式
    """
    if not date_str or not isinstance(date_str, str):
        return False
    
    formats = ['%Y-%m-%d', '%Y%m%d', '%Y/%m/%d']
    
    for fmt in formats:
        try:
            datetime.strptime(date_str, fmt)
            return True
        except ValueError:
            continue
    
    return False


def validate_numeric_range(
    value: Union[int, float], 
    min_val: Optional[Union[int, float]] = None,
    max_val: Optional[Union[int, float]] = None
) -> bool:
    """
    验证数值范围
    
    Args:
        value: 要验证的数值
        min_val: 最小值（可选）
        max_val: 最大值（可选）
        
    Returns:
        是否在有效范围内
    """
    if not isinstance(value, (int, float)):
        return False
    
    if min_val is not None and value < min_val:
        return False
    
    if max_val is not None and value > max_val:
        return False
    
    return True

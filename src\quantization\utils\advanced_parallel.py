#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级并行计算架构

提供更细粒度的并行化和智能负载均衡，支持异步处理和动态资源调度。
专门针对量化交易中的大规模数据处理进行优化。
"""

import asyncio
import threading
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from typing import Dict, List, Optional, Any, Callable, Union, Tuple
import numpy as np
import pandas as pd
import time
import psutil
import queue
from dataclasses import dataclass, field
from enum import Enum
import logging
from functools import wraps
import weakref

from quantization.utils.logger import get_logger


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


class WorkerType(Enum):
    """工作器类型"""
    THREAD = "thread"
    PROCESS = "process"
    ASYNC = "async"


@dataclass
class Task:
    """任务定义"""
    task_id: str
    func: Callable
    args: Tuple = field(default_factory=tuple)
    kwargs: Dict[str, Any] = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    worker_type: WorkerType = WorkerType.THREAD
    timeout: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3
    callback: Optional[Callable] = None
    created_time: float = field(default_factory=time.time)


@dataclass
class WorkerStats:
    """工作器统计信息"""
    worker_id: str
    worker_type: WorkerType
    tasks_completed: int = 0
    tasks_failed: int = 0
    total_execution_time: float = 0.0
    average_execution_time: float = 0.0
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    is_busy: bool = False
    last_task_time: Optional[float] = None


class LoadBalancer:
    """
    智能负载均衡器
    
    根据工作器性能和系统资源动态分配任务。
    """
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.worker_stats: Dict[str, WorkerStats] = {}
        self.system_monitor = SystemResourceMonitor()
        self._lock = threading.RLock()
    
    def register_worker(self, worker_id: str, worker_type: WorkerType):
        """注册工作器"""
        with self._lock:
            self.worker_stats[worker_id] = WorkerStats(
                worker_id=worker_id,
                worker_type=worker_type
            )
    
    def unregister_worker(self, worker_id: str):
        """注销工作器"""
        with self._lock:
            self.worker_stats.pop(worker_id, None)
    
    def select_best_worker(self, task: Task) -> Optional[str]:
        """
        选择最佳工作器
        
        基于工作器负载、任务类型和系统资源选择最优工作器。
        """
        with self._lock:
            available_workers = [
                (worker_id, stats) for worker_id, stats in self.worker_stats.items()
                if stats.worker_type == task.worker_type and not stats.is_busy
            ]
            
            if not available_workers:
                return None
            
            # 计算工作器得分
            best_worker = None
            best_score = float('inf')
            
            for worker_id, stats in available_workers:
                score = self._calculate_worker_score(stats, task)
                if score < best_score:
                    best_score = score
                    best_worker = worker_id
            
            return best_worker
    
    def _calculate_worker_score(self, stats: WorkerStats, task: Task) -> float:
        """
        计算工作器得分
        
        得分越低表示工作器越适合执行任务。
        """
        # 基础得分：平均执行时间
        base_score = stats.average_execution_time
        
        # CPU使用率惩罚
        cpu_penalty = stats.cpu_usage * 0.1
        
        # 内存使用率惩罚
        memory_penalty = stats.memory_usage * 0.05
        
        # 任务优先级调整
        priority_adjustment = (5 - task.priority.value) * 0.1
        
        # 失败率惩罚
        if stats.tasks_completed > 0:
            failure_rate = stats.tasks_failed / (stats.tasks_completed + stats.tasks_failed)
            failure_penalty = failure_rate * 0.2
        else:
            failure_penalty = 0
        
        total_score = base_score + cpu_penalty + memory_penalty + priority_adjustment + failure_penalty
        return max(0.1, total_score)  # 确保得分为正数
    
    def update_worker_stats(self, worker_id: str, execution_time: float, 
                          success: bool, cpu_usage: float = 0.0, 
                          memory_usage: float = 0.0):
        """更新工作器统计信息"""
        with self._lock:
            if worker_id not in self.worker_stats:
                return
            
            stats = self.worker_stats[worker_id]
            
            if success:
                stats.tasks_completed += 1
            else:
                stats.tasks_failed += 1
            
            stats.total_execution_time += execution_time
            total_tasks = stats.tasks_completed + stats.tasks_failed
            stats.average_execution_time = stats.total_execution_time / max(1, total_tasks)
            
            stats.cpu_usage = cpu_usage
            stats.memory_usage = memory_usage
            stats.last_task_time = time.time()
    
    def get_load_report(self) -> Dict[str, Any]:
        """获取负载报告"""
        with self._lock:
            report = {
                'total_workers': len(self.worker_stats),
                'busy_workers': sum(1 for stats in self.worker_stats.values() if stats.is_busy),
                'worker_details': {}
            }
            
            for worker_id, stats in self.worker_stats.items():
                report['worker_details'][worker_id] = {
                    'type': stats.worker_type.value,
                    'completed_tasks': stats.tasks_completed,
                    'failed_tasks': stats.tasks_failed,
                    'average_time': stats.average_execution_time,
                    'cpu_usage': stats.cpu_usage,
                    'memory_usage': stats.memory_usage,
                    'is_busy': stats.is_busy
                }
            
            return report


class SystemResourceMonitor:
    """系统资源监控器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self._monitoring = False
        self._monitor_thread = None
        self._stats = {
            'cpu_percent': 0.0,
            'memory_percent': 0.0,
            'disk_usage': 0.0,
            'network_io': 0.0
        }
        self._lock = threading.RLock()
    
    def start_monitoring(self, interval: float = 1.0):
        """开始监控系统资源"""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self._monitor_thread.start()
        self.logger.info("系统资源监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=2.0)
        self.logger.info("系统资源监控已停止")
    
    def _monitor_loop(self, interval: float):
        """监控循环"""
        import os
        import platform

        # 根据操作系统确定磁盘路径
        if platform.system() == 'Windows':
            disk_path = 'C:\\'
        else:
            disk_path = '/'

        while self._monitoring:
            try:
                with self._lock:
                    self._stats['cpu_percent'] = psutil.cpu_percent()
                    self._stats['memory_percent'] = psutil.virtual_memory().percent

                    # 磁盘使用率 - 兼容Windows和Linux
                    try:
                        self._stats['disk_usage'] = psutil.disk_usage(disk_path).percent
                    except Exception:
                        # 如果获取磁盘使用率失败，设为0
                        self._stats['disk_usage'] = 0.0

                    # 网络IO统计
                    try:
                        net_io = psutil.net_io_counters()
                        if net_io:
                            self._stats['network_io'] = net_io.bytes_sent + net_io.bytes_recv
                        else:
                            self._stats['network_io'] = 0.0
                    except Exception:
                        self._stats['network_io'] = 0.0

                time.sleep(interval)

            except Exception as e:
                self.logger.error(f"系统资源监控失败: {str(e)}")
                time.sleep(interval)
    
    def get_current_stats(self) -> Dict[str, float]:
        """获取当前系统统计信息"""
        with self._lock:
            return self._stats.copy()
    
    def is_system_overloaded(self, cpu_threshold: float = 80.0, 
                           memory_threshold: float = 85.0) -> bool:
        """检查系统是否过载"""
        with self._lock:
            return (self._stats['cpu_percent'] > cpu_threshold or 
                   self._stats['memory_percent'] > memory_threshold)


class AdvancedParallelExecutor:
    """
    高级并行执行器
    
    提供智能任务调度、负载均衡和资源管理。
    """
    
    def __init__(self, max_thread_workers: Optional[int] = None, max_process_workers: Optional[int] = None):
        self.logger = get_logger(self.__class__.__name__)
        
        # 自动确定工作器数量
        self.max_thread_workers = max_thread_workers or min(32, (mp.cpu_count() or 1) + 4)
        self.max_process_workers = max_process_workers or (mp.cpu_count() or 1)
        
        # 执行器
        self.thread_executor = ThreadPoolExecutor(max_workers=self.max_thread_workers)
        self.process_executor = ProcessPoolExecutor(max_workers=self.max_process_workers)
        
        # 任务队列
        self.task_queue = queue.PriorityQueue()
        self.result_cache: Dict[str, Any] = {}
        
        # 负载均衡和监控
        self.load_balancer = LoadBalancer()
        self.resource_monitor = SystemResourceMonitor()
        
        # 控制变量
        self._running = False
        self._scheduler_thread = None
        self._lock = threading.RLock()
        
        # 注册工作器
        for i in range(self.max_thread_workers):
            self.load_balancer.register_worker(f"thread_{i}", WorkerType.THREAD)
        
        for i in range(self.max_process_workers):
            self.load_balancer.register_worker(f"process_{i}", WorkerType.PROCESS)
        
        # 启动资源监控
        self.resource_monitor.start_monitoring()
    
    def start(self):
        """启动并行执行器"""
        if self._running:
            return
        
        self._running = True
        self._scheduler_thread = threading.Thread(
            target=self._scheduler_loop,
            daemon=True
        )
        self._scheduler_thread.start()
        self.logger.info("高级并行执行器已启动")
    
    def stop(self):
        """停止并行执行器"""
        self._running = False
        
        if self._scheduler_thread:
            self._scheduler_thread.join(timeout=5.0)
        
        self.thread_executor.shutdown(wait=True)
        self.process_executor.shutdown(wait=True)
        self.resource_monitor.stop_monitoring()
        
        self.logger.info("高级并行执行器已停止")
    
    def submit_task(self, task: Task) -> str:
        """
        提交任务
        
        Args:
            task: 任务对象
            
        Returns:
            任务ID
        """
        # 任务优先级排序（优先级高的先执行）
        priority_score = -task.priority.value  # 负数使高优先级排在前面
        self.task_queue.put((priority_score, time.time(), task))
        
        self.logger.debug(f"任务已提交: {task.task_id}, 优先级: {task.priority.value}")
        return task.task_id
    
    def _scheduler_loop(self):
        """任务调度循环"""
        while self._running:
            try:
                # 检查系统负载
                if self.resource_monitor.is_system_overloaded():
                    time.sleep(0.5)  # 系统过载时减缓调度
                    continue
                
                # 获取任务
                try:
                    _, _, task = self.task_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # 选择最佳工作器
                best_worker = self.load_balancer.select_best_worker(task)
                if not best_worker:
                    # 没有可用工作器，重新放回队列
                    priority_score = -task.priority.value
                    self.task_queue.put((priority_score, time.time(), task))
                    time.sleep(0.1)
                    continue
                
                # 执行任务
                self._execute_task(task, best_worker)
                
            except Exception as e:
                self.logger.error(f"任务调度失败: {e}")
                time.sleep(0.1)
    
    def _execute_task(self, task: Task, worker_id: str):
        """执行任务"""
        try:
            # 标记工作器为忙碌状态
            with self._lock:
                if worker_id in self.load_balancer.worker_stats:
                    self.load_balancer.worker_stats[worker_id].is_busy = True
            
            # 选择执行器
            if task.worker_type == WorkerType.THREAD:
                executor = self.thread_executor
            elif task.worker_type == WorkerType.PROCESS:
                executor = self.process_executor
            else:
                raise ValueError(f"不支持的工作器类型: {task.worker_type}")
            
            # 提交任务执行
            future = executor.submit(self._task_wrapper, task, worker_id)
            
            # 异步处理结果
            def handle_result(fut):
                try:
                    result = fut.result()
                    if task.callback:
                        task.callback(task.task_id, result, None)
                except Exception as e:
                    self.logger.error(f"任务执行失败: {task.task_id}, 错误: {e}")
                    if task.callback:
                        task.callback(task.task_id, None, e)
                finally:
                    # 标记工作器为空闲状态
                    with self._lock:
                        if worker_id in self.load_balancer.worker_stats:
                            self.load_balancer.worker_stats[worker_id].is_busy = False
            
            future.add_done_callback(handle_result)
            
        except Exception as e:
            self.logger.error(f"任务执行提交失败: {task.task_id}, 错误: {e}")
            # 标记工作器为空闲状态
            with self._lock:
                if worker_id in self.load_balancer.worker_stats:
                    self.load_balancer.worker_stats[worker_id].is_busy = False
    
    def _task_wrapper(self, task: Task, worker_id: str) -> Any:
        """任务包装器，用于统计和错误处理"""
        start_time = time.time()
        success = False
        result = None
        
        try:
            # 执行任务
            result = task.func(*task.args, **task.kwargs)
            success = True
            
            # 缓存结果
            self.result_cache[task.task_id] = result
            
            return result
            
        except Exception as e:
            self.logger.error(f"任务执行失败: {task.task_id}, 错误: {e}")
            
            # 重试逻辑
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                self.logger.info(f"任务重试: {task.task_id}, 第{task.retry_count}次重试")
                
                # 重新提交任务
                priority_score = -task.priority.value
                self.task_queue.put((priority_score, time.time(), task))
            
            raise e
            
        finally:
            # 更新工作器统计信息
            execution_time = time.time() - start_time
            
            # 获取当前进程的资源使用情况
            try:
                process = psutil.Process()
                cpu_usage = process.cpu_percent()
                memory_usage = process.memory_percent()
            except:
                cpu_usage = 0.0
                memory_usage = 0.0
            
            self.load_balancer.update_worker_stats(
                worker_id, execution_time, success, cpu_usage, memory_usage
            )
    
    def get_result(self, task_id: str) -> Optional[Any]:
        """获取任务结果"""
        return self.result_cache.get(task_id)
    
    def get_status_report(self) -> Dict[str, Any]:
        """获取状态报告"""
        return {
            'executor_running': self._running,
            'task_queue_size': self.task_queue.qsize(),
            'cached_results': len(self.result_cache),
            'load_balancer': self.load_balancer.get_load_report(),
            'system_resources': self.resource_monitor.get_current_stats()
        }


# 全局实例
advanced_parallel_executor = AdvancedParallelExecutor()

# 装饰器：自动并行化
def parallelize(worker_type: WorkerType = WorkerType.THREAD, 
               priority: TaskPriority = TaskPriority.NORMAL,
               timeout: Optional[float] = None):
    """
    并行化装饰器
    
    自动将函数调用转换为并行任务。
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            task_id = f"{func.__name__}_{int(time.time() * 1000000)}"
            
            task = Task(
                task_id=task_id,
                func=func,
                args=args,
                kwargs=kwargs,
                worker_type=worker_type,
                priority=priority,
                timeout=timeout
            )
            
            # 启动执行器（如果未启动）
            if not advanced_parallel_executor._running:
                advanced_parallel_executor.start()
            
            # 提交任务
            advanced_parallel_executor.submit_task(task)
            
            return task_id
        
        return wrapper
    return decorator

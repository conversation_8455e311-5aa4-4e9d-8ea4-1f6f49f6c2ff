# A股数据采集框架 - Python 3.11使用指南

## 🎯 专为Python 3.11环境优化

由于您使用的是Python 3.11环境，我们提供了专门的安装和测试脚本来解决兼容性问题。

## ⚠️ 常见问题解决

### 1. sqlite3导入错误
**问题**: `ERROR: No matching distribution found for sqlite3`

**解决方案**: sqlite3是Python内置模块，无需通过pip安装。我们已经修复了requirements.txt文件。

### 2. numpy版本兼容性
**问题**: numpy版本与Python 3.11不兼容

**解决方案**: 使用numpy>=1.23.0版本，专门支持Python 3.11。

## 🚀 快速安装（推荐）

### 方法1: 使用Python 3.11专用安装脚本
```bash
# 运行专用安装脚本
python install_py311.py
```

这个脚本会：
- ✅ 检查Python 3.11兼容性
- ✅ 安装兼容的依赖包版本
- ✅ 创建必要的目录和配置
- ✅ 运行基础功能测试

### 方法2: 手动安装核心依赖
```bash
# 安装核心依赖（Python 3.11兼容版本）
pip install numpy>=1.23.0
pip install pandas>=1.5.0
pip install requests>=2.28.0
pip install beautifulsoup4>=4.11.0
pip install lxml>=4.9.0
pip install python-dateutil>=2.8.0

# 可选：安装akshare（可能需要额外配置）
pip install akshare>=1.9.0

# 可选：安装matplotlib用于图表
pip install matplotlib>=3.6.0
```

## 🧪 测试框架

### 基础测试（推荐先运行）
```bash
# 运行基础测试，不依赖第三方包
python test_basic_cn.py
```

### 快速功能测试
```bash
# 运行Python 3.11专用快速测试
python quick_test_py311.py
```

### 完整测试
```bash
# 运行完整测试（需要安装依赖包）
python test_framework_cn.py
```

## 📦 依赖包状态检查

运行以下脚本检查依赖包安装状态：

```python
# check_packages.py
import sys

packages = {
    'numpy': '数值计算',
    'pandas': '数据处理', 
    'requests': 'HTTP请求',
    'beautifulsoup4': 'HTML解析',
    'akshare': 'A股数据源',
    'matplotlib': '图表绘制'
}

print(f"Python版本: {sys.version}")
print("\n依赖包状态:")

for package, desc in packages.items():
    try:
        if package == 'beautifulsoup4':
            import bs4
        else:
            __import__(package)
        print(f"✅ {package} ({desc})")
    except ImportError:
        print(f"❌ {package} ({desc}) - 未安装")
```

## 🎯 使用示例

### 1. 基础功能测试
```python
# 测试股票代码工具
from data_acquisition.utils.stock_codes import normalize_stock_code

code = normalize_stock_code('000001')
print(f"标准化代码: {code}")  # 输出: 000001.SZ
```

### 2. 配置系统测试
```python
# 测试配置系统
from data_acquisition.config.settings import Config

config = Config()
print(f"缓存启用: {config.CACHE_ENABLED}")
print(f"数据库URL: {config.DATABASE_URL}")
```

### 3. 数据库功能测试
```python
# 测试数据库功能
from data_acquisition.storage.database import DatabaseManager
from data_acquisition.config.settings import Config

config = Config()
db = DatabaseManager(config)

# 保存股票信息
info = {'name': '测试股票', 'exchange': 'SZ'}
success = db.save_stock_info('TEST.SZ', info)
print(f"保存成功: {success}")
```

## 🔧 故障排除

### 问题1: 模块导入失败
```bash
# 确保在正确的目录中
cd /path/to/your/project

# 检查Python路径
python -c "import sys; print(sys.path)"

# 检查框架文件
ls -la data_acquisition/
```

### 问题2: 依赖包安装失败
```bash
# 更新pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ numpy pandas

# 在虚拟环境中安装
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

### 问题3: akshare安装失败
```bash
# akshare可能需要额外依赖
pip install akshare --no-deps
pip install requests beautifulsoup4 lxml pandas numpy

# 或者跳过akshare，使用网络爬虫功能
# 框架会自动降级到备用数据源
```

## 📚 推荐使用流程

### 第一步：环境检查
```bash
python --version  # 确认Python 3.11
python install_py311.py  # 安装依赖
```

### 第二步：基础测试
```bash
python test_basic_cn.py  # 基础功能测试
python quick_test_py311.py  # 快速功能测试
```

### 第三步：功能体验
```bash
# 如果pandas可用
python examples/basic_usage_cn.py

# 如果matplotlib也可用
python examples/strategy_backtest_cn.py
```

## 🎉 成功标志

当您看到以下输出时，说明框架安装成功：

```
🎉 Python 3.11环境安装完成！
✅ 核心功能测试通过！
📦 可选功能: X/Y 可用
```

## 📞 技术支持

如果遇到问题：

1. **查看日志**: 检查 `logs/` 目录中的日志文件
2. **运行诊断**: `python test_basic_cn.py`
3. **检查配置**: 查看 `config_py311.env` 文件
4. **重新安装**: `python install_py311.py`

## 🚀 下一步

安装成功后，您可以：

1. 📖 阅读完整文档：`README_CN.md`
2. 🧪 运行示例代码：`examples/` 目录
3. 🔧 自定义配置：修改配置文件
4. 📊 开始您的量化投资项目！

---

**专为Python 3.11环境优化的A股数据采集框架，让您的量化投资更加顺畅！** 🎯

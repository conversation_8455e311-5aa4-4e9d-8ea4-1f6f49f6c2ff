"""
快速演示版优化回测系统

用于展示优化效果的简化版本，使用较短的回测期间和较少的股票
"""

import sys
import os
from pathlib import Path
import sqlite3
import time
from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Any
import warnings
warnings.filterwarnings('ignore')

from data_acquisition import DataManager
from data_acquisition.utils.technical_indicators import TechnicalIndicators

class QuickDemoBacktest:
    """快速演示回测系统"""
    
    def __init__(self):
        """初始化演示系统"""
        self.data_manager = DataManager()
        self.indicators = TechnicalIndicators()
        
        # 选股条件
        self.criteria = {
            'market_cap_min': 15e8,    # 15亿
            'market_cap_max': 300e8,   # 300亿
            'wr_threshold': -20,       # WR > -20
            'big_order_ratio': 0.4,    # 大单净量 > 0.4
            'volume_ratio': 2.0        # 量比 > 2
        }
        
        # 性能统计
        self.perf_stats = {}
        
        # 数据缓存
        self.data_cache = {}
    
    def get_demo_stocks(self) -> List[str]:
        """获取演示用的创业板股票"""
        return [
            '300001.SZ', '300002.SZ', '300008.SZ', '300015.SZ', '300029.SZ',
            '300033.SZ', '300059.SZ', '300124.SZ', '300144.SZ', '300251.SZ',
            '300274.SZ', '300296.SZ', '300347.SZ', '300408.SZ', '300413.SZ'
        ]
    
    def preload_data(self, stock_list: List[str], start_date: str, end_date: str):
        """预加载所有股票数据"""
        print("📥 预加载股票数据...")
        start_time = time.time()
        
        def load_single_stock(stock_code: str):
            try:
                data = self.data_manager.get_stock_data(stock_code, start_date, end_date)
                if data is not None and not data.empty and len(data) >= 20:
                    # 计算技术指标
                    indicators_data = self.indicators.calculate_all_indicators(data)
                    return stock_code, indicators_data
            except Exception as e:
                print(f"加载 {stock_code} 失败: {e}")
            return stock_code, None
        
        # 并行加载
        with ThreadPoolExecutor(max_workers=5) as executor:
            results = list(tqdm(
                executor.map(load_single_stock, stock_list),
                total=len(stock_list),
                desc="加载数据"
            ))
        
        # 存储到缓存
        successful_loads = 0
        for stock_code, data in results:
            if data is not None:
                self.data_cache[stock_code] = data
                successful_loads += 1
        
        load_time = time.time() - start_time
        self.perf_stats['data_load_time'] = load_time
        
        print(f"✅ 数据预加载完成: {successful_loads}/{len(stock_list)} 只股票")
        print(f"⏱️  加载耗时: {load_time:.2f} 秒")
    
    def estimate_market_cap(self, stock_code: str, price: float) -> float:
        """估算流通市值"""
        try:
            code_num = int(stock_code[3:6])
            
            if code_num < 100:
                shares = (2 + (code_num % 10) * 0.5) * 1e8
            elif code_num < 500:
                shares = (1.5 + (code_num % 20) * 0.2) * 1e8
            else:
                shares = (1 + (code_num % 30) * 0.1) * 1e8
            
            return price * shares
        except:
            return 0
    
    def simulate_big_order_ratio(self, volume: float, price_change: float) -> float:
        """模拟大单净量比例"""
        base_ratio = 0.3
        volume_factor = min(volume / 1e6, 2.0) * 0.1
        price_factor = max(price_change, 0) * 0.5
        
        return base_ratio + volume_factor + price_factor
    
    def screen_stocks_optimized(self, date: str) -> List[str]:
        """优化版选股"""
        selected_stocks = []
        
        try:
            date_index = pd.to_datetime(date)
            
            for stock_code, data in self.data_cache.items():
                try:
                    # 检查日期是否存在
                    if date_index not in data.index:
                        continue
                    
                    current_data = data.loc[date_index]
                    
                    # 条件1: 流通市值
                    close_price = float(current_data['close'])
                    market_cap = self.estimate_market_cap(stock_code, close_price)
                    
                    if not (self.criteria['market_cap_min'] <= market_cap <= self.criteria['market_cap_max']):
                        continue
                    
                    # 条件2: WR值
                    if 'williams_r' not in data.columns or pd.isna(current_data['williams_r']):
                        continue
                    
                    wr_value = float(current_data['williams_r'])
                    if wr_value <= self.criteria['wr_threshold']:
                        continue
                    
                    # 条件3: 量比
                    if 'volume_ratio' not in data.columns or pd.isna(current_data['volume_ratio']):
                        continue
                    
                    volume_ratio = float(current_data['volume_ratio'])
                    if volume_ratio <= self.criteria['volume_ratio']:
                        continue
                    
                    # 条件4: 大单净量（模拟）
                    current_volume = float(current_data['volume'])
                    price_change = (current_data['close'] - current_data['open']) / current_data['open']
                    big_order_ratio = self.simulate_big_order_ratio(current_volume, price_change)
                    
                    if big_order_ratio <= self.criteria['big_order_ratio']:
                        continue
                    
                    # 所有条件满足
                    selected_stocks.append(stock_code)
                    
                    # 限制选股数量
                    if len(selected_stocks) >= 10:
                        break
                        
                except Exception as e:
                    continue
            
        except Exception as e:
            print(f"选股失败 {date}: {e}")
        
        return selected_stocks
    
    def calculate_portfolio_returns(self, selections: Dict[str, List[str]], 
                                  trading_dates: List[str]) -> List[float]:
        """计算投资组合收益率"""
        print("📈 计算投资组合收益率...")
        
        portfolio_returns = []
        
        for i, current_date in enumerate(tqdm(trading_dates[:-1], desc="计算收益率")):
            next_date = trading_dates[i + 1]
            
            selected_stocks = selections.get(current_date, [])
            
            if not selected_stocks:
                portfolio_returns.append(0.0)
                continue
            
            try:
                current_date_index = pd.to_datetime(current_date)
                next_date_index = pd.to_datetime(next_date)
                
                stock_returns = []
                
                for stock_code in selected_stocks:
                    if stock_code in self.data_cache:
                        data = self.data_cache[stock_code]
                        
                        if (current_date_index in data.index and 
                            next_date_index in data.index):
                            
                            current_price = float(data.loc[current_date_index]['close'])
                            next_price = float(data.loc[next_date_index]['close'])
                            
                            stock_return = (next_price - current_price) / current_price
                            stock_returns.append(stock_return)
                
                if stock_returns:
                    portfolio_return = np.mean(stock_returns)
                    portfolio_returns.append(float(portfolio_return))
                else:
                    portfolio_returns.append(0.0)
                    
            except Exception as e:
                portfolio_returns.append(0.0)
        
        return portfolio_returns
    
    def run_demo_backtest(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """运行演示回测"""
        print(f"🚀 快速演示回测")
        print(f"回测期间: {start_date} 到 {end_date}")
        print(f"=" * 60)
        
        total_start_time = time.time()
        
        # 1. 获取股票列表
        stock_list = self.get_demo_stocks()
        print(f"演示股票池: {len(stock_list)} 只")
        
        # 2. 预加载数据
        self.preload_data(stock_list, start_date, end_date)
        
        # 3. 生成交易日期
        trading_dates = pd.date_range(start=start_date, end=end_date, freq='B')
        trading_dates = [d.strftime('%Y-%m-%d') for d in trading_dates]
        
        print(f"交易日数量: {len(trading_dates)} 天")
        
        # 4. 执行选股
        print("🎯 执行日频选股...")
        selection_start_time = time.time()
        
        daily_selections = {}
        
        for date in tqdm(trading_dates, desc="日频选股"):
            selected_stocks = self.screen_stocks_optimized(date)
            daily_selections[date] = selected_stocks
        
        selection_time = time.time() - selection_start_time
        self.perf_stats['selection_time'] = selection_time
        
        # 5. 计算收益率
        backtest_start_time = time.time()
        portfolio_returns = self.calculate_portfolio_returns(daily_selections, trading_dates)
        backtest_time = time.time() - backtest_start_time
        self.perf_stats['backtest_time'] = backtest_time
        
        # 6. 计算回测指标
        print("📊 计算回测指标...")
        results = self.calculate_metrics(portfolio_returns, daily_selections)
        
        # 7. 性能统计
        total_time = time.time() - total_start_time
        self.perf_stats['total_time'] = total_time
        
        results['performance_stats'] = self.perf_stats
        results['daily_returns'] = portfolio_returns
        results['daily_selections'] = daily_selections
        
        return results
    
    def calculate_metrics(self, returns: List[float], selections: Dict[str, List[str]]) -> Dict[str, Any]:
        """计算回测指标"""
        try:
            returns_array = np.array(returns, dtype=float)
            returns_array = returns_array[~np.isnan(returns_array)]
            
            if len(returns_array) == 0:
                return self._empty_results()
            
            # 基本统计
            total_return = float(np.prod(1 + returns_array) - 1)
            annual_return = float((1 + total_return) ** (252 / len(returns_array)) - 1)
            volatility = float(np.std(returns_array) * np.sqrt(252))
            sharpe_ratio = float(annual_return / volatility) if volatility > 0 else 0.0
            
            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = float(np.min(drawdown))
            
            # 其他指标
            win_rate = float(np.mean(returns_array > 0))
            
            # 选股统计
            selection_counts = [len(stocks) for stocks in selections.values()]
            avg_selection_count = float(np.mean(selection_counts)) if selection_counts else 0
            
            return {
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'avg_selection_count': avg_selection_count,
                'total_trading_days': len(returns_array),
                'cumulative_returns': cumulative_returns
            }
            
        except Exception as e:
            print(f"计算指标失败: {e}")
            return self._empty_results()
    
    def _empty_results(self) -> Dict[str, Any]:
        """空结果"""
        return {
            'total_return': 0.0,
            'annual_return': 0.0,
            'volatility': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0,
            'avg_selection_count': 0.0,
            'total_trading_days': 0,
            'cumulative_returns': np.array([])
        }
    
    def print_results(self, results: Dict[str, Any]):
        """打印结果"""
        print(f"\n" + "="*60)
        print(f"📊 快速演示回测结果")
        print(f"="*60)
        
        print(f"\n📈 收益指标:")
        print(f"  总收益率:     {results['total_return']:.2%}")
        print(f"  年化收益率:   {results['annual_return']:.2%}")
        print(f"  年化波动率:   {results['volatility']:.2%}")
        print(f"  夏普比率:     {results['sharpe_ratio']:.2f}")
        
        print(f"\n📉 风险指标:")
        print(f"  最大回撤:     {results['max_drawdown']:.2%}")
        print(f"  胜率:         {results['win_rate']:.2%}")
        
        print(f"\n📊 交易统计:")
        print(f"  交易天数:     {results['total_trading_days']} 天")
        print(f"  平均选股:     {results['avg_selection_count']:.1f} 只")
        
        # 性能统计
        if 'performance_stats' in results:
            perf = results['performance_stats']
            print(f"\n⚡ 性能统计:")
            print(f"  数据加载:     {perf.get('data_load_time', 0):.2f} 秒")
            print(f"  选股执行:     {perf.get('selection_time', 0):.2f} 秒")
            print(f"  回测计算:     {perf.get('backtest_time', 0):.2f} 秒")
            print(f"  总执行时间:   {perf.get('total_time', 0):.2f} 秒")
        
        # 选股样例
        if 'daily_selections' in results:
            selections = results['daily_selections']
            print(f"\n📋 选股记录样例:")
            
            selection_samples = [(date, stocks) for date, stocks in selections.items() if stocks][:3]
            
            for date, stocks in selection_samples:
                print(f"  {date}: {len(stocks)} 只 - {', '.join(stocks[:3])}")
    
    def cleanup(self):
        """清理资源"""
        if hasattr(self, 'data_manager'):
            self.data_manager.cleanup()

def main():
    """主函数"""
    print("🚀 快速演示版优化回测系统")
    print("=" * 60)
    
    demo = QuickDemoBacktest()
    
    try:
        # 设置回测参数（最近3个月）
        end_date = date.today()
        start_date = end_date - timedelta(days=90)
        
        print(f"📅 演示参数:")
        print(f"  开始日期: {start_date}")
        print(f"  结束日期: {end_date}")
        print(f"  回测期间: 约3个月")
        print(f"  股票数量: 15只创业板股票")
        
        # 执行回测
        results = demo.run_demo_backtest(
            start_date.strftime('%Y-%m-%d'),
            end_date.strftime('%Y-%m-%d')
        )
        
        # 打印结果
        demo.print_results(results)
        
        # 优化效果展示
        total_time = results.get('performance_stats', {}).get('total_time', 0)
        if total_time > 0:
            print(f"\n🏆 优化效果:")
            print(f"  ✅ 3个月回测仅需 {total_time:.1f} 秒")
            print(f"  ✅ 平均每日处理时间: {total_time/results['total_trading_days']:.3f} 秒")
            print(f"  ✅ 预计17个月回测时间: {total_time * 17/3:.1f} 秒 (约{total_time * 17/3/60:.1f}分钟)")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        demo.cleanup()

if __name__ == "__main__":
    main()

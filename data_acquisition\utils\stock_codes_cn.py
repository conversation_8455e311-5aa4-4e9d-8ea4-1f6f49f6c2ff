"""
A股股票代码工具模块

处理中国股票市场的股票代码格式和验证
支持上海证券交易所和深圳证券交易所的各种板块
"""

import re
from typing import List, Optional, Tuple, Set
from enum import Enum

class Exchange(Enum):
    """中国证券交易所"""
    SHANGHAI = "SH"    # 上海证券交易所
    SHENZHEN = "SZ"    # 深圳证券交易所

class StockCodeValidator:
    """A股股票代码验证器"""
    
    # 股票代码模式
    PATTERNS = {
        # 上海证券交易所
        'SH_MAIN': re.compile(r'^60[0-9]{4}$'),      # 主板: 600000-609999
        'SH_STAR': re.compile(r'^68[8-9][0-9]{3}$'), # 科创板: 688000-689999
        'SH_BOND': re.compile(r'^11[0-9]{4}$'),      # 债券: 110000-119999
        
        # 深圳证券交易所  
        'SZ_MAIN': re.compile(r'^00[0-9]{4}$'),      # 主板: 000000-009999
        'SZ_SME': re.compile(r'^00[2-9][0-9]{3}$'),  # 中小板: 002000-009999
        'SZ_CHINEXT': re.compile(r'^30[0-9]{4}$'),   # 创业板: 300000-309999
        'SZ_BOND': re.compile(r'^12[0-9]{4}$'),      # 债券: 120000-129999
    }
    
    @classmethod
    def is_valid_code(cls, code: str) -> bool:
        """
        检查股票代码是否有效
        
        参数:
            code: 股票代码（6位数字）
            
        返回:
            bool: 有效返回True
        """
        if not code or len(code) != 6:
            return False
            
        return any(pattern.match(code) for pattern in cls.PATTERNS.values())
    
    @classmethod
    def get_exchange(cls, code: str) -> Optional[Exchange]:
        """
        获取股票代码对应的交易所
        
        参数:
            code: 股票代码（6位数字）
            
        返回:
            Exchange: 交易所枚举，无效时返回None
        """
        if not cls.is_valid_code(code):
            return None
            
        # 上海交易所模式
        if (cls.PATTERNS['SH_MAIN'].match(code) or 
            cls.PATTERNS['SH_STAR'].match(code) or
            cls.PATTERNS['SH_BOND'].match(code)):
            return Exchange.SHANGHAI
            
        # 深圳交易所模式
        if (cls.PATTERNS['SZ_MAIN'].match(code) or 
            cls.PATTERNS['SZ_SME'].match(code) or
            cls.PATTERNS['SZ_CHINEXT'].match(code) or
            cls.PATTERNS['SZ_BOND'].match(code)):
            return Exchange.SHENZHEN
            
        return None
    
    @classmethod
    def get_market_type(cls, code: str) -> Optional[str]:
        """
        获取股票代码对应的市场类型
        
        参数:
            code: 股票代码（6位数字）
            
        返回:
            str: 市场类型，无效时返回None
        """
        if not cls.is_valid_code(code):
            return None
            
        market_type_map = {
            'SH_MAIN': '上海主板',
            'SH_STAR': '科创板',
            'SH_BOND': '上海债券',
            'SZ_MAIN': '深圳主板',
            'SZ_SME': '中小板',
            'SZ_CHINEXT': '创业板',
            'SZ_BOND': '深圳债券'
        }
        
        for market_type, pattern in cls.PATTERNS.items():
            if pattern.match(code):
                return market_type_map.get(market_type, market_type)
                
        return None

def normalize_stock_code(code: str) -> Optional[str]:
    """
    将股票代码标准化为标准格式 (XXXXXX.EX)
    
    参数:
        code: 各种格式的股票代码
        
    返回:
        str: 标准化代码，无效时返回None
    """
    if not code:
        return None
        
    # 移除空白字符并转换为大写
    code = code.strip().upper()
    
    # 处理不同的输入格式
    if '.' in code:
        # 格式: XXXXXX.SH 或 XXXXXX.SZ
        parts = code.split('.')
        if len(parts) == 2:
            stock_code, exchange = parts
            if len(stock_code) == 6 and exchange in ['SH', 'SZ']:
                if StockCodeValidator.is_valid_code(stock_code):
                    return f"{stock_code}.{exchange}"
    else:
        # 格式: XXXXXX（仅6位数字）
        if len(code) == 6 and code.isdigit():
            exchange = StockCodeValidator.get_exchange(code)
            if exchange:
                return f"{code}.{exchange.value}"
    
    return None

def parse_stock_code(code: str) -> Optional[Tuple[str, str]]:
    """
    解析股票代码为代码和交易所部分
    
    参数:
        code: 格式为 XXXXXX.EX 的股票代码
        
    返回:
        Tuple[str, str]: (股票代码, 交易所)，无效时返回None
    """
    normalized = normalize_stock_code(code)
    if normalized:
        return tuple(normalized.split('.'))
    return None

def validate_stock_codes(codes: List[str]) -> Tuple[List[str], List[str]]:
    """
    验证股票代码列表
    
    参数:
        codes: 股票代码列表
        
    返回:
        Tuple[List[str], List[str]]: (有效代码, 无效代码)
    """
    valid_codes = []
    invalid_codes = []
    
    for code in codes:
        normalized = normalize_stock_code(code)
        if normalized:
            valid_codes.append(normalized)
        else:
            invalid_codes.append(code)
    
    return valid_codes, invalid_codes

def get_stock_codes_by_exchange(codes: List[str]) -> dict:
    """
    按交易所分组股票代码
    
    参数:
        codes: 股票代码列表
        
    返回:
        dict: {交易所: [代码列表]}
    """
    result = {'SH': [], 'SZ': []}
    
    for code in codes:
        normalized = normalize_stock_code(code)
        if normalized:
            _, exchange = normalized.split('.')
            result[exchange].append(normalized)
    
    return result

def is_main_board_stock(code: str) -> bool:
    """
    检查是否为主板股票（非创业板、科创板等）
    
    参数:
        code: 股票代码
        
    返回:
        bool: 主板股票返回True
    """
    parsed = parse_stock_code(code)
    if not parsed:
        return False
        
    stock_code, _ = parsed
    
    # 主板模式
    return (StockCodeValidator.PATTERNS['SH_MAIN'].match(stock_code) or
            StockCodeValidator.PATTERNS['SZ_MAIN'].match(stock_code))

def is_growth_stock(code: str) -> bool:
    """
    检查是否为成长型市场股票（创业板、科创板）
    
    参数:
        code: 股票代码
        
    返回:
        bool: 成长型市场股票返回True
    """
    parsed = parse_stock_code(code)
    if not parsed:
        return False
        
    stock_code, _ = parsed
    
    # 成长型市场模式
    return (StockCodeValidator.PATTERNS['SH_STAR'].match(stock_code) or
            StockCodeValidator.PATTERNS['SZ_CHINEXT'].match(stock_code))

def is_sme_stock(code: str) -> bool:
    """
    检查是否为中小板股票
    
    参数:
        code: 股票代码
        
    返回:
        bool: 中小板股票返回True
    """
    parsed = parse_stock_code(code)
    if not parsed:
        return False
        
    stock_code, _ = parsed
    return StockCodeValidator.PATTERNS['SZ_SME'].match(stock_code) is not None

def get_stock_market_info(code: str) -> Optional[dict]:
    """
    获取股票的市场信息
    
    参数:
        code: 股票代码
        
    返回:
        dict: 包含交易所、市场类型等信息的字典
    """
    normalized = normalize_stock_code(code)
    if not normalized:
        return None
    
    stock_code, exchange_code = normalized.split('.')
    exchange = StockCodeValidator.get_exchange(stock_code)
    market_type = StockCodeValidator.get_market_type(stock_code)
    
    return {
        'code': normalized,
        'stock_number': stock_code,
        'exchange': exchange.value if exchange else None,
        'exchange_name': '上海证券交易所' if exchange == Exchange.SHANGHAI else '深圳证券交易所',
        'market_type': market_type,
        'is_main_board': is_main_board_stock(normalized),
        'is_growth_market': is_growth_stock(normalized),
        'is_sme_board': is_sme_stock(normalized)
    }

# 常见股票代码范围，用于不同市场
MARKET_RANGES = {
    '上海主板': (600000, 609999),
    '科创板': (688000, 689999), 
    '深圳主板': (0, 9999),
    '创业板': (300000, 309999),
    '中小板': (2000, 2999),  # 002000-002999的子集
}

def generate_stock_codes_in_range(start: int, end: int, exchange: str) -> List[str]:
    """
    生成指定范围内的股票代码
    
    参数:
        start: 起始代码（整数）
        end: 结束代码（整数）
        exchange: 交易所（'SH' 或 'SZ'）
        
    返回:
        List[str]: 股票代码列表
    """
    codes = []
    for code_int in range(start, end + 1):
        code = f"{code_int:06d}"
        if StockCodeValidator.is_valid_code(code):
            codes.append(f"{code}.{exchange}")
    
    return codes

def get_popular_stocks() -> dict:
    """
    获取一些知名股票的代码和名称
    
    返回:
        dict: {代码: 名称} 的字典
    """
    return {
        '000001.SZ': '平安银行',
        '000002.SZ': '万科A',
        '000858.SZ': '五粮液',
        '600000.SH': '浦发银行',
        '600036.SH': '招商银行',
        '600519.SH': '贵州茅台',
        '600887.SH': '伊利股份',
        '000725.SZ': '京东方A',
        '002415.SZ': '海康威视',
        '300059.SZ': '东方财富',
        '688981.SH': '中芯国际',
        '688036.SH': '传音控股'
    }

def search_stock_by_pattern(pattern: str, exchange: Optional[str] = None) -> List[str]:
    """
    根据模式搜索股票代码
    
    参数:
        pattern: 搜索模式（如 '6000*' 表示以6000开头）
        exchange: 限制交易所（可选）
        
    返回:
        List[str]: 匹配的股票代码列表
    """
    # 这是一个简化的实现，实际应用中可能需要连接数据库
    # 这里只返回一些示例
    if pattern.startswith('6000'):
        codes = ['600000.SH', '600036.SH', '600519.SH', '600887.SH']
    elif pattern.startswith('0000'):
        codes = ['000001.SZ', '000002.SZ', '000858.SZ', '000725.SZ']
    elif pattern.startswith('3000'):
        codes = ['300059.SZ', '300015.SZ', '300033.SZ']
    else:
        codes = []
    
    if exchange:
        codes = [code for code in codes if code.endswith(f'.{exchange}')]
    
    return codes

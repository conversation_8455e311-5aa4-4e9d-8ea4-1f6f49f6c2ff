#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创业板动态因子策略完整系统集成测试
验证数据下载、清洗、存储、回测的完整流程
"""

import os
import sys
import logging
import time
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.quantization.strategies.chinext_dynamic_factor_strategy import ChiNextDynamicFactorStrategy
from src.quantization.strategies.chinext_minute_data_downloader import ChinextMinuteDataDownloader
from src.quantization.strategies.data_quality_controller import DataQualityController
from src.quantization.strategies.tick_backtester import TickBacktester

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('integrated_test.log', encoding='utf-8')
        ]
    )

def test_data_download_system():
    """测试数据下载系统"""
    print("\n" + "="*60)
    print("1. 测试创业板分时数据下载系统")
    print("="*60)
    
    try:
        # 创建下载器
        downloader = ChinextMinuteDataDownloader()
        
        # 测试获取创业板股票列表
        print("获取创业板股票列表...")
        stock_codes = downloader.get_chinext_stocks()
        print(f"获取到创业板股票: {len(stock_codes)} 只")
        
        if stock_codes:
            print(f"前10只股票: {stock_codes[:10]}")
        
        # 测试获取交易日期
        print("\n获取最近5个交易日...")
        dates = downloader.get_trading_dates(days=5)
        print(f"交易日期: {dates}")
        
        # 测试下载少量数据（避免API限制）
        if stock_codes and dates:
            test_stocks = stock_codes[:3]  # 只测试前3只股票
            test_dates = dates[-2:]       # 只测试最近2个交易日
            
            print(f"\n开始下载测试数据...")
            print(f"股票: {test_stocks}")
            print(f"日期: {test_dates}")
            
            result = downloader.batch_download_minute_data(test_stocks, test_dates)
            
            print(f"\n下载结果:")
            print(f"总任务数: {result['total']}")
            print(f"成功: {result['success']}")
            print(f"失败: {result['failed']}")
            print(f"成功率: {result['success_rate']:.2%}")
        
        return True
        
    except Exception as e:
        print(f"数据下载系统测试失败: {e}")
        return False

def test_data_quality_system():
    """测试数据质量控制系统"""
    print("\n" + "="*60)
    print("2. 测试数据质量控制系统")
    print("="*60)
    
    try:
        # 创建质量控制器
        controller = DataQualityController()
        
        # 获取一些测试数据进行清洗
        downloader = ChinextMinuteDataDownloader()
        dates = downloader.get_trading_dates(days=2)
        
        if dates:
            test_stocks = ['300015.SZ', '300017.SZ']  # 使用活跃股票
            test_date = dates[-1]  # 最近一个交易日
            
            print(f"测试数据清洗: {test_stocks} on {test_date}")
            
            for stock_code in test_stocks:
                print(f"\n清洗股票数据: {stock_code}")
                result = controller.clean_minute_data(stock_code, test_date)
                
                if result['status'] == 'success':
                    print(f"  原始记录数: {result['original_count']}")
                    print(f"  清洗后记录数: {result['cleaned_count']}")
                    print(f"  填补缺失值: {result['missing_filled']}")
                    print(f"  移除异常值: {result['outliers_removed']}")
                    print(f"  移除重复值: {result['duplicates_removed']}")
                else:
                    print(f"  清洗失败: {result.get('message', '未知错误')}")
        
        return True
        
    except Exception as e:
        print(f"数据质量控制系统测试失败: {e}")
        return False

def test_strategy_system():
    """测试策略系统"""
    print("\n" + "="*60)
    print("3. 测试创业板动态因子策略系统")
    print("="*60)
    
    try:
        # 创建策略实例
        strategy = ChiNextDynamicFactorStrategy()
        
        # 测试市场成交额获取
        print("测试市场成交额获取...")
        today = datetime.now().strftime('%Y-%m-%d')
        market_volume = strategy._get_market_volume(today)
        print(f"市场成交额: {market_volume/1e8:.2f}亿元")
        
        # 测试股票筛选
        print("\n测试股票筛选...")
        if market_volume > 1.5e12:  # 大于1.5万亿
            print("市场成交额满足条件，执行股票筛选...")
            candidates = strategy._get_candidate_stocks(today)
            print(f"候选股票数量: {len(candidates)}")
            
            if candidates:
                print(f"前5只候选股票: {candidates[:5]}")
        else:
            print("市场成交额不满足条件，跳过股票筛选")
        
        return True
        
    except Exception as e:
        print(f"策略系统测试失败: {e}")
        return False

def test_backtest_system():
    """测试回测系统"""
    print("\n" + "="*60)
    print("4. 测试TICK级回测系统")
    print("="*60)
    
    try:
        # 创建回测引擎
        backtester = TickBacktester(
            initial_capital=1000000,
            commission_rate=0.0003,
            slippage=0.001
        )
        
        print("回测引擎初始化成功")
        print(f"初始资金: {backtester.initial_capital:,.0f}元")
        print(f"手续费率: {backtester.commission_rate:.4f}")
        print(f"滑点率: {backtester.slippage:.4f}")
        print(f"买入时间: {backtester.buy_time}")
        print(f"卖出时间: {backtester.sell_time}")
        
        # 测试价格获取功能
        print("\n测试价格获取功能...")
        
        # 创建模拟分时数据
        import pandas as pd
        import numpy as np
        
        # 生成模拟的分时数据
        times = []
        for hour in [9, 10, 11, 13, 14]:
            for minute in range(30 if hour == 9 else 0, 60 if hour < 15 else 1):
                if hour == 11 and minute > 30:
                    break
                if hour == 15 and minute > 0:
                    break
                times.append(f"{hour:02d}:{minute:02d}:00")
        
        mock_data = pd.DataFrame({
            'time': times,
            'open': np.random.uniform(10, 20, len(times)),
            'high': np.random.uniform(15, 25, len(times)),
            'low': np.random.uniform(8, 15, len(times)),
            'close': np.random.uniform(10, 20, len(times)),
            'volume': np.random.randint(1000, 10000, len(times)),
            'amount': np.random.uniform(100000, 1000000, len(times))
        })
        
        # 测试获取执行价格
        from datetime import time
        buy_price = backtester._get_execution_price(mock_data, time(14, 30), 'buy')
        sell_price = backtester._get_execution_price(mock_data, time(9, 59), 'sell')
        
        if buy_price and sell_price:
            print(f"买入价格 (14:30): {buy_price:.2f}")
            print(f"卖出价格 (09:59): {sell_price:.2f}")
            
            # 测试手续费计算
            buy_amount = 100000  # 10万元
            sell_amount = 105000  # 10.5万元
            
            buy_commission = backtester._calculate_commission(buy_amount, 'buy')
            sell_commission = backtester._calculate_commission(sell_amount, 'sell')
            
            print(f"\n手续费测试:")
            print(f"买入手续费 (10万元): {buy_commission:.2f}元")
            print(f"卖出手续费 (10.5万元): {sell_commission:.2f}元")
        
        return True
        
    except Exception as e:
        print(f"回测系统测试失败: {e}")
        return False

def test_system_integration():
    """测试系统集成"""
    print("\n" + "="*60)
    print("5. 系统集成测试")
    print("="*60)
    
    try:
        print("测试各模块间的数据流...")
        
        # 1. 数据下载 -> 数据清洗
        downloader = ChinextMinuteDataDownloader()
        controller = DataQualityController()
        
        # 获取测试数据
        dates = downloader.get_trading_dates(days=1)
        if dates:
            test_date = dates[-1]
            test_stock = '300015.SZ'
            
            print(f"集成测试: {test_stock} on {test_date}")
            
            # 下载数据
            success, count, error = downloader.download_stock_minute_data(test_stock, test_date)
            if success:
                print(f"✓ 数据下载成功: {count} 条记录")
                
                # 清洗数据
                clean_result = controller.clean_minute_data(test_stock, test_date)
                if clean_result['status'] == 'success':
                    print(f"✓ 数据清洗成功: {clean_result['cleaned_count']} 条记录")
                    
                    # 策略分析
                    strategy = ChiNextDynamicFactorStrategy()
                    print("✓ 策略系统就绪")
                    
                    # 回测准备
                    backtester = TickBacktester()
                    print("✓ 回测系统就绪")
                    
                    print("\n✅ 系统集成测试通过！")
                    return True
                else:
                    print(f"✗ 数据清洗失败: {clean_result.get('message')}")
            else:
                print(f"✗ 数据下载失败: {error}")
        
        return False
        
    except Exception as e:
        print(f"系统集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    setup_logging()
    
    print("="*80)
    print("创业板动态因子策略完整系统集成测试")
    print("="*80)
    
    start_time = time.time()
    
    # 执行各项测试
    test_results = {
        '数据下载系统': test_data_download_system(),
        '数据质量控制系统': test_data_quality_system(),
        '策略系统': test_strategy_system(),
        '回测系统': test_backtest_system(),
        '系统集成': test_system_integration()
    }
    
    # 输出测试结果
    print("\n" + "="*80)
    print("测试结果汇总")
    print("="*80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    print(f"成功率: {passed/total:.1%}")
    
    elapsed_time = time.time() - start_time
    print(f"测试耗时: {elapsed_time:.1f}秒")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统运行正常。")
    else:
        print(f"\n⚠️  有 {total-passed} 项测试失败，请检查相关模块。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

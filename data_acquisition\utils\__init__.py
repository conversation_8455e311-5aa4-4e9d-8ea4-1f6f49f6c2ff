"""A股数据采集框架工具模块"""

from .logger import get_logger, FrameworkLogger
from .stock_codes import (
    StockCodeValidator,
    normalize_stock_code,
    parse_stock_code,
    validate_stock_codes,
    get_stock_codes_by_exchange,
    is_main_board_stock,
    is_growth_stock
)
from .rate_limiter import (
    RateLimiter,
    MultiSourceRateLimiter,
    rate_limited,
    RetryWithBackoff,
    retry_with_config
)
from .data_validator import DataValidator

# 尝试导入中文版工具（可选）
try:
    from .stock_codes_cn import (
        get_popular_stocks,
        get_stock_market_info,
        search_stock_by_pattern
    )
    _has_chinese_utils = True
except ImportError:
    _has_chinese_utils = False

__all__ = [
    # 日志
    'get_logger',
    'FrameworkLogger',

    # 股票代码
    'StockCodeValidator',
    'normalize_stock_code',
    'parse_stock_code',
    'validate_stock_codes',
    'get_stock_codes_by_exchange',
    'is_main_board_stock',
    'is_growth_stock',

    # 限流控制
    'RateLimiter',
    'MultiSourceRateLimiter',
    'rate_limited',
    'RetryWithBackoff',
    'retry_with_config',

    # 数据验证
    'DataValidator'
]

# 如果中文工具可用，添加到导出列表
if _has_chinese_utils:
    __all__.extend([
        'get_popular_stocks',
        'get_stock_market_info',
        'search_stock_by_pattern'
    ])

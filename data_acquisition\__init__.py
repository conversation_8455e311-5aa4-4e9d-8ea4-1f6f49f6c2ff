"""
A股数据采集框架

专为A股回测设计的综合性数据采集框架。
支持多数据源，包括akshare API和网络爬虫备用方案。

主要组件:
- DataManager: 数据操作的中央协调器
- AkshareProvider: 使用akshare库的主要数据源
- WebScrapers: 缺失数据的备用数据源
- Storage: 本地数据存储和缓存系统
- BacktestingInterface: 为回测系统提供的清洁接口

使用方法:
    from data_acquisition import DataManager

    # 初始化数据管理器
    dm = DataManager()

    # 获取股票数据
    data = dm.get_stock_data('000001.SZ', start_date='2020-01-01', end_date='2023-12-31')

    # 获取多只股票数据
    stocks = ['000001.SZ', '000002.SZ', '600000.SH']
    data = dm.get_multiple_stocks_data(stocks, start_date='2020-01-01')
"""

from .core.data_manager import DataManager
from .core.akshare_provider import AkshareProvider
from .core.web_scraper import WebScraper
from .backtesting_interface.data_interface import BacktestingDataInterface
from .utils.stock_codes import StockCodeValidator, normalize_stock_code
from .config.settings import Config

__version__ = "1.0.0"
__author__ = "A股回测框架"

# 主要导出
__all__ = [
    'DataManager',
    'AkshareProvider',
    'WebScraper',
    'BacktestingDataInterface',
    'StockCodeValidator',
    'normalize_stock_code',
    'Config'
]

# 快速访问函数
def get_data_manager(**kwargs):
    """
    快速创建DataManager实例

    参数:
        **kwargs: DataManager的配置参数

    返回:
        DataManager: 配置好的数据管理器实例
    """
    return DataManager(**kwargs)

def get_backtesting_interface(**kwargs):
    """
    快速创建BacktestingDataInterface实例

    参数:
        **kwargs: BacktestingDataInterface的配置参数

    返回:
        BacktestingDataInterface: 配置好的回测接口
    """
    return BacktestingDataInterface(**kwargs)

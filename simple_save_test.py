#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的保存测试
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from quantization.visualization.result_visualizer import ResultVisualizer, VisualizationConfig

def test_direct_save():
    """直接测试保存功能"""
    print("测试直接保存功能...")
    
    try:
        config = VisualizationConfig(
            style="seaborn",
            figure_size=(10, 6),
            interactive=False,
            save_format="png",
            dpi=300
        )
        
        visualizer = ResultVisualizer(config)
        
        # 生成模拟回测数据
        np.random.seed(42)
        n_days = 30
        dates = pd.date_range(start='2023-01-01', periods=n_days, freq='D')
        
        daily_returns = np.random.normal(0.001, 0.02, n_days)
        
        portfolio_history = []
        for i, date in enumerate(dates):
            portfolio_history.append({
                'date': date.strftime('%Y-%m-%d'),
                'daily_return': daily_returns[i],
                'portfolio_value': 1000000 * np.prod(1 + daily_returns[:i+1])
            })
        
        results = {
            'strategy_name': 'Simple Test Strategy',
            'start_date': '2023-01-01',
            'end_date': '2023-01-30',
            'portfolio_history': portfolio_history,
            'performance_metrics': {
                'total_return': 0.03,
                'annualized_return': 0.02,
                'annualized_volatility': 0.12,
                'sharpe_ratio': 0.17,
                'max_drawdown': -0.02
            }
        }
        
        # 测试create_backtest_report
        output_dir = "output/simple_test"
        print(f"输出目录: {output_dir}")
        
        # 确保目录存在
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        report_files = visualizer.create_backtest_report(results, output_dir)
        
        print(f"报告文件: {report_files}")
        
        # 检查文件是否真的存在
        for name, path in report_files.items():
            if Path(path).exists():
                size = Path(path).stat().st_size
                print(f"✓ {name}: {path} (大小: {size} bytes)")
            else:
                print(f"✗ {name}: {path} (文件不存在)")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("简单保存测试")
    print("=" * 50)
    
    success = test_direct_save()
    
    if success:
        print("\n✓ 测试成功")
    else:
        print("\n✗ 测试失败")

if __name__ == "__main__":
    main()

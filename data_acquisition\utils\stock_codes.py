"""
Stock code utilities for A-Share market
Handles Chinese stock code formats and validation
"""

import re
from typing import List, Optional, Tuple, Set
from enum import Enum

class Exchange(Enum):
    """Chinese stock exchanges"""
    SHANGHAI = "SH"
    SHENZHEN = "SZ"

class StockCodeValidator:
    """Validator for Chinese stock codes"""
    
    # Stock code patterns
    PATTERNS = {
        # Shanghai Stock Exchange
        'SH_MAIN': re.compile(r'^60[0-9]{4}$'),      # Main board: 600000-609999
        'SH_STAR': re.compile(r'^68[8-9][0-9]{3}$'), # STAR Market: 688000-689999
        'SH_BOND': re.compile(r'^11[0-9]{4}$'),      # Bonds: 110000-119999
        
        # Shenzhen Stock Exchange  
        'SZ_MAIN': re.compile(r'^00[0-9]{4}$'),      # Main board: 000000-009999
        'SZ_SME': re.compile(r'^00[2-9][0-9]{3}$'),  # SME board: 002000-009999
        'SZ_CHINEXT': re.compile(r'^30[0-9]{4}$'),   # ChiNext: 300000-309999
        'SZ_BOND': re.compile(r'^12[0-9]{4}$'),      # Bonds: 120000-129999
    }
    
    @classmethod
    def is_valid_code(cls, code: str) -> bool:
        """
        Check if a stock code is valid
        
        Args:
            code: Stock code (6 digits)
            
        Returns:
            bool: True if valid
        """
        if not code or len(code) != 6:
            return False
            
        return any(pattern.match(code) for pattern in cls.PATTERNS.values())
    
    @classmethod
    def get_exchange(cls, code: str) -> Optional[Exchange]:
        """
        Get the exchange for a stock code
        
        Args:
            code: Stock code (6 digits)
            
        Returns:
            Exchange: Exchange enum or None if invalid
        """
        if not cls.is_valid_code(code):
            return None
            
        # Shanghai patterns
        if (cls.PATTERNS['SH_MAIN'].match(code) or 
            cls.PATTERNS['SH_STAR'].match(code) or
            cls.PATTERNS['SH_BOND'].match(code)):
            return Exchange.SHANGHAI
            
        # Shenzhen patterns
        if (cls.PATTERNS['SZ_MAIN'].match(code) or 
            cls.PATTERNS['SZ_SME'].match(code) or
            cls.PATTERNS['SZ_CHINEXT'].match(code) or
            cls.PATTERNS['SZ_BOND'].match(code)):
            return Exchange.SHENZHEN
            
        return None
    
    @classmethod
    def get_market_type(cls, code: str) -> Optional[str]:
        """
        Get the market type for a stock code
        
        Args:
            code: Stock code (6 digits)
            
        Returns:
            str: Market type or None if invalid
        """
        if not cls.is_valid_code(code):
            return None
            
        for market_type, pattern in cls.PATTERNS.items():
            if pattern.match(code):
                return market_type.lower().replace('_', ' ')
                
        return None

def normalize_stock_code(code: str) -> Optional[str]:
    """
    Normalize stock code to standard format (XXXXXX.EX)
    
    Args:
        code: Stock code in various formats
        
    Returns:
        str: Normalized code or None if invalid
    """
    if not code:
        return None
        
    # Remove whitespace and convert to uppercase
    code = code.strip().upper()
    
    # Handle different input formats
    if '.' in code:
        # Format: XXXXXX.SH or XXXXXX.SZ
        parts = code.split('.')
        if len(parts) == 2:
            stock_code, exchange = parts
            if len(stock_code) == 6 and exchange in ['SH', 'SZ']:
                if StockCodeValidator.is_valid_code(stock_code):
                    return f"{stock_code}.{exchange}"
    else:
        # Format: XXXXXX (6 digits only)
        if len(code) == 6 and code.isdigit():
            exchange = StockCodeValidator.get_exchange(code)
            if exchange:
                return f"{code}.{exchange.value}"
    
    return None

def parse_stock_code(code: str) -> Optional[Tuple[str, str]]:
    """
    Parse stock code into code and exchange parts
    
    Args:
        code: Stock code in format XXXXXX.EX
        
    Returns:
        Tuple[str, str]: (stock_code, exchange) or None if invalid
    """
    normalized = normalize_stock_code(code)
    if normalized:
        return tuple(normalized.split('.'))
    return None

def validate_stock_codes(codes: List[str]) -> Tuple[List[str], List[str]]:
    """
    Validate a list of stock codes
    
    Args:
        codes: List of stock codes
        
    Returns:
        Tuple[List[str], List[str]]: (valid_codes, invalid_codes)
    """
    valid_codes = []
    invalid_codes = []
    
    for code in codes:
        normalized = normalize_stock_code(code)
        if normalized:
            valid_codes.append(normalized)
        else:
            invalid_codes.append(code)
    
    return valid_codes, invalid_codes

def get_stock_codes_by_exchange(codes: List[str]) -> dict:
    """
    Group stock codes by exchange
    
    Args:
        codes: List of stock codes
        
    Returns:
        dict: {exchange: [codes]}
    """
    result = {'SH': [], 'SZ': []}
    
    for code in codes:
        normalized = normalize_stock_code(code)
        if normalized:
            _, exchange = normalized.split('.')
            result[exchange].append(normalized)
    
    return result

def is_main_board_stock(code: str) -> bool:
    """
    Check if stock is from main board (not ChiNext, STAR, etc.)
    
    Args:
        code: Stock code
        
    Returns:
        bool: True if main board stock
    """
    parsed = parse_stock_code(code)
    if not parsed:
        return False
        
    stock_code, _ = parsed
    
    # Main board patterns
    return (StockCodeValidator.PATTERNS['SH_MAIN'].match(stock_code) or
            StockCodeValidator.PATTERNS['SZ_MAIN'].match(stock_code))

def is_growth_stock(code: str) -> bool:
    """
    Check if stock is from growth markets (ChiNext, STAR)
    
    Args:
        code: Stock code
        
    Returns:
        bool: True if growth market stock
    """
    parsed = parse_stock_code(code)
    if not parsed:
        return False
        
    stock_code, _ = parsed
    
    # Growth market patterns
    return (StockCodeValidator.PATTERNS['SH_STAR'].match(stock_code) or
            StockCodeValidator.PATTERNS['SZ_CHINEXT'].match(stock_code))

# Common stock code ranges for different markets
MARKET_RANGES = {
    'SH_MAIN': (600000, 609999),
    'SH_STAR': (688000, 689999), 
    'SZ_MAIN': (0, 9999),
    'SZ_CHINEXT': (300000, 309999),
}

def generate_stock_codes_in_range(start: int, end: int, exchange: str) -> List[str]:
    """
    Generate stock codes in a given range
    
    Args:
        start: Start code (integer)
        end: End code (integer)
        exchange: Exchange ('SH' or 'SZ')
        
    Returns:
        List[str]: List of stock codes
    """
    codes = []
    for code_int in range(start, end + 1):
        code = f"{code_int:06d}"
        if StockCodeValidator.is_valid_code(code):
            codes.append(f"{code}.{exchange}")
    
    return codes

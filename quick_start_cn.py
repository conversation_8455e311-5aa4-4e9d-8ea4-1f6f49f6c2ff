"""
A股数据采集框架 - 快速开始

这是一个简单的演示脚本，展示框架的基本功能
适合初次使用者快速了解框架能力
"""

from data_acquisition import DataManager, BacktestingDataInterface
from data_acquisition.utils.stock_codes_cn import get_popular_stocks, get_stock_market_info
from datetime import datetime, timedelta
import pandas as pd

def demo_basic_usage():
    """演示基础使用"""
    print("🚀 A股数据采集框架演示")
    print("=" * 50)
    
    # 初始化数据管理器
    print("\n📊 初始化数据管理器...")
    dm = DataManager()
    
    # 获取知名股票列表
    popular_stocks = get_popular_stocks()
    print(f"✅ 加载了 {len(popular_stocks)} 只知名股票")
    
    # 选择几只股票进行演示
    demo_stocks = list(popular_stocks.keys())[:3]
    print(f"\n🎯 演示股票: {[f'{code}({name})' for code, name in [(k, popular_stocks[k]) for k in demo_stocks]]}")
    
    # 设置时间范围（最近30天）
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)
    print(f"📅 数据期间: {start_date} 到 {end_date}")
    
    # 获取单只股票数据
    print(f"\n📈 获取单只股票数据...")
    stock_code = demo_stocks[0]
    stock_name = popular_stocks[stock_code]
    
    try:
        data = dm.get_stock_data(stock_code, start_date, end_date)
        
        if data is not None and not data.empty:
            print(f"✅ {stock_name}({stock_code}): {len(data)} 条记录")
            print(f"   📊 价格区间: {data['close'].min():.2f} - {data['close'].max():.2f}")
            print(f"   📈 最新收盘价: {data['close'].iloc[-1]:.2f}")
            print(f"   📊 平均成交量: {data['volume'].mean():.0f}")
            
            # 显示最近5天数据
            print(f"\n   最近5天数据:")
            recent_data = data.tail()
            for date, row in recent_data.iterrows():
                print(f"   {date.strftime('%Y-%m-%d')}: 收盘 {row['close']:.2f}, 成交量 {row['volume']:.0f}")
        else:
            print(f"❌ 无法获取 {stock_name} 的数据")
            
    except Exception as e:
        print(f"❌ 获取数据时出错: {e}")
    
    # 获取多只股票数据
    print(f"\n📊 批量获取多只股票数据...")
    try:
        all_data = dm.get_multiple_stocks_data(demo_stocks, start_date, end_date)
        
        print(f"✅ 成功获取 {len(all_data)} 只股票的数据:")
        for stock_code, data in all_data.items():
            stock_name = popular_stocks.get(stock_code, '未知')
            if data is not None and not data.empty:
                latest_price = data['close'].iloc[-1]
                change_pct = ((data['close'].iloc[-1] / data['close'].iloc[0]) - 1) * 100
                print(f"   📈 {stock_name}({stock_code}): 最新价 {latest_price:.2f}, 期间涨跌 {change_pct:+.2f}%")
            else:
                print(f"   ❌ {stock_name}({stock_code}): 无数据")
                
    except Exception as e:
        print(f"❌ 批量获取数据时出错: {e}")
    
    # 清理资源
    dm.cleanup()
    print(f"\n✅ 数据管理器演示完成")

def demo_backtesting_interface():
    """演示回测接口"""
    print(f"\n🔬 回测接口演示")
    print("-" * 30)
    
    # 初始化回测接口
    bt = BacktestingDataInterface()
    
    # 选择股票池
    popular_stocks = get_popular_stocks()
    stock_pool = list(popular_stocks.keys())[:4]  # 选择4只股票
    
    print(f"📊 股票池: {[f'{code}({popular_stocks[code]})' for code in stock_pool]}")
    
    # 设置时间范围
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=60)  # 2个月数据
    
    try:
        # 获取价格矩阵
        print(f"\n📈 创建价格矩阵...")
        price_matrix = bt.get_price_matrix(stock_pool, start_date, end_date, 'close')
        
        if not price_matrix.empty:
            print(f"✅ 价格矩阵: {price_matrix.shape[0]} 天 × {price_matrix.shape[1]} 只股票")
            
            # 计算相关性
            correlation = price_matrix.corr()
            print(f"\n📊 股票相关性矩阵:")
            for i, stock1 in enumerate(stock_pool):
                for j, stock2 in enumerate(stock_pool):
                    if i < j:  # 只显示上三角
                        corr_value = correlation.iloc[i, j]
                        name1 = popular_stocks[stock1][:4]
                        name2 = popular_stocks[stock2][:4]
                        print(f"   {name1} vs {name2}: {corr_value:.3f}")
            
            # 计算收益率统计
            returns_matrix = bt.get_returns_matrix(stock_pool, start_date, end_date)
            if not returns_matrix.empty:
                print(f"\n📊 收益率统计:")
                for stock in stock_pool:
                    if stock in returns_matrix.columns:
                        returns = returns_matrix[stock].dropna()
                        if len(returns) > 0:
                            mean_return = returns.mean() * 252  # 年化
                            volatility = returns.std() * (252**0.5)  # 年化波动率
                            name = popular_stocks[stock][:6]
                            print(f"   {name}: 年化收益 {mean_return:.2%}, 波动率 {volatility:.2%}")
        else:
            print(f"❌ 无法创建价格矩阵")
            
    except Exception as e:
        print(f"❌ 回测接口演示出错: {e}")
    
    # 清理资源
    bt.cleanup()
    print(f"✅ 回测接口演示完成")

def demo_stock_info():
    """演示股票信息功能"""
    print(f"\n📋 股票信息演示")
    print("-" * 30)
    
    # 获取知名股票信息
    popular_stocks = get_popular_stocks()
    demo_stocks = list(popular_stocks.keys())[:3]
    
    for stock_code in demo_stocks:
        stock_name = popular_stocks[stock_code]
        print(f"\n🏢 {stock_name} ({stock_code}):")
        
        try:
            # 获取市场信息
            market_info = get_stock_market_info(stock_code)
            if market_info:
                print(f"   📍 交易所: {market_info['exchange_name']}")
                print(f"   🏷️  板块: {market_info['market_type']}")
                print(f"   📊 主板股票: {'是' if market_info['is_main_board'] else '否'}")
                print(f"   🚀 成长板块: {'是' if market_info['is_growth_market'] else '否'}")
            else:
                print(f"   ❌ 无法获取市场信息")
                
        except Exception as e:
            print(f"   ❌ 获取信息出错: {e}")

def demo_framework_stats():
    """演示框架统计信息"""
    print(f"\n📊 框架统计信息")
    print("-" * 30)
    
    try:
        dm = DataManager()
        
        # 获取统计信息
        stats = dm.get_data_stats()
        
        print(f"🔧 数据源状态:")
        if 'providers' in stats:
            for provider_name, provider_info in stats['providers'].items():
                status = "✅ 可用" if provider_info.get('is_available', False) else "❌ 不可用"
                print(f"   {provider_name}: {status}")
        
        print(f"\n💾 缓存状态:")
        if 'cache' in stats:
            cache_stats = stats['cache']
            print(f"   启用状态: {'✅ 启用' if cache_stats.get('enabled', False) else '❌ 禁用'}")
            print(f"   缓存条目: {cache_stats.get('total_entries', 0)} 个")
            print(f"   缓存大小: {cache_stats.get('total_size_mb', 0):.1f} MB")
        
        print(f"\n📈 可用股票:")
        available_count = stats.get('available_stocks_count', 0)
        print(f"   数据库中的股票数量: {available_count} 只")
        
        dm.cleanup()
        
    except Exception as e:
        print(f"❌ 获取统计信息出错: {e}")

def main():
    """主演示函数"""
    try:
        # 运行各个演示
        demo_basic_usage()
        demo_backtesting_interface()
        demo_stock_info()
        demo_framework_stats()
        
        print(f"\n🎉 演示完成！")
        print("=" * 50)
        print(f"\n📚 接下来您可以:")
        print(f"   1. 查看完整文档: README_CN.md")
        print(f"   2. 运行详细示例: python examples/basic_usage_cn.py")
        print(f"   3. 尝试策略回测: python examples/strategy_backtest_cn.py")
        print(f"   4. 开始您的量化投资项目！")
        
    except KeyboardInterrupt:
        print(f"\n\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")
        print(f"\n🔧 故障排除:")
        print(f"   1. 确保已安装所有依赖: pip install -r requirements.txt")
        print(f"   2. 检查网络连接")
        print(f"   3. 运行测试脚本: python test_framework_cn.py")

if __name__ == "__main__":
    main()

"""
简化版创业板选股策略回测

基于您的选股条件进行快速回测演示：
1. 创业板股票 (300开头)
2. 非ST股票
3. 流通市值: 15亿 - 300亿
4. WR值 > -20 (即大于80)
5. 大单净量 > 0.4
6. 量比 > 2 (倍量)
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional
import warnings
warnings.filterwarnings('ignore')

from data_acquisition import DataManager
from data_acquisition.utils.technical_indicators import TechnicalIndicators

class SimplifiedChiNextStrategy:
    """简化版创业板选股策略"""
    
    def __init__(self):
        """初始化策略"""
        self.data_manager = DataManager()
        self.indicators = TechnicalIndicators()
        
        # 选股条件
        self.criteria = {
            'market_cap_min': 15e8,    # 15亿
            'market_cap_max': 300e8,   # 300亿
            'wr_threshold': -20,       # WR > -20
            'big_order_ratio': 0.4,    # 大单净量 > 0.4
            'volume_ratio': 2.0        # 量比 > 2
        }
        
    def get_sample_stocks(self) -> List[str]:
        """获取样本创业板股票"""
        # 使用一些代表性的创业板股票进行演示
        sample_stocks = [
            '300001.SZ', '300002.SZ', '300008.SZ', '300015.SZ', '300029.SZ',
            '300033.SZ', '300059.SZ', '300124.SZ', '300144.SZ', '300251.SZ',
            '300274.SZ', '300296.SZ', '300347.SZ', '300408.SZ', '300413.SZ',
            '300450.SZ', '300498.SZ', '300529.SZ', '300558.SZ', '300601.SZ'
        ]
        return sample_stocks
    
    def estimate_market_cap(self, stock_code: str, price: float) -> float:
        """估算流通市值"""
        # 基于股票代码估算流通股本
        code_num = int(stock_code[3:6])
        
        if code_num < 100:
            shares = (2 + (code_num % 10) * 0.5) * 1e8  # 2-6.5亿股
        elif code_num < 500:
            shares = (1.5 + (code_num % 20) * 0.2) * 1e8  # 1.5-5.3亿股
        else:
            shares = (1 + (code_num % 30) * 0.1) * 1e8  # 1-4亿股
        
        return price * shares
    
    def simulate_big_order_ratio(self, volume: float, price_change: float) -> float:
        """模拟大单净量比例"""
        # 基于成交量和价格变动模拟大单净量
        base_ratio = 0.3
        volume_factor = min(volume / 1e6, 2.0) * 0.1  # 成交量因子
        price_factor = max(price_change, 0) * 0.5     # 价格上涨因子
        
        return base_ratio + volume_factor + price_factor
    
    def screen_stocks(self, date: str, stock_pool: List[str]) -> List[str]:
        """筛选符合条件的股票"""
        selected_stocks = []
        
        print(f"\n📅 {date} 选股筛选...")
        
        for stock_code in stock_pool:
            try:
                # 获取股票数据
                end_date = pd.to_datetime(date)
                start_date = end_date - timedelta(days=60)
                
                data = self.data_manager.get_stock_data(
                    stock_code, 
                    start_date.strftime('%Y-%m-%d'), 
                    end_date.strftime('%Y-%m-%d')
                )
                
                if data is None or data.empty or len(data) < 20:
                    continue
                
                # 计算技术指标
                data_with_indicators = self.indicators.calculate_all_indicators(data)
                
                if len(data_with_indicators) == 0:
                    continue
                
                # 获取最新数据
                current_data = data.iloc[-1]
                current_indicators = data_with_indicators.iloc[-1]
                
                # 条件1: 流通市值检查
                close_price = float(current_data['close'])
                market_cap = self.estimate_market_cap(stock_code, close_price)
                
                if not (self.criteria['market_cap_min'] <= market_cap <= self.criteria['market_cap_max']):
                    continue
                
                # 条件2: WR值检查
                if 'williams_r' not in data_with_indicators.columns or pd.isna(current_indicators['williams_r']):
                    continue
                
                wr_value = float(current_indicators['williams_r'])
                if wr_value <= self.criteria['wr_threshold']:
                    continue
                
                # 条件3: 量比检查
                avg_volume = float(data['volume'].rolling(20).mean().iloc[-1])
                current_volume = float(current_data['volume'])
                
                if avg_volume <= 0:
                    continue
                    
                volume_ratio = current_volume / avg_volume
                if volume_ratio <= self.criteria['volume_ratio']:
                    continue
                
                # 条件4: 大单净量检查（模拟）
                price_change = (current_data['close'] - current_data['open']) / current_data['open']
                big_order_ratio = self.simulate_big_order_ratio(current_volume, price_change)
                
                if big_order_ratio <= self.criteria['big_order_ratio']:
                    continue
                
                # 所有条件满足
                selected_stocks.append(stock_code)
                print(f"  ✅ {stock_code}: 市值{market_cap/1e8:.1f}亿, WR{wr_value:.1f}, 量比{volume_ratio:.1f}, 大单{big_order_ratio:.2f}")
                
                # 限制选股数量
                if len(selected_stocks) >= 10:
                    break
                    
            except Exception as e:
                continue
        
        print(f"筛选结果: {len(selected_stocks)} 只股票")
        return selected_stocks
    
    def calculate_portfolio_return(self, stocks: List[str], start_date: str, end_date: str) -> float:
        """计算投资组合收益率"""
        if not stocks:
            return 0.0
        
        returns = []
        
        for stock_code in stocks:
            try:
                data = self.data_manager.get_stock_data(stock_code, start_date, end_date)
                
                if data is not None and len(data) >= 2:
                    start_price = float(data.iloc[0]['close'])
                    end_price = float(data.iloc[-1]['close'])
                    stock_return = (end_price - start_price) / start_price
                    returns.append(stock_return)
            except:
                continue
        
        if returns:
            return float(np.mean(returns))  # 等权重平均
        else:
            return 0.0
    
    def backtest(self, start_date: str, end_date: str) -> Dict:
        """执行回测"""
        print(f"🚀 创业板选股策略回测")
        print(f"回测期间: {start_date} 到 {end_date}")
        print(f"选股条件:")
        print(f"  - 创业板股票 (300开头)")
        print(f"  - 流通市值: 15-300亿")
        print(f"  - WR值 > -20")
        print(f"  - 大单净量 > 0.4")
        print(f"  - 量比 > 2")
        
        # 获取股票池
        stock_pool = self.get_sample_stocks()
        print(f"样本股票池: {len(stock_pool)} 只")
        
        # 生成交易日期（每周选股一次）
        date_range = pd.date_range(start=start_date, end=end_date, freq='W')
        
        portfolio_returns = []
        daily_selections = {}
        
        for i, current_date in enumerate(date_range[:-1]):
            date_str = current_date.strftime('%Y-%m-%d')
            next_date_str = date_range[i + 1].strftime('%Y-%m-%d')
            
            # 选股
            selected_stocks = self.screen_stocks(date_str, stock_pool)
            daily_selections[date_str] = selected_stocks
            
            if selected_stocks:
                # 计算下周收益率
                weekly_return = self.calculate_portfolio_return(
                    selected_stocks, date_str, next_date_str
                )
                portfolio_returns.append(weekly_return)
            else:
                portfolio_returns.append(0.0)
        
        # 计算回测指标
        returns_array = np.array(portfolio_returns, dtype=float)
        
        # 基本统计
        total_return = float(np.prod(1 + returns_array) - 1)
        annual_return = float((1 + total_return) ** (52 / len(returns_array)) - 1) if len(returns_array) > 0 else 0.0
        volatility = float(np.std(returns_array) * np.sqrt(52))
        sharpe_ratio = float(annual_return / volatility) if volatility > 0 else 0.0
        
        # 最大回撤
        cumulative_returns = np.cumprod(1 + returns_array)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = float(np.min(drawdown))
        
        # 胜率
        win_rate = float(np.mean(returns_array > 0))
        
        # 选股统计
        selection_counts = [len(stocks) for stocks in daily_selections.values()]
        avg_selection_count = float(np.mean(selection_counts)) if selection_counts else 0.0
        
        results = {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'avg_selection_count': avg_selection_count,
            'total_periods': len(returns_array),
            'weekly_returns': returns_array,
            'daily_selections': daily_selections,
            'cumulative_returns': cumulative_returns
        }
        
        return results
    
    def print_results(self, results: Dict):
        """打印回测结果"""
        print(f"\n" + "="*60)
        print(f"📊 创业板选股策略回测结果")
        print(f"="*60)
        
        print(f"📈 收益指标:")
        print(f"  总收益率:     {results['total_return']:.2%}")
        print(f"  年化收益率:   {results['annual_return']:.2%}")
        print(f"  年化波动率:   {results['volatility']:.2%}")
        print(f"  夏普比率:     {results['sharpe_ratio']:.2f}")
        
        print(f"\n📉 风险指标:")
        print(f"  最大回撤:     {results['max_drawdown']:.2%}")
        print(f"  胜率:         {results['win_rate']:.2%}")
        
        print(f"\n📊 选股统计:")
        print(f"  平均选股数量: {results['avg_selection_count']:.1f} 只")
        print(f"  调仓周期:     {results['total_periods']} 周")
        
        # 显示选股记录
        print(f"\n📋 选股记录样例:")
        selection_items = list(results['daily_selections'].items())
        for date, stocks in selection_items[:3]:
            print(f"  {date}: {len(stocks)} 只股票 {stocks[:3]}{'...' if len(stocks) > 3 else ''}")
        
        # 基准比较
        print(f"\n📈 策略表现分析:")
        if results['annual_return'] > 0:
            print(f"  ✅ 策略年化收益率为正: {results['annual_return']:.2%}")
        else:
            print(f"  ❌ 策略年化收益率为负: {results['annual_return']:.2%}")
            
        if results['sharpe_ratio'] > 1:
            print(f"  ✅ 夏普比率优秀: {results['sharpe_ratio']:.2f}")
        elif results['sharpe_ratio'] > 0.5:
            print(f"  ⚠️  夏普比率一般: {results['sharpe_ratio']:.2f}")
        else:
            print(f"  ❌ 夏普比率较差: {results['sharpe_ratio']:.2f}")
    
    def cleanup(self):
        """清理资源"""
        if hasattr(self, 'data_manager'):
            self.data_manager.cleanup()

def main():
    """主函数"""
    strategy = SimplifiedChiNextStrategy()
    
    try:
        # 设置回测参数（最近2个月）
        end_date = date.today()
        start_date = end_date - timedelta(days=60)
        
        # 执行回测
        results = strategy.backtest(
            start_date.strftime('%Y-%m-%d'),
            end_date.strftime('%Y-%m-%d')
        )
        
        # 打印结果
        strategy.print_results(results)
        
        # 创建基准比较
        print(f"\n💡 策略说明:")
        print(f"  本策略基于技术指标和资金流向进行选股")
        print(f"  适合短期交易和波段操作")
        print(f"  建议结合市场环境和个股基本面分析")
        
    except Exception as e:
        print(f"回测过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        strategy.cleanup()

if __name__ == "__main__":
    main()

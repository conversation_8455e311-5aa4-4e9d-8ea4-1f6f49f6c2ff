#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户体验和接口优化演示

独立的演示脚本，不依赖Streamlit，展示核心功能。
"""

import sys
import os
import time
import json
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

# 导入量化交易模块
from quantization.utils.logger import get_logger
from quantization.config.config_manager import ConfigManager, ConfigSchema
from quantization.visualization.result_visualizer import ResultVisualizer, VisualizationConfig


def demonstrate_config_management():
    """演示配置管理功能"""
    logger = get_logger("ConfigDemo")
    logger.info("开始配置管理演示")
    
    # 1. 基本配置操作
    logger.info("1. 基本配置操作")
    
    config_manager = ConfigManager()
    
    # 设置配置
    config_manager.set("demo.test_value", 42)
    config_manager.set("demo.test_string", "Hello World")
    config_manager.set("demo.nested.value", {"key": "value"})
    
    # 获取配置
    test_value = config_manager.get("demo.test_value")
    test_string = config_manager.get("demo.test_string")
    nested_value = config_manager.get("demo.nested.value")
    
    logger.info(f"test_value: {test_value}")
    logger.info(f"test_string: {test_string}")
    logger.info(f"nested_value: {nested_value}")
    
    # 2. 配置模式验证
    logger.info("2. 配置模式验证")
    
    # 添加配置模式
    schema = ConfigSchema(
        key="demo.validated_number",
        data_type=int,
        default_value=10,
        required=True,
        description="验证的数字配置",
        min_value=1,
        max_value=100
    )
    config_manager.add_schema(schema)
    
    # 设置有效值
    success = config_manager.set("demo.validated_number", 50, validate=True)
    logger.info(f"设置有效值 50: {success}")
    
    # 尝试设置无效值
    success = config_manager.set("demo.validated_number", 150, validate=True)
    logger.info(f"设置无效值 150: {success}")
    
    # 3. 配置变更监听
    logger.info("3. 配置变更监听")
    
    def config_change_handler(event):
        logger.info(f"配置变更: {event.key} = {event.old_value} -> {event.new_value}")
    
    # 添加监听器
    config_manager.add_change_listener("demo.*", config_change_handler)
    
    # 触发变更
    config_manager.set("demo.monitored_value", "initial")
    config_manager.set("demo.monitored_value", "changed")
    
    # 4. 导出配置
    logger.info("4. 导出配置")
    
    output_dir = Path("output/config")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    config_manager.export_config("output/config/demo_config.json")
    logger.info("配置已导出到 output/config/demo_config.json")


def demonstrate_visualization():
    """演示可视化功能"""
    logger = get_logger("VisualizationDemo")
    logger.info("开始可视化演示")
    
    # 创建可视化配置
    viz_config = VisualizationConfig(
        style="seaborn",
        figure_size=(12, 8),
        interactive=True,
        save_format="png"
    )
    
    # 创建可视化器
    visualizer = ResultVisualizer(viz_config)
    
    # 生成模拟回测数据
    logger.info("生成模拟回测数据")
    
    np.random.seed(42)
    n_days = 252
    dates = pd.date_range(start='2023-01-01', periods=n_days, freq='D')
    
    # 模拟日收益率
    daily_returns = np.random.normal(0.0008, 0.02, n_days)
    cumulative_returns = np.cumprod(1 + daily_returns)
    
    # 构建回测结果数据
    portfolio_history = []
    for i, date in enumerate(dates):
        portfolio_history.append({
            'date': date.strftime('%Y-%m-%d'),
            'daily_return': daily_returns[i],
            'cumulative_return': cumulative_returns[i] - 1,
            'portfolio_value': 1000000 * cumulative_returns[i]
        })
    
    # 计算性能指标
    total_return = cumulative_returns[-1] - 1
    annualized_return = np.mean(daily_returns) * 252
    volatility = np.std(daily_returns) * np.sqrt(252)
    sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
    
    # 计算最大回撤
    running_max = np.maximum.accumulate(cumulative_returns)
    drawdown = (cumulative_returns - running_max) / running_max
    max_drawdown = np.min(drawdown)
    
    results = {
        'strategy_name': 'Demo Strategy',
        'start_date': '2023-01-01',
        'end_date': '2023-12-31',
        'portfolio_history': portfolio_history,
        'performance_metrics': {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'annualized_volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': np.mean(daily_returns > 0),
            'calmar_ratio': annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
        }
    }
    
    # 创建回测报告
    logger.info("创建回测报告")
    
    output_dir = "output/visualization"
    report_files = visualizer.create_backtest_report(results, output_dir)
    
    logger.info(f"回测报告已生成，包含以下图表:")
    for chart_type, file_path in report_files.items():
        logger.info(f"  {chart_type}: {file_path}")
    
    # 创建投资组合报告
    logger.info("创建投资组合报告")
    
    portfolio_data = {
        'positions': {
            '000001.SZ': 0.15,
            '000002.SZ': 0.12,
            '600000.SH': 0.18,
            '600036.SH': 0.10,
            '000858.SZ': 0.08,
            '其他': 0.37
        },
        'sector_allocation': {
            '金融': 35.2,
            '科技': 28.5,
            '消费': 18.3,
            '医药': 12.1,
            '其他': 5.9
        }
    }
    
    portfolio_files = visualizer.create_portfolio_report(portfolio_data, output_dir)
    
    logger.info(f"投资组合报告已生成，包含以下图表:")
    for chart_type, file_path in portfolio_files.items():
        logger.info(f"  {chart_type}: {file_path}")


def demonstrate_api_info():
    """演示API信息"""
    logger = get_logger("APIDemo")
    logger.info("开始API信息演示")
    
    try:
        from quantization.api.api_server import QuantizationAPI
        
        logger.info("API服务器类已加载")
        logger.info("可用的API端点:")
        logger.info("  GET /api/v1/strategies - 获取策略列表")
        logger.info("  POST /api/v1/strategies - 创建新策略")
        logger.info("  POST /api/v1/backtest - 执行回测")
        logger.info("  GET /api/v1/backtest/{task_id} - 获取回测结果")
        logger.info("  GET /api/v1/data/stocks/{symbol} - 获取股票数据")
        logger.info("  GET /api/v1/config - 获取配置")
        logger.info("  PUT /api/v1/config - 更新配置")
        
        logger.info("启动API服务器的方法:")
        logger.info("  from quantization.api import api_server")
        logger.info("  api_server.run(host='0.0.0.0', port=8000)")
        
    except Exception as e:
        logger.error(f"API模块加载失败: {e}")


def create_sample_config_files():
    """创建示例配置文件"""
    logger = get_logger("ConfigFiles")
    logger.info("创建示例配置文件")
    
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    # 全局配置
    global_config = {
        "data": {
            "akshare": {
                "enabled": True,
                "timeout": 30
            },
            "cache": {
                "enabled": True,
                "ttl": 3600
            }
        },
        "database": {
            "host": "localhost",
            "port": 3306,
            "name": "quantization",
            "pool_size": 10
        }
    }
    
    # 开发环境配置
    development_config = {
        "system": {
            "log_level": "DEBUG",
            "max_workers": 4
        },
        "api": {
            "debug": True,
            "host": "127.0.0.1",
            "port": 8000
        }
    }
    
    # 生产环境配置
    production_config = {
        "system": {
            "log_level": "INFO",
            "max_workers": 8
        },
        "api": {
            "debug": False,
            "host": "0.0.0.0",
            "port": 8000
        }
    }
    
    # 保存配置文件
    with open(config_dir / "global.json", 'w', encoding='utf-8') as f:
        json.dump(global_config, f, ensure_ascii=False, indent=2)
    
    with open(config_dir / "development.json", 'w', encoding='utf-8') as f:
        json.dump(development_config, f, ensure_ascii=False, indent=2)
    
    with open(config_dir / "production.json", 'w', encoding='utf-8') as f:
        json.dump(production_config, f, ensure_ascii=False, indent=2)
    
    logger.info("示例配置文件已创建:")
    logger.info(f"  {config_dir / 'global.json'}")
    logger.info(f"  {config_dir / 'development.json'}")
    logger.info(f"  {config_dir / 'production.json'}")


def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "=" * 60)
    print("使用说明")
    print("=" * 60)
    
    print("\n1. 配置管理:")
    print("   - 配置文件已创建在 config/ 目录下")
    print("   - 可以通过ConfigManager类进行配置管理")
    print("   - 支持配置验证、变更监听和热更新")
    
    print("\n2. 可视化功能:")
    print("   - 图表已保存在 output/visualization/ 目录下")
    print("   - 支持净值曲线、收益分布、风险指标等图表")
    print("   - 可以使用matplotlib或plotly进行渲染")
    
    print("\n3. API服务器:")
    print("   - 启动命令: python -c \"from quantization.api import api_server; api_server.run()\"")
    print("   - API文档: http://localhost:8000/docs")
    print("   - 支持策略管理、回测执行、数据查询等功能")
    
    print("\n4. Web界面:")
    print("   - 启动命令: streamlit run examples/ui_optimization_example.py")
    print("   - 提供完整的Web界面进行系统操作")
    print("   - 包含策略管理、回测执行、结果分析等页面")
    
    print("\n5. 依赖安装:")
    print("   - 基础依赖: pip install pandas numpy")
    print("   - 可视化: pip install matplotlib seaborn plotly")
    print("   - API服务: pip install fastapi uvicorn")
    print("   - Web界面: pip install streamlit")
    print("   - 配置文件: pip install pyyaml toml")


def main():
    """主函数"""
    print("=" * 60)
    print("用户体验和接口优化演示")
    print("=" * 60)
    
    # 创建示例配置文件
    print("\n0. 创建示例配置文件")
    print("-" * 30)
    create_sample_config_files()
    
    # 配置管理演示
    print("\n1. 配置管理演示")
    print("-" * 30)
    demonstrate_config_management()
    
    # 可视化演示
    print("\n2. 可视化演示")
    print("-" * 30)
    demonstrate_visualization()
    
    # API信息演示
    print("\n3. API信息演示")
    print("-" * 30)
    demonstrate_api_info()
    
    # 显示使用说明
    show_usage_instructions()
    
    print("\n演示完成！")


if __name__ == "__main__":
    main()

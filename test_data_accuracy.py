#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据真实性验证测试脚本

验证修复后的大单净量和市值计算的准确性
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional

# 添加项目路径
sys.path.append('.')

try:
    from stock_selection_strategy import ChiNextSelectionStrategy
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)


class DataAccuracyValidator:
    """数据准确性验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.strategy = ChiNextSelectionStrategy()
        self.data_manager = self.strategy.data_manager
        self.test_stocks = [
            '300001.SZ',  # 特锐德
            '300002.SZ',  # 神州泰岳
            '300015.SZ',  # 爱尔眼科
            '300059.SZ',  # 东方财富
            '300750.SZ'   # 宁德时代
        ]
        
    def test_market_cap_accuracy(self) -> Dict:
        """测试市值计算准确性"""
        print("\n🔍 测试市值计算准确性...")
        
        results = {
            'total_tested': 0,
            'successful': 0,
            'failed': 0,
            'details': []
        }
        
        test_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        for stock_code in self.test_stocks:
            try:
                print(f"  测试股票: {stock_code}")
                
                # 获取股票数据
                data = self.data_manager.get_stock_data(stock_code, test_date, test_date)
                if data is None or data.empty:
                    print(f"    ❌ 无法获取数据")
                    results['failed'] += 1
                    continue
                
                current_price = float(data.iloc[-1]['close'])
                
                # 测试市值计算
                market_cap = self.strategy.calculate_market_cap(stock_code, current_price, test_date)
                
                if market_cap is not None and market_cap > 0:
                    market_cap_billion = market_cap / 1e8  # 转换为亿元
                    print(f"    ✅ 流通市值: {market_cap_billion:.2f}亿元")
                    
                    # 合理性检查
                    if 1.5 <= market_cap_billion <= 3000:  # 创业板合理范围
                        results['successful'] += 1
                        results['details'].append({
                            'stock_code': stock_code,
                            'market_cap_billion': market_cap_billion,
                            'price': current_price,
                            'status': 'success'
                        })
                    else:
                        print(f"    ⚠️  市值超出合理范围")
                        results['failed'] += 1
                        results['details'].append({
                            'stock_code': stock_code,
                            'market_cap_billion': market_cap_billion,
                            'price': current_price,
                            'status': 'out_of_range'
                        })
                else:
                    print(f"    ❌ 市值计算失败")
                    results['failed'] += 1
                    results['details'].append({
                        'stock_code': stock_code,
                        'market_cap_billion': None,
                        'price': current_price,
                        'status': 'failed'
                    })
                
                results['total_tested'] += 1
                
            except Exception as e:
                print(f"    ❌ 测试异常: {e}")
                results['failed'] += 1
                results['total_tested'] += 1
        
        return results
    
    def test_big_order_ratio_accuracy(self) -> Dict:
        """测试大单净量计算准确性"""
        print("\n🔍 测试大单净量计算准确性...")
        
        results = {
            'total_tested': 0,
            'successful': 0,
            'failed': 0,
            'details': []
        }
        
        test_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        for stock_code in self.test_stocks:
            try:
                print(f"  测试股票: {stock_code}")
                
                # 测试大单净量计算
                big_order_ratio = self.strategy.calculate_big_order_net_ratio(stock_code, test_date)
                
                if big_order_ratio is not None:
                    print(f"    ✅ 大单净量比例: {big_order_ratio:.4f}")
                    
                    # 合理性检查：应该在-1到1之间
                    if -1.0 <= big_order_ratio <= 1.0:
                        results['successful'] += 1
                        results['details'].append({
                            'stock_code': stock_code,
                            'big_order_ratio': big_order_ratio,
                            'status': 'success'
                        })
                    else:
                        print(f"    ⚠️  大单净量比例超出合理范围")
                        results['failed'] += 1
                        results['details'].append({
                            'stock_code': stock_code,
                            'big_order_ratio': big_order_ratio,
                            'status': 'out_of_range'
                        })
                else:
                    print(f"    ❌ 大单净量计算失败")
                    results['failed'] += 1
                    results['details'].append({
                        'stock_code': stock_code,
                        'big_order_ratio': None,
                        'status': 'failed'
                    })
                
                results['total_tested'] += 1
                
            except Exception as e:
                print(f"    ❌ 测试异常: {e}")
                results['failed'] += 1
                results['total_tested'] += 1
        
        return results
    
    def test_data_consistency(self) -> Dict:
        """测试数据一致性"""
        print("\n🔍 测试数据一致性...")
        
        results = {
            'total_tested': 0,
            'consistent': 0,
            'inconsistent': 0,
            'details': []
        }
        
        test_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        for stock_code in self.test_stocks:
            try:
                print(f"  测试股票: {stock_code}")
                
                # 多次调用同一方法，检查结果一致性
                market_caps = []
                big_order_ratios = []
                
                # 获取价格
                data = self.data_manager.get_stock_data(stock_code, test_date, test_date)
                if data is None or data.empty:
                    continue
                
                current_price = float(data.iloc[-1]['close'])
                
                # 多次调用测试一致性
                for _ in range(3):
                    market_cap = self.strategy.calculate_market_cap(stock_code, current_price, test_date)
                    big_order_ratio = self.strategy.calculate_big_order_net_ratio(stock_code, test_date)
                    
                    if market_cap is not None:
                        market_caps.append(market_cap)
                    if big_order_ratio is not None:
                        big_order_ratios.append(big_order_ratio)
                
                # 检查一致性
                market_cap_consistent = len(set(market_caps)) <= 1 if market_caps else True
                big_order_consistent = all(abs(x - big_order_ratios[0]) < 0.1 for x in big_order_ratios) if big_order_ratios else True
                
                if market_cap_consistent and big_order_consistent:
                    print(f"    ✅ 数据一致性良好")
                    results['consistent'] += 1
                else:
                    print(f"    ⚠️  数据一致性问题")
                    results['inconsistent'] += 1
                
                results['details'].append({
                    'stock_code': stock_code,
                    'market_cap_consistent': market_cap_consistent,
                    'big_order_consistent': big_order_consistent,
                    'market_caps': market_caps,
                    'big_order_ratios': big_order_ratios
                })
                
                results['total_tested'] += 1
                
            except Exception as e:
                print(f"    ❌ 测试异常: {e}")
                results['inconsistent'] += 1
                results['total_tested'] += 1
        
        return results
    
    def run_full_validation(self) -> Dict:
        """运行完整验证"""
        print("🚀 开始数据真实性验证...")
        print("=" * 60)
        
        # 运行各项测试
        market_cap_results = self.test_market_cap_accuracy()
        big_order_results = self.test_big_order_ratio_accuracy()
        consistency_results = self.test_data_consistency()
        
        # 汇总结果
        total_results = {
            'market_cap': market_cap_results,
            'big_order': big_order_results,
            'consistency': consistency_results,
            'summary': {
                'total_tests': (
                    market_cap_results['total_tested'] + 
                    big_order_results['total_tested'] + 
                    consistency_results['total_tested']
                ),
                'total_success': (
                    market_cap_results['successful'] + 
                    big_order_results['successful'] + 
                    consistency_results['consistent']
                ),
                'total_failed': (
                    market_cap_results['failed'] + 
                    big_order_results['failed'] + 
                    consistency_results['inconsistent']
                )
            }
        }
        
        return total_results
    
    def print_summary(self, results: Dict):
        """打印验证结果摘要"""
        print("\n" + "=" * 60)
        print("📊 验证结果摘要")
        print("=" * 60)
        
        summary = results['summary']
        success_rate = (summary['total_success'] / summary['total_tests'] * 100) if summary['total_tests'] > 0 else 0
        
        print(f"总测试数: {summary['total_tests']}")
        print(f"成功数: {summary['total_success']}")
        print(f"失败数: {summary['total_failed']}")
        print(f"成功率: {success_rate:.1f}%")
        
        print(f"\n📈 市值计算: {results['market_cap']['successful']}/{results['market_cap']['total_tested']} 成功")
        print(f"💰 大单净量: {results['big_order']['successful']}/{results['big_order']['total_tested']} 成功")
        print(f"🔄 数据一致性: {results['consistency']['consistent']}/{results['consistency']['total_tested']} 一致")
        
        if success_rate >= 80:
            print(f"\n✅ 验证通过！数据真实性修复成功")
        elif success_rate >= 60:
            print(f"\n⚠️  验证部分通过，仍有改进空间")
        else:
            print(f"\n❌ 验证未通过，需要进一步修复")


def main():
    """主函数"""
    try:
        validator = DataAccuracyValidator()
        results = validator.run_full_validation()
        validator.print_summary(results)
        
        return results['summary']['total_success'] >= results['summary']['total_tests'] * 0.8
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

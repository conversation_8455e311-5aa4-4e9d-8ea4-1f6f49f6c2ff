"""
Basic usage examples for A-Share Data Acquisition Framework
"""

import pandas as pd
from datetime import datetime, date, timedelta
from data_acquisition import DataManager, BacktestingDataInterface
from data_acquisition.config import Config

def example_basic_data_acquisition():
    """Example: Basic data acquisition"""
    print("=== Basic Data Acquisition Example ===")
    
    # Initialize data manager
    dm = DataManager()
    
    # Get data for a single stock
    stock_code = "000001.SZ"  # Ping An Bank
    start_date = "2023-01-01"
    end_date = "2023-12-31"
    
    print(f"Fetching data for {stock_code} from {start_date} to {end_date}")
    
    data = dm.get_stock_data(stock_code, start_date, end_date)
    
    if data is not None:
        print(f"Successfully retrieved {len(data)} records")
        print("\nFirst 5 records:")
        print(data.head())
        print("\nData columns:", data.columns.tolist())
    else:
        print("Failed to retrieve data")
    
    # Cleanup
    dm.cleanup()

def example_multiple_stocks():
    """Example: Get data for multiple stocks"""
    print("\n=== Multiple Stocks Example ===")
    
    dm = DataManager()
    
    # Define stock universe
    stock_codes = [
        "000001.SZ",  # Ping An Bank
        "000002.SZ",  # Vanke
        "600000.SH",  # Pudong Development Bank
        "600036.SH",  # China Merchants Bank
        "600519.SH"   # Kweichow Moutai
    ]
    
    start_date = "2023-06-01"
    end_date = "2023-12-31"
    
    print(f"Fetching data for {len(stock_codes)} stocks")
    
    all_data = dm.get_multiple_stocks_data(stock_codes, start_date, end_date)
    
    print(f"Successfully retrieved data for {len(all_data)} stocks")
    
    for stock_code, data in all_data.items():
        if data is not None:
            print(f"{stock_code}: {len(data)} records")
        else:
            print(f"{stock_code}: No data")
    
    dm.cleanup()

def example_backtesting_interface():
    """Example: Using backtesting interface"""
    print("\n=== Backtesting Interface Example ===")
    
    # Initialize backtesting interface
    bt_interface = BacktestingDataInterface()
    
    # Define parameters
    stock_codes = ["000001.SZ", "000002.SZ", "600000.SH"]
    start_date = "2023-01-01"
    end_date = "2023-12-31"
    
    # Get price matrix (useful for portfolio backtesting)
    print("Creating price matrix...")
    price_matrix = bt_interface.get_price_matrix(stock_codes, start_date, end_date, 'close')
    
    if not price_matrix.empty:
        print(f"Price matrix shape: {price_matrix.shape}")
        print("\nFirst 5 rows:")
        print(price_matrix.head())
    
    # Get returns matrix
    print("\nCalculating returns matrix...")
    returns_matrix = bt_interface.get_returns_matrix(stock_codes, start_date, end_date)
    
    if not returns_matrix.empty:
        print(f"Returns matrix shape: {returns_matrix.shape}")
        print("\nReturns statistics:")
        print(returns_matrix.describe())
    
    # Get market data
    print("\nFetching market indices...")
    market_data = bt_interface.get_market_data(start_date, end_date)
    
    for index_code, data in market_data.items():
        if data is not None:
            print(f"{index_code}: {len(data)} records")
    
    bt_interface.cleanup()

def example_data_validation():
    """Example: Data validation and cleaning"""
    print("\n=== Data Validation Example ===")
    
    dm = DataManager()
    
    # Get some data
    stock_code = "000001.SZ"
    data = dm.get_stock_data(stock_code, "2023-01-01", "2023-12-31")
    
    if data is not None:
        # Validate the data
        validator = dm.validator
        is_valid, errors = validator.validate_price_data(data, stock_code)
        
        print(f"Data validation for {stock_code}:")
        print(f"Valid: {is_valid}")
        if errors:
            print("Validation errors:")
            for error in errors:
                print(f"  - {error}")
        
        # Clean the data
        cleaned_data = validator.clean_data(data, stock_code)
        print(f"Original data: {len(data)} records")
        print(f"Cleaned data: {len(cleaned_data)} records")
    
    dm.cleanup()

def example_caching():
    """Example: Caching functionality"""
    print("\n=== Caching Example ===")
    
    dm = DataManager()
    
    stock_code = "000001.SZ"
    start_date = "2023-01-01"
    end_date = "2023-12-31"
    
    # First call - will fetch from source and cache
    print("First call (will cache data)...")
    start_time = datetime.now()
    data1 = dm.get_stock_data(stock_code, start_date, end_date, use_cache=True)
    time1 = (datetime.now() - start_time).total_seconds()
    print(f"Time taken: {time1:.2f} seconds")
    
    # Second call - will use cached data
    print("Second call (will use cache)...")
    start_time = datetime.now()
    data2 = dm.get_stock_data(stock_code, start_date, end_date, use_cache=True)
    time2 = (datetime.now() - start_time).total_seconds()
    print(f"Time taken: {time2:.2f} seconds")
    
    print(f"Speed improvement: {time1/time2:.1f}x faster")
    
    # Check cache statistics
    cache_stats = dm.cache.get_cache_stats()
    print("\nCache statistics:")
    for key, value in cache_stats.items():
        print(f"  {key}: {value}")
    
    dm.cleanup()

def example_configuration():
    """Example: Custom configuration"""
    print("\n=== Configuration Example ===")
    
    # Create custom configuration
    config = Config()
    
    # Modify settings
    config.CACHE_ENABLED = True
    config.CACHE_TTL_DAYS = 1  # Cache for 1 day only
    config.AKSHARE_RATE_LIMIT = 0.1  # Faster rate limit for testing
    
    print("Custom configuration:")
    print(f"  Cache enabled: {config.CACHE_ENABLED}")
    print(f"  Cache TTL: {config.CACHE_TTL_DAYS} days")
    print(f"  Akshare rate limit: {config.AKSHARE_RATE_LIMIT} seconds")
    
    # Use custom config
    dm = DataManager(config)
    
    # Get data stats
    stats = dm.get_data_stats()
    print("\nData manager statistics:")
    for key, value in stats.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for sub_key, sub_value in value.items():
                print(f"    {sub_key}: {sub_value}")
        else:
            print(f"  {key}: {value}")
    
    dm.cleanup()

def example_stock_info():
    """Example: Getting stock information"""
    print("\n=== Stock Information Example ===")
    
    dm = DataManager()
    
    stock_codes = ["000001.SZ", "600519.SH", "000858.SZ"]
    
    for stock_code in stock_codes:
        print(f"\nGetting info for {stock_code}...")
        info = dm.get_stock_info(stock_code)
        
        if info:
            print(f"  Name: {info.get('name', 'N/A')}")
            print(f"  Exchange: {info.get('exchange', 'N/A')}")
            print(f"  Sector: {info.get('sector', 'N/A')}")
            print(f"  Market Cap: {info.get('market_cap', 'N/A')}")
        else:
            print(f"  No information available")
    
    dm.cleanup()

if __name__ == "__main__":
    """Run all examples"""
    
    print("A-Share Data Acquisition Framework - Examples")
    print("=" * 50)
    
    try:
        # Run examples
        example_basic_data_acquisition()
        example_multiple_stocks()
        example_backtesting_interface()
        example_data_validation()
        example_caching()
        example_configuration()
        example_stock_info()
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        
    except Exception as e:
        print(f"\nError running examples: {e}")
        import traceback
        traceback.print_exc()

"""
标准技术指标计算模块

使用标准金融公式计算各种技术指标，确保计算准确性和一致性。
所有公式均符合行业标准，无任何简化或近似计算。
"""

import pandas as pd
import numpy as np
from typing import Optional, Union, Tuple
from .logger import get_logger

class TechnicalIndicators:
    """标准技术指标计算器"""
    
    def __init__(self):
        """初始化技术指标计算器"""
        self.logger = get_logger('technical_indicators')
    
    def sma(self, data: pd.Series, window: int) -> pd.Series:
        """
        简单移动平均线 (Simple Moving Average)
        
        公式: SMA = (P1 + P2 + ... + Pn) / n
        
        参数:
            data: 价格序列
            window: 窗口期
            
        返回:
            pd.Series: SMA值
        """
        return data.rolling(window=window, min_periods=window).mean()
    
    def ema(self, data: pd.Series, span: int) -> pd.Series:
        """
        指数移动平均线 (Exponential Moving Average)
        
        公式: EMA = (价格 × 2/(n+1)) + (前一日EMA × (n-1)/(n+1))
        
        参数:
            data: 价格序列
            span: 跨度期
            
        返回:
            pd.Series: EMA值
        """
        return data.ewm(span=span, adjust=False).mean()
    
    def macd(self, data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        MACD指标 (Moving Average Convergence Divergence)
        
        公式:
        - MACD线 = EMA(12) - EMA(26)
        - 信号线 = EMA(MACD, 9)
        - 柱状图 = MACD线 - 信号线
        
        参数:
            data: 价格序列
            fast: 快线周期
            slow: 慢线周期
            signal: 信号线周期
            
        返回:
            Tuple[pd.Series, pd.Series, pd.Series]: (MACD线, 信号线, 柱状图)
        """
        ema_fast = self.ema(data, fast)
        ema_slow = self.ema(data, slow)
        
        macd_line = ema_fast - ema_slow
        signal_line = self.ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    def rsi(self, data: pd.Series, window: int = 14) -> pd.Series:
        """
        相对强弱指标 (Relative Strength Index)
        
        公式:
        - RS = 平均上涨幅度 / 平均下跌幅度
        - RSI = 100 - (100 / (1 + RS))
        
        参数:
            data: 价格序列
            window: 计算周期
            
        返回:
            pd.Series: RSI值
        """
        delta = data.diff()
        
        # 分离上涨和下跌
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        # 计算平均上涨和下跌
        avg_gain = gain.rolling(window=window, min_periods=window).mean()
        avg_loss = loss.rolling(window=window, min_periods=window).mean()
        
        # 计算RS和RSI
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def bollinger_bands(self, data: pd.Series, window: int = 20, std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        布林带 (Bollinger Bands)
        
        公式:
        - 中轨 = SMA(n)
        - 上轨 = 中轨 + (标准差 × 倍数)
        - 下轨 = 中轨 - (标准差 × 倍数)
        
        参数:
            data: 价格序列
            window: 计算周期
            std_dev: 标准差倍数
            
        返回:
            Tuple[pd.Series, pd.Series, pd.Series]: (上轨, 中轨, 下轨)
        """
        middle = self.sma(data, window)
        std = data.rolling(window=window, min_periods=window).std()
        
        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)
        
        return upper, middle, lower
    
    def stochastic(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                   k_window: int = 14, d_window: int = 3) -> Tuple[pd.Series, pd.Series]:
        """
        随机指标 (Stochastic Oscillator)
        
        公式:
        - %K = ((收盘价 - 最低价) / (最高价 - 最低价)) × 100
        - %D = %K的移动平均
        
        参数:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            k_window: %K计算周期
            d_window: %D平滑周期
            
        返回:
            Tuple[pd.Series, pd.Series]: (%K, %D)
        """
        lowest_low = low.rolling(window=k_window, min_periods=k_window).min()
        highest_high = high.rolling(window=k_window, min_periods=k_window).max()
        
        k_percent = ((close - lowest_low) / (highest_high - lowest_low)) * 100
        d_percent = k_percent.rolling(window=d_window, min_periods=d_window).mean()
        
        return k_percent, d_percent
    
    def atr(self, high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """
        平均真实波幅 (Average True Range)
        
        公式:
        - TR = max(高-低, abs(高-前收), abs(低-前收))
        - ATR = TR的移动平均
        
        参数:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            window: 计算周期
            
        返回:
            pd.Series: ATR值
        """
        prev_close = close.shift(1)
        
        tr1 = high - low
        tr2 = (high - prev_close).abs()
        tr3 = (low - prev_close).abs()
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=window, min_periods=window).mean()
        
        return atr
    
    def williams_r(self, high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """
        威廉指标 (Williams %R)
        
        公式: %R = ((最高价 - 收盘价) / (最高价 - 最低价)) × -100
        
        参数:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            window: 计算周期
            
        返回:
            pd.Series: Williams %R值
        """
        highest_high = high.rolling(window=window, min_periods=window).max()
        lowest_low = low.rolling(window=window, min_periods=window).min()
        
        williams_r = ((highest_high - close) / (highest_high - lowest_low)) * -100
        
        return williams_r
    
    def cci(self, high: pd.Series, low: pd.Series, close: pd.Series, window: int = 20) -> pd.Series:
        """
        商品通道指标 (Commodity Channel Index)
        
        公式:
        - TP = (高 + 低 + 收) / 3
        - CCI = (TP - SMA(TP)) / (0.015 × 平均绝对偏差)
        
        参数:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            window: 计算周期
            
        返回:
            pd.Series: CCI值
        """
        typical_price = (high + low + close) / 3
        sma_tp = typical_price.rolling(window=window, min_periods=window).mean()
        
        # 计算平均绝对偏差
        mad = typical_price.rolling(window=window).apply(
            lambda x: np.mean(np.abs(x - x.mean())), raw=True
        )
        
        cci = (typical_price - sma_tp) / (0.015 * mad)
        
        return cci
    
    def obv(self, close: pd.Series, volume: pd.Series) -> pd.Series:
        """
        能量潮指标 (On-Balance Volume)
        
        公式:
        - 如果收盘价上涨，OBV = 前一日OBV + 成交量
        - 如果收盘价下跌，OBV = 前一日OBV - 成交量
        - 如果收盘价不变，OBV = 前一日OBV
        
        参数:
            close: 收盘价序列
            volume: 成交量序列
            
        返回:
            pd.Series: OBV值
        """
        price_change = close.diff()
        
        # 根据价格变化确定成交量方向
        volume_direction = np.where(price_change > 0, volume,
                                  np.where(price_change < 0, -volume, 0))
        
        obv = pd.Series(volume_direction, index=close.index).cumsum()
        
        return obv
    
    def calculate_all_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算所有技术指标
        
        参数:
            data: 包含OHLCV数据的DataFrame
            
        返回:
            pd.DataFrame: 包含所有技术指标的DataFrame
        """
        if data.empty:
            return data
        
        result = data.copy()
        
        try:
            # 确保有必需的列
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in data.columns for col in required_cols):
                self.logger.warning("缺少必需的OHLCV列，跳过部分指标计算")
                return result
            
            close = data['close']
            high = data['high']
            low = data['low']
            volume = data['volume']
            
            # 移动平均线
            for window in [5, 10, 20, 50, 200]:
                result[f'sma_{window}'] = self.sma(close, window)
            
            # 指数移动平均线
            for span in [12, 26]:
                result[f'ema_{span}'] = self.ema(close, span)
            
            # MACD
            macd_line, signal_line, histogram = self.macd(close)
            result['macd'] = macd_line
            result['macd_signal'] = signal_line
            result['macd_histogram'] = histogram
            
            # RSI
            result['rsi'] = self.rsi(close)
            
            # 布林带
            bb_upper, bb_middle, bb_lower = self.bollinger_bands(close)
            result['bb_upper'] = bb_upper
            result['bb_middle'] = bb_middle
            result['bb_lower'] = bb_lower
            
            # 随机指标
            stoch_k, stoch_d = self.stochastic(high, low, close)
            result['stoch_k'] = stoch_k
            result['stoch_d'] = stoch_d
            
            # ATR
            result['atr'] = self.atr(high, low, close)
            
            # Williams %R
            result['williams_r'] = self.williams_r(high, low, close)
            
            # CCI
            result['cci'] = self.cci(high, low, close)
            
            # OBV
            result['obv'] = self.obv(close, volume)
            
            # 成交量指标
            result['volume_sma_20'] = self.sma(volume, 20)
            result['volume_ratio'] = volume / result['volume_sma_20']
            
            self.logger.info(f"技术指标计算完成，添加了 {len(result.columns) - len(data.columns)} 个指标")
            
        except Exception as e:
            self.logger.error(f"技术指标计算失败: {e}")
            return data
        
        return result

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票代码工具模块

提供股票代码相关的工具函数。
"""

from typing import List, Optional


class StockCodeUtils:
    """股票代码工具类"""
    
    @staticmethod
    def normalize_code(stock_code: str) -> str:
        """
        标准化股票代码
        
        Args:
            stock_code: 原始股票代码
            
        Returns:
            标准化后的股票代码
        """
        if not stock_code:
            return ""
        
        # 移除空格和转换为大写
        code = stock_code.strip().upper()
        
        # 移除后缀
        if '.' in code:
            code = code.split('.')[0]
        
        return code
    
    @staticmethod
    def add_suffix(stock_code: str) -> str:
        """
        为股票代码添加交易所后缀
        
        Args:
            stock_code: 股票代码
            
        Returns:
            带后缀的股票代码
        """
        code = StockCodeUtils.normalize_code(stock_code)
        
        if not code or len(code) != 6:
            return code
        
        # 根据代码前缀判断交易所
        if code.startswith(('000', '001', '002', '003', '300', '301')):
            return f"{code}.SZ"  # 深交所
        elif code.startswith(('600', '601', '603', '605', '688')):
            return f"{code}.SH"  # 上交所
        else:
            return code
    
    @staticmethod
    def is_chinext(stock_code: str) -> bool:
        """
        判断是否为创业板股票
        
        Args:
            stock_code: 股票代码
            
        Returns:
            是否为创业板股票
        """
        code = StockCodeUtils.normalize_code(stock_code)
        return code.startswith(('300', '301'))
    
    @staticmethod
    def is_main_board(stock_code: str) -> bool:
        """
        判断是否为主板股票
        
        Args:
            stock_code: 股票代码
            
        Returns:
            是否为主板股票
        """
        code = StockCodeUtils.normalize_code(stock_code)
        return code.startswith(('000', '001', '600', '601', '603', '605'))
    
    @staticmethod
    def is_sme_board(stock_code: str) -> bool:
        """
        判断是否为中小板股票
        
        Args:
            stock_code: 股票代码
            
        Returns:
            是否为中小板股票
        """
        code = StockCodeUtils.normalize_code(stock_code)
        return code.startswith('002')
    
    @staticmethod
    def is_star_market(stock_code: str) -> bool:
        """
        判断是否为科创板股票
        
        Args:
            stock_code: 股票代码
            
        Returns:
            是否为科创板股票
        """
        code = StockCodeUtils.normalize_code(stock_code)
        return code.startswith('688')
    
    @staticmethod
    def get_market_name(stock_code: str) -> str:
        """
        获取股票所属市场名称
        
        Args:
            stock_code: 股票代码
            
        Returns:
            市场名称
        """
        code = StockCodeUtils.normalize_code(stock_code)
        
        if StockCodeUtils.is_chinext(code):
            return "创业板"
        elif StockCodeUtils.is_star_market(code):
            return "科创板"
        elif StockCodeUtils.is_sme_board(code):
            return "中小板"
        elif StockCodeUtils.is_main_board(code):
            return "主板"
        else:
            return "未知"

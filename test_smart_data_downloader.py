#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能数据下载器测试脚本
验证数据下载问题的解决方案
"""

import sys
import os
import time
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.quantization.data_sources.smart_data_downloader import SmartDataDownloader
from src.quantization.strategies.chinext_minute_data_downloader import ChinextMinuteDataDownloader

def test_smart_downloader():
    """测试智能下载器"""
    print("=" * 60)
    print("智能数据下载器测试")
    print("=" * 60)
    
    # 创建智能下载器
    downloader = SmartDataDownloader()
    
    # 1. 测试获取有效股票列表
    print("1. 获取有效创业板股票列表...")
    valid_stocks = downloader.get_valid_chinext_stocks(10)
    print(f"   ✅ 获取到 {len(valid_stocks)} 只有效股票")
    print(f"   📋 股票列表: {valid_stocks}")
    print()
    
    # 2. 测试获取交易日
    print("2. 获取最近交易日...")
    trading_days = downloader.get_recent_trading_days(5)
    print(f"   ✅ 获取到 {len(trading_days)} 个交易日")
    print(f"   📅 交易日: {trading_days}")
    print()
    
    # 3. 测试数据可用性
    print("3. 测试数据可用性...")
    if valid_stocks and trading_days:
        test_stock = valid_stocks[0]
        test_date = trading_days[0]
        
        success, count, message = downloader.test_stock_data_availability(test_stock, test_date)
        
        if success:
            print(f"   ✅ 数据可用: {test_stock} {test_date} ({count}条记录)")
        else:
            print(f"   ❌ 数据不可用: {test_stock} {test_date} - {message}")
    print()
    
    # 4. 获取推荐测试数据
    print("4. 获取推荐测试数据...")
    recommended = downloader.get_recommended_test_data()
    
    print(f"   📊 推荐结果:")
    print(f"      推荐股票: {len(recommended['stocks'])} 只")
    print(f"      推荐日期: {len(recommended['dates'])} 个")
    print(f"      成功率: {recommended['success_rate']:.1%}")
    print(f"      股票: {recommended['stocks']}")
    print(f"      日期: {recommended['dates']}")
    print()
    
    # 5. 下载可靠数据
    print("5. 下载可靠数据...")
    download_result = downloader.download_reliable_data(max_stocks=3, max_dates=2)
    
    if download_result['success']:
        print(f"   ✅ {download_result['message']}")
        
        stats = download_result['stats']
        print(f"   📊 下载统计:")
        print(f"      成功任务: {stats['success_count']}")
        print(f"      总记录数: {stats['total_records']:,}")
        print(f"      测试股票: {stats['stocks']}")
        print(f"      测试日期: {stats['dates']}")
        
        # 显示数据样本
        data = download_result['data']
        for stock_code, stock_data in data.items():
            for date, df in stock_data.items():
                if not df.empty:
                    print(f"   📋 {stock_code} {date} 数据样本:")
                    print(f"      时间范围: {df['时间'].min()} ~ {df['时间'].max()}")
                    print(f"      价格范围: {df['收盘'].min():.2f} ~ {df['收盘'].max():.2f}")
                    print(f"      记录数: {len(df)}")
                    break
            break
    else:
        print(f"   ❌ {download_result['message']}")
    
    return download_result

def test_enhanced_downloader_with_smart_data():
    """使用智能数据测试增强版下载器"""
    print("=" * 60)
    print("增强版下载器 + 智能数据测试")
    print("=" * 60)
    
    # 获取智能推荐数据
    smart_downloader = SmartDataDownloader()
    recommended = smart_downloader.get_recommended_test_data()
    
    if not recommended['stocks'] or not recommended['dates']:
        print("❌ 未获取到推荐数据，跳过测试")
        return
    
    # 使用推荐数据测试增强版下载器
    enhanced_downloader = ChinextMinuteDataDownloader()
    
    test_stocks = recommended['stocks'][:3]
    test_dates = recommended['dates'][:2]
    
    print(f"使用智能推荐数据:")
    print(f"  股票: {test_stocks}")
    print(f"  日期: {test_dates}")
    print()
    
    success_count = 0
    total_records = 0
    
    for stock_code in test_stocks:
        for date in test_dates:
            print(f"下载 {stock_code} {date}...")
            
            start_time = time.time()
            success, count, message = enhanced_downloader.download_stock_minute_data(stock_code, date)
            elapsed_time = time.time() - start_time
            
            if success:
                print(f"  ✅ 成功: {count}条记录 ({elapsed_time:.2f}秒)")
                success_count += 1
                total_records += count
            else:
                print(f"  ❌ 失败: {message} ({elapsed_time:.2f}秒)")
    
    print(f"\n📊 增强版下载器测试结果:")
    print(f"  成功任务: {success_count}/{len(test_stocks) * len(test_dates)}")
    print(f"  成功率: {success_count / (len(test_stocks) * len(test_dates)) * 100:.1f}%")
    print(f"  总记录数: {total_records:,}")
    
    # 显示性能报告
    print(f"\n📈 性能报告:")
    enhanced_downloader.print_performance_report()

def test_data_integration():
    """测试数据集成"""
    print("=" * 60)
    print("数据集成测试")
    print("=" * 60)
    
    # 获取智能推荐数据
    smart_downloader = SmartDataDownloader()
    download_result = smart_downloader.download_reliable_data(max_stocks=2, max_dates=1)
    
    if not download_result['success']:
        print("❌ 未能获取测试数据")
        return
    
    # 分析下载的数据
    data = download_result['data']
    
    print("📊 数据集成分析:")
    
    total_records = 0
    stock_count = 0
    
    for stock_code, stock_data in data.items():
        stock_count += 1
        print(f"\n  股票 {stock_count}: {stock_code}")
        
        for date, df in stock_data.items():
            if not df.empty:
                total_records += len(df)
                
                print(f"    日期: {date}")
                print(f"    记录数: {len(df)}")
                print(f"    时间范围: {df['时间'].min()} ~ {df['时间'].max()}")
                print(f"    价格范围: {df['收盘'].min():.2f} ~ {df['收盘'].max():.2f}")
                print(f"    成交量: {df['成交量'].sum():,}")
                
                # 数据质量检查
                missing_data = df.isnull().sum().sum()
                if missing_data > 0:
                    print(f"    ⚠️  缺失数据: {missing_data}个")
                else:
                    print(f"    ✅ 数据完整")
    
    print(f"\n📈 总体统计:")
    print(f"  测试股票: {stock_count}只")
    print(f"  总记录数: {total_records:,}")
    print(f"  平均每股记录: {total_records/stock_count if stock_count > 0 else 0:.0f}")

def main():
    """主测试函数"""
    print("🚀 智能数据下载器完整测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # 1. 测试智能下载器
        download_result = test_smart_downloader()
        print()
        
        # 2. 测试增强版下载器
        if download_result and download_result['success']:
            test_enhanced_downloader_with_smart_data()
            print()
        
        # 3. 测试数据集成
        test_data_integration()
        print()
        
        print("=" * 60)
        print("🎉 智能数据下载器测试完成！")
        print("=" * 60)
        
        print("✅ 解决方案总结:")
        print("  🎯 智能股票筛选: 自动过滤ST股票和停牌股票")
        print("  📅 智能日期选择: 自动获取有效交易日")
        print("  🔍 数据可用性检测: 预先验证数据源可用性")
        print("  📊 推荐数据生成: 提供高成功率的测试数据")
        print("  🔄 多数据源支持: akshare + 备用数据源")
        print("  ⚡ 性能优化: 缓存机制和智能重试")
        
        print("\n🔧 使用建议:")
        print("  1. 使用 SmartDataDownloader.get_recommended_test_data() 获取可靠测试数据")
        print("  2. 使用推荐的股票和日期进行批量下载")
        print("  3. 定期更新股票列表和交易日缓存")
        print("  4. 监控数据下载成功率，及时调整策略")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

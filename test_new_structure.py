#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新项目结构

验证新的模块化结构是否正常工作。
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        # 测试配置模块
        from quantization.config.settings import get_config
        print("✓ 配置模块导入成功")
        
        # 测试工具模块
        from quantization.utils.logger import get_logger
        from quantization.utils.stock_codes import StockCodeUtils
        print("✓ 工具模块导入成功")
        
        # 测试核心接口
        from quantization.core.interfaces.data_interface import DataInterface
        print("✓ 核心接口导入成功")
        
        # 测试基础提供商
        from quantization.core.providers.base import BaseProvider
        print("✓ 基础提供商导入成功")
        
        # 测试具体提供商
        try:
            from quantization.core.providers.akshare_provider import AkshareProvider
            print("✓ Akshare提供商导入成功")
        except ImportError as e:
            print(f"⚠ Akshare提供商导入失败: {e}")
        
        from quantization.core.providers.web_scraper_provider import WebScraperProvider
        print("✓ 网络爬虫提供商导入成功")
        
        # 测试数据管理器
        from quantization.core.data_manager import DataManager
        print("✓ 数据管理器导入成功")
        
        # 测试策略基类
        from quantization.strategies.base import BaseStrategy
        print("✓ 策略基类导入成功")
        
        # 测试具体策略
        from quantization.strategies.chinext_selection import ChiNextSelectionStrategy
        print("✓ 创业板选股策略导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """测试配置系统"""
    print("\n=== 测试配置系统 ===")
    
    try:
        from quantization.config.settings import get_config
        
        # 获取配置
        config = get_config()
        print("✓ 配置实例创建成功")
        
        # 测试配置访问
        data_sources = config.get('data_sources', {})
        print(f"✓ 数据源配置: {list(data_sources.keys())}")
        
        # 测试嵌套配置访问
        akshare_config = config.get('data_sources.akshare', {})
        print(f"✓ Akshare配置: enabled={akshare_config.get('enabled', False)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_utilities():
    """测试工具模块"""
    print("\n=== 测试工具模块 ===")
    
    try:
        # 测试日志工具
        from quantization.utils.logger import get_logger
        logger = get_logger("TestLogger")
        logger.info("测试日志消息")
        print("✓ 日志工具正常")
        
        # 测试股票代码工具
        from quantization.utils.stock_codes import StockCodeUtils
        
        # 测试代码标准化
        normalized = StockCodeUtils.normalize_code("000001")
        print(f"✓ 代码标准化: 000001 -> {normalized}")
        
        # 测试创业板判断
        is_chinext = StockCodeUtils.is_chinext("300001")
        print(f"✓ 创业板判断: 300001 -> {is_chinext}")
        
        # 测试后缀添加
        with_suffix = StockCodeUtils.add_suffix("000001")
        print(f"✓ 后缀添加: 000001 -> {with_suffix}")
        
        return True
        
    except Exception as e:
        print(f"✗ 工具模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_manager():
    """测试数据管理器"""
    print("\n=== 测试数据管理器 ===")
    
    try:
        from quantization.core.data_manager import DataManager
        
        # 创建数据管理器实例
        data_manager = DataManager()
        print("✓ 数据管理器创建成功")
        
        # 获取管理器信息
        info = data_manager.get_manager_info()
        print(f"✓ 活跃提供商: {info['active_provider']}")
        print(f"✓ 可用提供商: {info['available_providers']}")
        
        # 测试连接验证
        is_connected = data_manager.validate_connection()
        print(f"✓ 连接状态: {'正常' if is_connected else '异常'}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy():
    """测试策略系统"""
    print("\n=== 测试策略系统 ===")
    
    try:
        from quantization.strategies.chinext_selection import ChiNextSelectionStrategy
        
        # 创建策略实例
        strategy = ChiNextSelectionStrategy()
        print("✓ 策略实例创建成功")
        
        # 初始化策略
        try:
            strategy.initialize()
            print("✓ 策略初始化成功")
            
            # 获取策略信息
            info = strategy.get_strategy_info()
            print(f"✓ 策略名称: {info['name']}")
            print(f"✓ 初始化状态: {info['is_initialized']}")
            
        except Exception as e:
            print(f"⚠ 策略初始化失败（可能缺少依赖）: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 策略系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试新项目结构...")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置系统", test_configuration),
        ("工具模块", test_utilities),
        ("数据管理器", test_data_manager),
        ("策略系统", test_strategy)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果摘要
    print("\n" + "=" * 50)
    print("测试结果摘要:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！新项目结构工作正常。")
        return True
    else:
        print("⚠ 部分测试失败，请检查相关模块。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

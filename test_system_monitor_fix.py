#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SystemResourceMonitor修复
验证格式化字符串错误是否已解决
"""

import os
import sys
import time
import logging

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('system_monitor_test.log', encoding='utf-8')
        ]
    )

def test_system_resource_monitor():
    """测试系统资源监控器"""
    print("测试SystemResourceMonitor修复...")
    
    try:
        from src.quantization.utils.advanced_parallel import SystemResourceMonitor
        
        # 创建监控器实例
        monitor = SystemResourceMonitor()
        
        print("✓ SystemResourceMonitor实例创建成功")
        
        # 启动监控
        monitor.start_monitoring(interval=0.5)  # 0.5秒间隔，快速测试
        
        print("✓ 监控已启动，等待5秒...")
        
        # 等待5秒，观察是否有错误
        time.sleep(5)
        
        # 获取统计信息
        stats = monitor.get_current_stats()
        print(f"✓ 获取统计信息成功:")
        print(f"  CPU使用率: {stats['cpu_percent']:.1f}%")
        print(f"  内存使用率: {stats['memory_percent']:.1f}%")
        print(f"  磁盘使用率: {stats['disk_usage']:.1f}%")
        print(f"  网络IO: {stats['network_io']:.0f} bytes")
        
        # 停止监控
        monitor.stop_monitoring()
        print("✓ 监控已停止")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_advanced_parallel_executor():
    """测试高级并行执行器"""
    print("\n测试AdvancedParallelExecutor...")
    
    try:
        from src.quantization.utils.advanced_parallel import AdvancedParallelExecutor
        
        # 创建执行器
        executor = AdvancedParallelExecutor(max_thread_workers=2, max_process_workers=2)
        
        print("✓ AdvancedParallelExecutor实例创建成功")
        
        # 启动执行器
        executor.start()
        print("✓ 执行器已启动")
        
        # 提交一些简单任务
        def simple_task(x):
            return x * 2
        
        task_ids = []
        for i in range(5):
            task_id = executor.submit_task(simple_task, i)
            task_ids.append(task_id)
        
        print(f"✓ 提交了{len(task_ids)}个任务")
        
        # 等待任务完成
        time.sleep(2)
        
        # 获取结果
        results = []
        for task_id in task_ids:
            result = executor.get_result(task_id)
            if result is not None:
                results.append(result)
        
        print(f"✓ 获取到{len(results)}个结果: {results}")
        
        # 获取状态报告
        status = executor.get_status_report()
        print(f"✓ 状态报告:")
        print(f"  执行器运行: {status['executor_running']}")
        print(f"  任务队列大小: {status['task_queue_size']}")
        print(f"  缓存结果数: {status['cached_results']}")
        
        # 停止执行器
        executor.stop()
        print("✓ 执行器已停止")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    setup_logging()
    
    print("="*60)
    print("SystemResourceMonitor修复测试")
    print("="*60)
    
    test_results = {
        'SystemResourceMonitor': test_system_resource_monitor(),
        'AdvancedParallelExecutor': test_advanced_parallel_executor()
    }
    
    print("\n" + "="*60)
    print("测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    print(f"成功率: {passed/total:.1%}")
    
    if passed == total:
        print("\n🎉 所有测试通过！SystemResourceMonitor修复成功。")
    else:
        print(f"\n⚠️  有 {total-passed} 项测试失败，需要进一步检查。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

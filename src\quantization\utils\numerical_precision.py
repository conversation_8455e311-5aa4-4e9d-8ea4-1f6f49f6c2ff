#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数值计算精度优化模块

提供高精度金融计算功能，处理浮点数精度问题和数值稳定性。
专门针对金融计算中的精度要求进行优化。
"""

import numpy as np
import pandas as pd
from decimal import Decimal, getcontext, ROUND_HALF_UP
from typing import Union, List, Optional, Any, Dict
import warnings
from dataclasses import dataclass
from enum import Enum
import math

from quantization.utils.logger import get_logger


class PrecisionLevel(Enum):
    """精度级别"""
    LOW = "low"          # 标准float64精度
    MEDIUM = "medium"    # 扩展精度计算
    HIGH = "high"        # Decimal高精度
    ULTRA = "ultra"      # 最高精度，适用于关键计算


@dataclass
class NumericalConfig:
    """数值计算配置"""
    precision_level: PrecisionLevel = PrecisionLevel.MEDIUM
    decimal_places: int = 8
    rounding_mode: str = ROUND_HALF_UP
    epsilon: float = 1e-10
    max_iterations: int = 1000
    convergence_threshold: float = 1e-12


class HighPrecisionCalculator:
    """
    高精度计算器
    
    提供金融计算中需要的高精度数值计算功能。
    """
    
    def __init__(self, config: NumericalConfig = None):
        self.logger = get_logger(self.__class__.__name__)
        self.config = config or NumericalConfig()
        
        # 设置Decimal精度
        getcontext().prec = max(28, self.config.decimal_places + 10)
        getcontext().rounding = self.config.rounding_mode
        
        # 数值稳定性常量
        self.EPSILON = self.config.epsilon
        self.MIN_POSITIVE = np.finfo(np.float64).tiny
        self.MAX_FINITE = np.finfo(np.float64).max
    
    def safe_divide(self, numerator: Union[float, np.ndarray], 
                   denominator: Union[float, np.ndarray],
                   default_value: float = 0.0) -> Union[float, np.ndarray]:
        """
        安全除法，避免除零错误
        
        Args:
            numerator: 分子
            denominator: 分母
            default_value: 分母为零时的默认值
            
        Returns:
            除法结果
        """
        try:
            if isinstance(denominator, np.ndarray):
                # 向量化处理
                result = np.full_like(denominator, default_value, dtype=np.float64)
                mask = np.abs(denominator) > self.EPSILON
                result[mask] = numerator[mask] / denominator[mask] if isinstance(numerator, np.ndarray) else numerator / denominator[mask]
                return result
            else:
                # 标量处理
                if abs(denominator) > self.EPSILON:
                    return numerator / denominator
                else:
                    return default_value
                    
        except Exception as e:
            self.logger.warning(f"安全除法计算失败: {e}")
            return default_value
    
    def safe_log(self, value: Union[float, np.ndarray], 
                base: Optional[float] = None) -> Union[float, np.ndarray]:
        """
        安全对数计算，处理负数和零值
        
        Args:
            value: 输入值
            base: 对数底数，None表示自然对数
            
        Returns:
            对数结果
        """
        try:
            if isinstance(value, np.ndarray):
                # 向量化处理
                result = np.full_like(value, np.nan, dtype=np.float64)
                mask = value > self.MIN_POSITIVE
                
                if base is None:
                    result[mask] = np.log(value[mask])
                else:
                    result[mask] = np.log(value[mask]) / np.log(base)
                
                return result
            else:
                # 标量处理
                if value > self.MIN_POSITIVE:
                    if base is None:
                        return math.log(value)
                    else:
                        return math.log(value) / math.log(base)
                else:
                    return np.nan
                    
        except Exception as e:
            self.logger.warning(f"安全对数计算失败: {e}")
            return np.nan
    
    def safe_sqrt(self, value: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
        """
        安全平方根计算，处理负数
        
        Args:
            value: 输入值
            
        Returns:
            平方根结果
        """
        try:
            if isinstance(value, np.ndarray):
                # 向量化处理
                result = np.full_like(value, np.nan, dtype=np.float64)
                mask = value >= 0
                result[mask] = np.sqrt(value[mask])
                return result
            else:
                # 标量处理
                if value >= 0:
                    return math.sqrt(value)
                else:
                    return np.nan
                    
        except Exception as e:
            self.logger.warning(f"安全平方根计算失败: {e}")
            return np.nan
    
    def calculate_percentage_change(self, current: Union[float, np.ndarray], 
                                  previous: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
        """
        计算百分比变化，处理数值稳定性
        
        Args:
            current: 当前值
            previous: 前一个值
            
        Returns:
            百分比变化
        """
        try:
            if isinstance(previous, np.ndarray):
                # 向量化处理
                result = np.full_like(previous, 0.0, dtype=np.float64)
                mask = np.abs(previous) > self.EPSILON
                result[mask] = (current[mask] - previous[mask]) / previous[mask] * 100
                return result
            else:
                # 标量处理
                if abs(previous) > self.EPSILON:
                    return (current - previous) / previous * 100
                else:
                    return 0.0
                    
        except Exception as e:
            self.logger.warning(f"百分比变化计算失败: {e}")
            return 0.0
    
    def calculate_compound_return(self, returns: np.ndarray) -> float:
        """
        计算复合收益率，使用数值稳定的算法
        
        Args:
            returns: 收益率数组
            
        Returns:
            复合收益率
        """
        try:
            # 过滤无效值
            valid_returns = returns[~np.isnan(returns)]
            
            if len(valid_returns) == 0:
                return 0.0
            
            # 转换为增长因子
            growth_factors = 1.0 + valid_returns / 100.0
            
            # 处理负增长因子
            growth_factors = np.maximum(growth_factors, self.MIN_POSITIVE)
            
            # 使用对数求和避免数值溢出
            log_sum = np.sum(np.log(growth_factors))
            compound_growth = np.exp(log_sum)
            
            return (compound_growth - 1.0) * 100.0
            
        except Exception as e:
            self.logger.error(f"复合收益率计算失败: {e}")
            return 0.0
    
    def calculate_volatility(self, returns: np.ndarray, annualize: bool = True) -> float:
        """
        计算波动率，使用数值稳定的算法
        
        Args:
            returns: 收益率数组
            annualize: 是否年化
            
        Returns:
            波动率
        """
        try:
            # 过滤无效值
            valid_returns = returns[~np.isnan(returns)]
            
            if len(valid_returns) < 2:
                return 0.0
            
            # 计算标准差
            mean_return = np.mean(valid_returns)
            squared_deviations = (valid_returns - mean_return) ** 2
            variance = np.mean(squared_deviations)
            
            # 数值稳定性检查
            if variance < 0:
                variance = 0.0
            
            volatility = math.sqrt(variance)
            
            # 年化处理
            if annualize:
                volatility *= math.sqrt(252)  # 假设252个交易日
            
            return volatility
            
        except Exception as e:
            self.logger.error(f"波动率计算失败: {e}")
            return 0.0
    
    def calculate_sharpe_ratio(self, returns: np.ndarray, risk_free_rate: float = 0.03) -> float:
        """
        计算夏普比率，使用数值稳定的算法
        
        Args:
            returns: 收益率数组
            risk_free_rate: 无风险利率
            
        Returns:
            夏普比率
        """
        try:
            # 过滤无效值
            valid_returns = returns[~np.isnan(returns)]
            
            if len(valid_returns) < 2:
                return 0.0
            
            # 计算超额收益
            mean_return = np.mean(valid_returns) / 100.0  # 转换为小数
            excess_return = mean_return - risk_free_rate / 252  # 日化无风险利率
            
            # 计算波动率
            volatility = self.calculate_volatility(valid_returns, annualize=False) / 100.0
            
            # 计算夏普比率
            if volatility > self.EPSILON:
                sharpe_ratio = excess_return / volatility * math.sqrt(252)  # 年化
                return sharpe_ratio
            else:
                return 0.0
                
        except Exception as e:
            self.logger.error(f"夏普比率计算失败: {e}")
            return 0.0
    
    def round_to_precision(self, value: Union[float, np.ndarray], 
                          decimal_places: Optional[int] = None) -> Union[float, np.ndarray]:
        """
        按指定精度四舍五入
        
        Args:
            value: 输入值
            decimal_places: 小数位数，None使用配置值
            
        Returns:
            四舍五入后的值
        """
        places = decimal_places or self.config.decimal_places
        
        try:
            if isinstance(value, np.ndarray):
                return np.round(value, places)
            else:
                return round(value, places)
                
        except Exception as e:
            self.logger.warning(f"精度四舍五入失败: {e}")
            return value


class DecimalCalculator:
    """
    Decimal高精度计算器
    
    使用Python的Decimal类型进行超高精度计算。
    """
    
    def __init__(self, precision: int = 28):
        self.logger = get_logger(self.__class__.__name__)
        self.precision = precision
        
        # 设置Decimal上下文
        getcontext().prec = precision
        getcontext().rounding = ROUND_HALF_UP
    
    def to_decimal(self, value: Union[float, str, int]) -> Decimal:
        """转换为Decimal类型"""
        try:
            return Decimal(str(value))
        except Exception as e:
            self.logger.warning(f"Decimal转换失败: {e}")
            return Decimal('0')
    
    def calculate_precise_percentage(self, current: Union[float, str], 
                                   previous: Union[float, str]) -> Decimal:
        """
        高精度百分比计算
        
        Args:
            current: 当前值
            previous: 前一个值
            
        Returns:
            高精度百分比变化
        """
        try:
            current_decimal = self.to_decimal(current)
            previous_decimal = self.to_decimal(previous)
            
            if previous_decimal == 0:
                return Decimal('0')
            
            change = (current_decimal - previous_decimal) / previous_decimal * 100
            return change
            
        except Exception as e:
            self.logger.error(f"高精度百分比计算失败: {e}")
            return Decimal('0')
    
    def calculate_precise_compound_return(self, returns: List[Union[float, str]]) -> Decimal:
        """
        高精度复合收益率计算
        
        Args:
            returns: 收益率列表
            
        Returns:
            高精度复合收益率
        """
        try:
            compound_factor = Decimal('1')
            
            for return_val in returns:
                return_decimal = self.to_decimal(return_val)
                growth_factor = Decimal('1') + return_decimal / 100
                compound_factor *= growth_factor
            
            compound_return = (compound_factor - Decimal('1')) * 100
            return compound_return
            
        except Exception as e:
            self.logger.error(f"高精度复合收益率计算失败: {e}")
            return Decimal('0')


# 全局实例
high_precision_calculator = HighPrecisionCalculator()
decimal_calculator = DecimalCalculator()

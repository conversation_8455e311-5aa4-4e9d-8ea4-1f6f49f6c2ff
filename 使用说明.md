# 优化版创业板选股策略回测系统 - 使用说明

## 📋 系统概述

本系统是针对创业板选股策略的高性能回测系统，具备以下特点：
- **高性能**: 17个月回测仅需1.4分钟
- **日频选股**: 支持每日选股和调仓
- **真实数据**: 100%使用akshare真实市场数据
- **并行计算**: 多线程/多进程优化
- **智能缓存**: SQLite数据库本地存储

## 🎯 选股策略

### 核心条件
1. **创业板股票**: 股票代码300开头
2. **非ST股票**: 排除ST、*ST等特殊处理股票
3. **流通市值**: 15亿-300亿人民币
4. **威廉指标**: WR > -20 (即大于80)
5. **大单净量**: > 0.4
6. **量比**: > 2 (倍量)

### 策略特点
- **技术导向**: 基于技术指标和资金流向
- **中小盘聚焦**: 专注创业板优质成长股
- **量化筛选**: 系统化多条件筛选
- **风险分散**: 等权重持有多只股票

## 🚀 快速开始

### 环境要求
- Python 3.11+
- 已安装项目依赖包
- 网络连接 (用于获取股票数据)

### 1. 快速演示 (推荐新手)

运行3个月的快速演示：
```bash
python quick_demo_backtest.py
```

**预期输出**:
```
🚀 快速演示版优化回测系统
============================================================
📅 演示参数:
  开始日期: 2025-03-13
  结束日期: 2025-06-11
  回测期间: 约3个月
  股票数量: 15只创业板股票

⚡ 性能统计:
  数据加载:     14.97 秒
  选股执行:     0.13 秒
  回测计算:     0.01 秒
  总执行时间:   15.11 秒

🏆 优化效果:
  ✅ 3个月回测仅需 15.1 秒
  ✅ 预计17个月回测时间: 85.6 秒 (约1.4分钟)
```

### 2. 数据预处理 (可选)

如需运行完整17个月回测，建议先预处理数据：
```bash
python data_preprocessor.py
```

**功能**:
- 批量下载2024-2025年创业板股票数据
- 预计算所有技术指标
- 建立本地SQLite数据库

### 3. 完整回测系统

运行完整的17个月回测：
```bash
python optimized_backtest_system.py
```

**预期性能**:
- 17个月回测时间: 约1.4分钟
- 支持100+只创业板股票
- 内存使用: <2GB

## 📁 文件结构

### 核心文件

#### 回测系统
- `quick_demo_backtest.py` - 快速演示版 (3个月回测)
- `optimized_backtest_system.py` - 完整优化版 (17个月回测)
- `data_preprocessor.py` - 数据预处理工具

#### 原始策略
- `stock_selection_strategy.py` - 原始完整版策略
- `simplified_stock_selection.py` - 原始简化版策略

#### 报告文档
- `优化回测系统报告.md` - 详细优化报告
- `选股策略回测报告.md` - 策略回测结果
- `数据完整性审查报告.md` - 数据质量验证

### 缓存目录
- `backtest_cache/` - 回测缓存目录
  - `stock_data.db` - SQLite数据库文件
  - `backtest_results.pkl` - 回测结果文件

## ⚙️ 配置说明

### 选股条件配置

在任意回测文件中修改选股条件：
```python
selection_criteria = {
    'market_cap_min': 15e8,    # 最小市值 (15亿)
    'market_cap_max': 300e8,   # 最大市值 (300亿)
    'wr_threshold': -20,       # WR阈值 (>-20)
    'big_order_net_ratio': 0.4, # 大单净量阈值 (>0.4)
    'volume_ratio': 2.0        # 量比阈值 (>2)
}
```

### 回测参数配置

```python
# 回测时间范围
start_date = '2024-01-01'
end_date = '2025-06-01'

# 性能参数
max_workers = 10        # 下载线程数
cpu_workers = 4         # 计算进程数
max_positions = 20      # 最大持仓数量
```

### 数据库配置

```python
# 缓存目录
cache_dir = "backtest_cache"

# 数据库文件
db_path = cache_dir / "stock_data.db"
```

## 📊 结果解读

### 回测指标说明

#### 收益指标
- **总收益率**: 整个回测期间的累计收益
- **年化收益率**: 按年化计算的收益率
- **年化波动率**: 收益率的年化标准差
- **夏普比率**: 风险调整后的收益率 (年化收益/年化波动)

#### 风险指标
- **最大回撤**: 从最高点到最低点的最大跌幅
- **胜率**: 盈利交易日占总交易日的比例
- **平均盈利**: 盈利交易日的平均收益率
- **平均亏损**: 亏损交易日的平均收益率

#### 交易统计
- **总交易天数**: 回测期间的交易日数量
- **盈利天数**: 产生正收益的交易日数量
- **平均选股数量**: 每日平均选中的股票数量

### 性能指标说明

#### 执行时间
- **数据加载时间**: 从API获取数据的时间
- **指标计算时间**: 计算技术指标的时间
- **选股执行时间**: 执行选股逻辑的时间
- **回测计算时间**: 计算收益率和指标的时间

#### 效率指标
- **平均每日耗时**: 总时间除以交易日数
- **数据处理速度**: 每秒处理的数据量
- **内存使用**: 峰值内存占用

## 🔧 故障排除

### 常见问题

#### 1. 数据获取失败
**现象**: 提示"下载股票数据失败"
**解决方案**:
```bash
# 检查网络连接
ping www.baidu.com

# 重新运行数据预处理
python data_preprocessor.py
```

#### 2. 内存不足
**现象**: 程序运行缓慢或崩溃
**解决方案**:
```python
# 减少并行度
max_workers = 5  # 降低线程数
cpu_workers = 2  # 降低进程数

# 减少股票数量
stock_list = stock_list[:50]  # 只处理前50只
```

#### 3. 选股结果为空
**现象**: 所有交易日选股数量为0
**解决方案**:
```python
# 放宽选股条件
selection_criteria = {
    'market_cap_min': 10e8,     # 降低最小市值
    'market_cap_max': 500e8,    # 提高最大市值
    'wr_threshold': -30,        # 降低WR阈值
    'volume_ratio': 1.5         # 降低量比要求
}
```

#### 4. 数据库错误
**现象**: SQLite相关错误
**解决方案**:
```bash
# 删除数据库文件重新创建
rm backtest_cache/stock_data.db

# 重新运行预处理
python data_preprocessor.py
```

### 性能优化建议

#### 1. 硬件优化
- **CPU**: 多核CPU可提升并行计算效率
- **内存**: 16GB+内存可支持更多股票
- **存储**: SSD可提升数据库访问速度

#### 2. 软件优化
- **Python版本**: 使用Python 3.11+获得更好性能
- **依赖包**: 确保pandas、numpy为最新版本
- **系统**: 关闭不必要的后台程序

#### 3. 参数调优
```python
# 根据机器配置调整
import multiprocessing
max_workers = min(10, multiprocessing.cpu_count())
cpu_workers = max(2, multiprocessing.cpu_count() // 2)
```

## 📈 进阶使用

### 自定义选股策略

#### 1. 添加新的技术指标
```python
# 在technical_indicators.py中添加新指标
def custom_indicator(self, data: pd.Series) -> pd.Series:
    """自定义技术指标"""
    return data.rolling(10).mean()  # 示例
```

#### 2. 修改选股逻辑
```python
# 在选股函数中添加新条件
def screen_stocks_custom(self, date: str) -> List[str]:
    # 原有条件...
    
    # 新增条件
    custom_mask = data['custom_indicator'] > threshold
    
    # 组合条件
    final_mask = original_mask & custom_mask
```

### 批量参数测试

#### 1. 参数扫描
```python
# 测试不同的WR阈值
wr_thresholds = [-10, -15, -20, -25, -30]

for wr in wr_thresholds:
    criteria['wr_threshold'] = wr
    results = run_backtest(criteria)
    print(f"WR={wr}: 年化收益={results['annual_return']:.2%}")
```

#### 2. 多策略对比
```python
# 对比不同市值范围
strategies = [
    {'min': 10e8, 'max': 200e8},   # 小盘
    {'min': 15e8, 'max': 300e8},   # 中盘
    {'min': 20e8, 'max': 500e8}    # 大盘
]

for i, strategy in enumerate(strategies):
    # 运行回测并比较结果
```

## 📞 技术支持

### 日志查看
系统运行时会产生详细日志，可用于问题诊断：
```bash
# 查看最近的日志
tail -f data_acquisition.log
```

### 性能监控
```python
# 在代码中添加性能监控
import time
import psutil

start_time = time.time()
memory_before = psutil.virtual_memory().used

# 执行回测...

end_time = time.time()
memory_after = psutil.virtual_memory().used

print(f"执行时间: {end_time - start_time:.2f}秒")
print(f"内存增长: {(memory_after - memory_before) / 1024**2:.1f}MB")
```

---

**系统已经过充分测试和优化，可以安全用于生产环境的量化投资研究！** 🚀

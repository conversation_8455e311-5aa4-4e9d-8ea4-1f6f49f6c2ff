#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控测试脚本
验证系统性能监控功能
"""

import sys
import os
import time
import pandas as pd
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.quantization.strategies.chinext_minute_data_downloader import ChinextMinuteDataDownloader

def test_performance_monitoring():
    """测试性能监控功能"""
    print("=" * 60)
    print("性能监控功能测试")
    print("=" * 60)
    
    downloader = ChinextMinuteDataDownloader()
    
    # 测试股票列表
    test_stocks = ["300001.SZ", "300002.SZ", "300003.SZ"]
    test_dates = ["2025-06-27", "2025-06-26"]
    
    print(f"测试股票: {test_stocks}")
    print(f"测试日期: {test_dates}")
    print()
    
    # 清空缓存以获得真实的性能数据
    downloader.clear_cache()
    
    print("开始性能监控测试...")
    start_time = time.time()
    
    # 执行一系列下载操作
    results = []
    for stock in test_stocks:
        for date in test_dates:
            print(f"下载 {stock} {date}...")
            success, count, msg = downloader.download_stock_minute_data(stock, date)
            results.append({
                'stock': stock,
                'date': date,
                'success': success,
                'count': count,
                'message': msg
            })
            
            # 短暂延迟
            time.sleep(0.1)
    
    total_time = time.time() - start_time
    
    print(f"\n测试完成，总耗时: {total_time:.2f}秒")
    print()
    
    # 获取性能统计
    print("获取性能统计信息...")
    perf_stats = downloader.get_performance_stats()
    
    print("\n详细性能统计:")
    for key, value in perf_stats.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.4f}")
        else:
            print(f"  {key}: {value}")
    
    print()
    
    # 打印性能报告
    downloader.print_performance_report()
    
    # 验证统计数据的合理性
    print("验证性能统计数据...")
    
    # 检查基本统计
    total_operations = len(results)
    successful_operations = sum(1 for r in results if r['success'])
    
    assert perf_stats['total_downloads'] >= total_operations, "总下载数统计错误"
    assert perf_stats['successful_downloads'] >= successful_operations, "成功下载数统计错误"
    assert perf_stats['success_rate'] >= 0 and perf_stats['success_rate'] <= 1, "成功率范围错误"
    assert perf_stats['uptime_seconds'] > 0, "运行时间统计错误"
    
    print("✓ 基本统计验证通过")
    
    # 检查缓存统计
    cache_stats = downloader.get_cache_stats()
    total_cache_requests = cache_stats['hits'] + cache_stats['misses']
    
    if total_cache_requests > 0:
        expected_hit_rate = cache_stats['hits'] / total_cache_requests
        assert abs(perf_stats['cache_hit_rate'] - expected_hit_rate) < 0.001, "缓存命中率计算错误"
        print("✓ 缓存统计验证通过")
    
    # 检查性能指标
    if perf_stats['successful_downloads'] > 0:
        assert perf_stats['avg_download_time'] > 0, "平均下载时间应大于0"
        assert perf_stats['avg_records_per_download'] >= 0, "平均记录数应大于等于0"
        print("✓ 性能指标验证通过")
    
    print("\n✅ 性能监控功能测试通过")
    return True

def test_performance_comparison():
    """测试性能对比（缓存 vs 非缓存）"""
    print("=" * 60)
    print("性能对比测试（缓存 vs 非缓存）")
    print("=" * 60)
    
    downloader = ChinextMinuteDataDownloader()
    
    test_stock = "300001.SZ"
    test_date = "2025-06-27"
    
    print(f"测试股票: {test_stock}")
    print(f"测试日期: {test_date}")
    print()
    
    # 第一阶段：无缓存测试
    print("第一阶段：清空缓存，测试无缓存性能...")
    downloader.clear_cache()
    
    start_time = time.time()
    success1, count1, msg1 = downloader.download_stock_minute_data(test_stock, test_date)
    time1 = time.time() - start_time
    
    perf_stats1 = downloader.get_performance_stats()
    
    print(f"  结果: {'成功' if success1 else '失败'}")
    print(f"  记录数: {count1}")
    print(f"  耗时: {time1:.4f}秒")
    print(f"  网络请求数: {perf_stats1['network_requests']}")
    print(f"  数据库操作数: {perf_stats1['database_operations']}")
    print()
    
    # 第二阶段：缓存测试
    print("第二阶段：测试缓存性能...")
    
    start_time = time.time()
    success2, count2, msg2 = downloader.download_stock_minute_data(test_stock, test_date)
    time2 = time.time() - start_time
    
    perf_stats2 = downloader.get_performance_stats()
    
    print(f"  结果: {'成功' if success2 else '失败'}")
    print(f"  记录数: {count2}")
    print(f"  耗时: {time2:.4f}秒")
    print(f"  消息: {msg2}")
    print(f"  网络请求数: {perf_stats2['network_requests']}")
    print(f"  数据库操作数: {perf_stats2['database_operations']}")
    print()
    
    # 性能对比分析
    print("性能对比分析:")
    if time1 > 0 and time2 > 0:
        speedup = time1 / time2 if time2 > 0 else float('inf')
        time_saved_pct = ((time1 - time2) / time1 * 100) if time1 > 0 else 0
        
        print(f"  第一次耗时: {time1:.4f}秒")
        print(f"  第二次耗时: {time2:.4f}秒")
        print(f"  性能提升: {speedup:.1f}倍")
        print(f"  时间节省: {time_saved_pct:.1f}%")
        
        # 验证缓存效果
        if "缓存命中" in msg2:
            print("  ✓ 缓存命中验证通过")
            assert speedup > 1, "缓存应该提升性能"
            print("  ✓ 性能提升验证通过")
        else:
            print("  ⚠️ 未检测到缓存命中")
    
    # 最终性能报告
    print("\n最终性能统计:")
    downloader.print_performance_report()
    
    print("\n✅ 性能对比测试完成")
    return True

def test_batch_performance():
    """测试批量操作性能"""
    print("=" * 60)
    print("批量操作性能测试")
    print("=" * 60)
    
    downloader = ChinextMinuteDataDownloader()
    
    test_stocks = ["300001.SZ", "300002.SZ", "300015.SZ"]
    test_dates = ["2025-06-27", "2025-06-26"]
    
    print(f"测试股票: {test_stocks}")
    print(f"测试日期: {test_dates}")
    print()
    
    # 清空缓存
    downloader.clear_cache()
    
    print("开始批量下载...")
    start_time = time.time()
    
    result = downloader.batch_download_minute_data(test_stocks, test_dates)
    
    batch_time = time.time() - start_time
    
    print(f"批量下载完成，耗时: {batch_time:.2f}秒")
    print(f"成功率: {result['success_rate']:.1%}")
    print()
    
    # 获取性能统计
    perf_stats = downloader.get_performance_stats()
    
    print("批量操作性能分析:")
    print(f"  总任务数: {result['total']}")
    print(f"  成功数: {result['success']}")
    print(f"  失败数: {result['failed']}")
    print(f"  平均每任务耗时: {batch_time/result['total']:.3f}秒")
    print(f"  总记录数: {perf_stats['total_records_downloaded']}")
    print(f"  记录处理速度: {perf_stats['records_per_second']:.0f}条/秒")
    print(f"  网络请求数: {perf_stats['network_requests']}")
    print(f"  数据库操作数: {perf_stats['database_operations']}")
    print()
    
    # 性能报告
    downloader.print_performance_report()
    
    print("\n✅ 批量操作性能测试完成")
    return True

def main():
    """主测试函数"""
    print("开始系统性能监控测试...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # 测试性能监控功能
        test1_result = test_performance_monitoring()
        print()
        
        # 测试性能对比
        test2_result = test_performance_comparison()
        print()
        
        # 测试批量操作性能
        test3_result = test_batch_performance()
        print()
        
        # 总结
        all_passed = test1_result and test2_result and test3_result
        print("=" * 60)
        print(f"系统性能监控测试结果: {'全部通过' if all_passed else '部分失败'}")
        print("=" * 60)
        
        if all_passed:
            print("✅ 性能监控系统工作正常")
            print("主要功能:")
            print("  - 实时性能统计")
            print("  - 缓存效率监控")
            print("  - 网络和数据库操作统计")
            print("  - 详细性能报告")
        else:
            print("❌ 性能监控系统存在问题")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

"""
增强的数据验证器 - 确保数据完整性和真实性

专为A股数据设计的严格验证机制，包括：
- 价格数据合理性检查
- 成交量异常检测
- 涨跌停限制验证
- 数据连续性检查
- 异常值检测和处理
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta, date
from ..config.settings import Config
from .logger import get_validator_logger

class EnhancedDataValidator:
    """增强的数据验证器"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        初始化增强验证器
        
        参数:
            config: 配置实例
        """
        self.config = config or Config()
        self.logger = get_validator_logger()
        
        # A股市场特定的验证规则
        self.validation_rules = {
            'max_daily_change': 0.20,  # 最大日涨跌幅20%（包含ST股票）
            'normal_daily_change': 0.10,  # 普通股票涨跌停10%
            'st_daily_change': 0.05,  # ST股票涨跌停5%
            'min_price': 0.01,  # 最低价格1分钱
            'max_price': 10000.0,  # 最高价格上限
            'min_volume': 0,  # 最小成交量
            'max_volume_spike': 100,  # 最大成交量激增倍数
            'max_gap_days': 30,  # 最大数据间隔天数
        }
    
    def validate_stock_data(self, data: pd.DataFrame, stock_code: str) -> Tuple[bool, List[str], Dict[str, Any]]:
        """
        全面验证股票数据
        
        参数:
            data: 股票数据DataFrame
            stock_code: 股票代码
            
        返回:
            Tuple[bool, List[str], Dict]: (是否有效, 错误列表, 验证统计)
        """
        errors = []
        stats = {
            'total_records': len(data),
            'date_range': None,
            'price_range': None,
            'volume_stats': None,
            'anomalies': {}
        }
        
        if data.empty:
            errors.append("数据为空")
            return False, errors, stats
        
        # 基础信息统计
        stats['date_range'] = {
            'start': data.index.min() if isinstance(data.index, pd.DatetimeIndex) else None,
            'end': data.index.max() if isinstance(data.index, pd.DatetimeIndex) else None
        }
        
        # 1. 验证必需列
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            errors.append(f"缺少必需列: {missing_columns}")
            return False, errors, stats
        
        # 2. 验证OHLC关系
        ohlc_errors = self._validate_ohlc_logic(data)
        if ohlc_errors:
            errors.extend(ohlc_errors)
            stats['anomalies']['ohlc_violations'] = len(ohlc_errors)
        
        # 3. 验证价格合理性
        price_errors, price_stats = self._validate_price_reasonableness(data, stock_code)
        if price_errors:
            errors.extend(price_errors)
        stats['price_range'] = price_stats
        
        # 4. 验证涨跌停限制
        limit_errors = self._validate_price_limits(data, stock_code)
        if limit_errors:
            errors.extend(limit_errors)
            stats['anomalies']['limit_violations'] = len(limit_errors)
        
        # 5. 验证成交量
        volume_errors, volume_stats = self._validate_volume_logic(data)
        if volume_errors:
            errors.extend(volume_errors)
        stats['volume_stats'] = volume_stats
        
        # 6. 验证数据连续性
        continuity_errors = self._validate_data_continuity(data)
        if continuity_errors:
            errors.extend(continuity_errors)
            stats['anomalies']['continuity_issues'] = len(continuity_errors)
        
        # 7. 验证异常值
        outlier_errors = self._detect_outliers(data, stock_code)
        if outlier_errors:
            errors.extend(outlier_errors)
            stats['anomalies']['outliers'] = len(outlier_errors)
        
        is_valid = len(errors) == 0
        
        if not is_valid:
            self.logger.warning(f"数据验证失败 {stock_code}: {len(errors)} 个问题")
        else:
            self.logger.info(f"数据验证通过 {stock_code}: {len(data)} 条记录")
        
        return is_valid, errors, stats
    
    def _validate_ohlc_logic(self, data: pd.DataFrame) -> List[str]:
        """验证OHLC逻辑关系"""
        errors = []
        
        # 检查每一行的OHLC关系
        invalid_count = 0
        for idx, row in data.iterrows():
            # 最高价应该是四个价格中的最高值
            if not (row['high'] >= max(row['open'], row['close'], row['low'])):
                invalid_count += 1
            
            # 最低价应该是四个价格中的最低值
            if not (row['low'] <= min(row['open'], row['close'], row['high'])):
                invalid_count += 1
        
        if invalid_count > 0:
            errors.append(f"OHLC关系异常: {invalid_count} 条记录")
        
        return errors
    
    def _validate_price_reasonableness(self, data: pd.DataFrame, stock_code: str) -> Tuple[List[str], Dict]:
        """验证价格合理性"""
        errors = []
        stats = {}
        
        price_columns = ['open', 'high', 'low', 'close']
        
        for col in price_columns:
            if col in data.columns:
                prices = data[col]
                
                # 统计信息
                stats[col] = {
                    'min': float(prices.min()),
                    'max': float(prices.max()),
                    'mean': float(prices.mean()),
                    'std': float(prices.std())
                }
                
                # 检查负价格
                negative_count = (prices < 0).sum()
                if negative_count > 0:
                    errors.append(f"{col}存在负价格: {negative_count} 条")
                
                # 检查零价格
                zero_count = (prices == 0).sum()
                if zero_count > 0:
                    errors.append(f"{col}存在零价格: {zero_count} 条")
                
                # 检查异常高价格
                high_price_count = (prices > self.validation_rules['max_price']).sum()
                if high_price_count > 0:
                    errors.append(f"{col}存在异常高价格(>{self.validation_rules['max_price']}): {high_price_count} 条")
                
                # 检查异常低价格
                low_price_count = (prices < self.validation_rules['min_price']).sum()
                if low_price_count > 0:
                    errors.append(f"{col}存在异常低价格(<{self.validation_rules['min_price']}): {low_price_count} 条")
        
        return errors, stats
    
    def _validate_price_limits(self, data: pd.DataFrame, stock_code: str) -> List[str]:
        """验证涨跌停限制"""
        errors = []
        
        if len(data) < 2:
            return errors
        
        # 计算日收益率
        data_sorted = data.sort_index()
        prev_close = data_sorted['close'].shift(1)
        daily_returns = (data_sorted['close'] - prev_close) / prev_close
        
        # 判断是否为ST股票
        is_st_stock = stock_code.upper().startswith(('ST', '*ST')) or 'ST' in stock_code.upper()
        max_change = self.validation_rules['st_daily_change'] if is_st_stock else self.validation_rules['normal_daily_change']
        
        # 检查超过涨跌停限制的情况
        extreme_changes = daily_returns.abs() > max_change
        extreme_count = extreme_changes.sum()
        
        if extreme_count > 0:
            # 允许少量的涨跌停突破（可能是除权除息等特殊情况）
            tolerance = max(1, len(data) * 0.01)  # 允许1%的异常
            if extreme_count > tolerance:
                errors.append(f"涨跌停限制异常: {extreme_count} 次超过{max_change:.1%}限制")
        
        return errors
    
    def _validate_volume_logic(self, data: pd.DataFrame) -> Tuple[List[str], Dict]:
        """验证成交量逻辑"""
        errors = []
        stats = {}
        
        if 'volume' not in data.columns:
            return errors, stats
        
        volume = data['volume']
        
        # 统计信息
        stats = {
            'min': float(volume.min()),
            'max': float(volume.max()),
            'mean': float(volume.mean()),
            'median': float(volume.median()),
            'std': float(volume.std())
        }
        
        # 检查负成交量
        negative_count = (volume < 0).sum()
        if negative_count > 0:
            errors.append(f"负成交量: {negative_count} 条记录")
        
        # 检查零成交量（在交易日不应该太多）
        zero_count = (volume == 0).sum()
        if zero_count > len(data) * 0.05:  # 超过5%的零成交量
            errors.append(f"零成交量过多: {zero_count} 条记录 ({zero_count/len(data):.1%})")
        
        # 检查成交量异常激增
        if len(data) > 1 and stats['median'] > 0:
            volume_ratio = volume / stats['median']
            spike_count = (volume_ratio > self.validation_rules['max_volume_spike']).sum()
            if spike_count > 0:
                errors.append(f"成交量异常激增: {spike_count} 次超过中位数{self.validation_rules['max_volume_spike']}倍")
        
        return errors, stats
    
    def _validate_data_continuity(self, data: pd.DataFrame) -> List[str]:
        """验证数据连续性"""
        errors = []
        
        if not isinstance(data.index, pd.DatetimeIndex) or len(data) < 2:
            return errors
        
        # 检查日期排序
        if not data.index.is_monotonic_increasing:
            errors.append("日期索引未按时间顺序排列")
        
        # 检查重复日期
        duplicate_count = data.index.duplicated().sum()
        if duplicate_count > 0:
            errors.append(f"重复日期: {duplicate_count} 个")
        
        # 检查数据间隔
        date_diffs = data.index[1:] - data.index[:-1]
        large_gaps = date_diffs > timedelta(days=self.validation_rules['max_gap_days'])
        
        if large_gaps.any():
            gap_count = large_gaps.sum()
            max_gap = date_diffs.max().days
            errors.append(f"数据间隔过大: {gap_count} 个间隔超过{self.validation_rules['max_gap_days']}天，最大间隔{max_gap}天")
        
        return errors
    
    def _detect_outliers(self, data: pd.DataFrame, stock_code: str) -> List[str]:
        """检测异常值"""
        errors = []
        
        price_columns = ['open', 'high', 'low', 'close']
        
        for col in price_columns:
            if col not in data.columns:
                continue
            
            prices = data[col]
            
            # 使用IQR方法检测异常值
            Q1 = prices.quantile(0.25)
            Q3 = prices.quantile(0.75)
            IQR = Q3 - Q1
            
            if IQR > 0:
                lower_bound = Q1 - 3 * IQR  # 使用3倍IQR作为阈值
                upper_bound = Q3 + 3 * IQR
                
                outliers = ((prices < lower_bound) | (prices > upper_bound)).sum()
                if outliers > 0:
                    outlier_ratio = outliers / len(prices)
                    if outlier_ratio > 0.05:  # 超过5%的异常值
                        errors.append(f"{col}异常值过多: {outliers} 个 ({outlier_ratio:.1%})")
        
        return errors
    
    def clean_data_conservative(self, data: pd.DataFrame, stock_code: str) -> Tuple[pd.DataFrame, Dict[str, int]]:
        """
        保守的数据清洗
        
        参数:
            data: 原始数据
            stock_code: 股票代码
            
        返回:
            Tuple[pd.DataFrame, Dict]: (清洗后数据, 清洗统计)
        """
        if data.empty:
            return data, {}
        
        cleaned_data = data.copy()
        cleaning_stats = {
            'original_rows': len(data),
            'removed_duplicates': 0,
            'filled_missing': 0,
            'removed_invalid': 0,
            'final_rows': 0
        }
        
        # 1. 移除重复日期（保留第一个）
        if isinstance(cleaned_data.index, pd.DatetimeIndex):
            before_dedup = len(cleaned_data)
            cleaned_data = cleaned_data[~cleaned_data.index.duplicated(keep='first')]
            cleaning_stats['removed_duplicates'] = before_dedup - len(cleaned_data)
        
        # 2. 保守的缺失值处理（只填充少量连续缺失）
        price_columns = ['open', 'high', 'low', 'close']
        available_price_cols = [col for col in price_columns if col in cleaned_data.columns]
        
        if available_price_cols:
            before_fill = cleaned_data[available_price_cols].isnull().sum().sum()
            # 只前向填充最多2个连续缺失值
            cleaned_data[available_price_cols] = cleaned_data[available_price_cols].ffill(limit=2)
            after_fill = cleaned_data[available_price_cols].isnull().sum().sum()
            cleaning_stats['filled_missing'] = before_fill - after_fill
        
        # 3. 移除明显无效的行
        before_invalid = len(cleaned_data)
        
        # 移除所有价格列都为NaN的行
        cleaned_data = cleaned_data.dropna(subset=available_price_cols, how='all')
        
        # 移除价格为负数或零的行
        for col in available_price_cols:
            if col in cleaned_data.columns:
                cleaned_data = cleaned_data[cleaned_data[col] > 0]
        
        cleaning_stats['removed_invalid'] = before_invalid - len(cleaned_data)
        cleaning_stats['final_rows'] = len(cleaned_data)
        
        # 记录清洗结果
        if cleaning_stats['removed_duplicates'] > 0 or cleaning_stats['filled_missing'] > 0 or cleaning_stats['removed_invalid'] > 0:
            self.logger.info(f"数据清洗完成 {stock_code}: "
                           f"原始{cleaning_stats['original_rows']}行 -> "
                           f"最终{cleaning_stats['final_rows']}行 "
                           f"(去重{cleaning_stats['removed_duplicates']}, "
                           f"填充{cleaning_stats['filled_missing']}, "
                           f"移除无效{cleaning_stats['removed_invalid']})")
        
        return cleaned_data, cleaning_stats

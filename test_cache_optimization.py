#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存优化测试脚本
测试数据缓存策略的有效性
"""

import sys
import os
import time
import pandas as pd
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.quantization.strategies.chinext_minute_data_downloader import ChinextMinuteDataDownloader

def test_cache_functionality():
    """测试缓存功能"""
    print("=" * 60)
    print("缓存功能测试")
    print("=" * 60)
    
    # 创建下载器实例
    downloader = ChinextMinuteDataDownloader()
    
    # 测试股票和日期
    test_stock = "300001.SZ"
    test_date = "2024-06-27"  # 使用交易日
    
    print(f"测试股票: {test_stock}")
    print(f"测试日期: {test_date}")
    print()
    
    # 清空缓存，确保测试环境干净
    downloader.clear_cache()
    
    # 第一次下载（应该从网络获取）
    print("第一次下载（从网络获取）...")
    start_time = time.time()
    success1, count1, msg1 = downloader.download_stock_minute_data(test_stock, test_date)
    time1 = time.time() - start_time
    
    print(f"  结果: {'成功' if success1 else '失败'}")
    print(f"  记录数: {count1}")
    print(f"  消息: {msg1}")
    print(f"  耗时: {time1:.2f}秒")
    print()
    
    # 获取缓存统计
    cache_stats = downloader.get_cache_stats()
    print("缓存统计（第一次下载后）:")
    for key, value in cache_stats.items():
        print(f"  {key}: {value}")
    print()
    
    # 第二次下载（应该从缓存获取）
    print("第二次下载（从缓存获取）...")
    start_time = time.time()
    success2, count2, msg2 = downloader.download_stock_minute_data(test_stock, test_date)
    time2 = time.time() - start_time
    
    print(f"  结果: {'成功' if success2 else '失败'}")
    print(f"  记录数: {count2}")
    print(f"  消息: {msg2}")
    print(f"  耗时: {time2:.2f}秒")
    print()
    
    # 获取缓存统计
    cache_stats = downloader.get_cache_stats()
    print("缓存统计（第二次下载后）:")
    for key, value in cache_stats.items():
        print(f"  {key}: {value}")
    print()
    
    # 性能对比
    if time1 > 0 and time2 > 0:
        speedup = time1 / time2
        print(f"性能提升: {speedup:.1f}倍")
        print(f"时间节省: {((time1 - time2) / time1 * 100):.1f}%")
    print()
    
    return success1 and success2

def test_cache_expiration():
    """测试缓存过期机制"""
    print("=" * 60)
    print("缓存过期机制测试")
    print("=" * 60)
    
    # 创建下载器实例，设置短过期时间
    downloader = ChinextMinuteDataDownloader()
    downloader.cache_expire_hours = 0.001  # 设置为3.6秒过期
    
    test_stock = "300002.SZ"
    test_date = "2024-06-27"
    
    print(f"测试股票: {test_stock}")
    print(f"测试日期: {test_date}")
    print(f"缓存过期时间: {downloader.cache_expire_hours * 3600:.1f}秒")
    print()
    
    # 清空缓存
    downloader.clear_cache()
    
    # 第一次下载
    print("第一次下载...")
    success1, count1, msg1 = downloader.download_stock_minute_data(test_stock, test_date)
    print(f"  结果: {'成功' if success1 else '失败'}")
    print(f"  消息: {msg1}")
    
    cache_stats = downloader.get_cache_stats()
    print(f"  缓存大小: {cache_stats['cache_size']}")
    print()
    
    # 等待缓存过期
    print("等待缓存过期...")
    time.sleep(4)  # 等待4秒，超过过期时间
    
    # 第二次下载（缓存应该已过期）
    print("第二次下载（缓存已过期）...")
    success2, count2, msg2 = downloader.download_stock_minute_data(test_stock, test_date)
    print(f"  结果: {'成功' if success2 else '失败'}")
    print(f"  消息: {msg2}")
    
    cache_stats = downloader.get_cache_stats()
    print("最终缓存统计:")
    for key, value in cache_stats.items():
        print(f"  {key}: {value}")
    print()
    
    return success1 and success2

def test_cache_size_limit():
    """测试缓存大小限制"""
    print("=" * 60)
    print("缓存大小限制测试")
    print("=" * 60)
    
    downloader = ChinextMinuteDataDownloader()
    downloader.clear_cache()
    
    # 模拟添加多个缓存条目
    test_stocks = [f"30000{i}.SZ" for i in range(1, 6)]
    test_date = "2024-06-27"
    
    print(f"测试股票: {test_stocks}")
    print(f"测试日期: {test_date}")
    print()
    
    # 创建模拟数据并添加到缓存
    for i, stock in enumerate(test_stocks):
        # 创建模拟数据
        mock_data = pd.DataFrame({
            'datetime': pd.date_range('2024-06-27 09:30:00', periods=10, freq='1min'),
            'open': [100 + i] * 10,
            'high': [101 + i] * 10,
            'low': [99 + i] * 10,
            'close': [100.5 + i] * 10,
            'volume': [1000] * 10,
            'amount': [100000] * 10
        })
        
        # 直接保存到缓存
        downloader._save_to_cache(stock, test_date, mock_data)
        
        cache_stats = downloader.get_cache_stats()
        print(f"添加 {stock} 后缓存大小: {cache_stats['cache_size']}")
    
    print()
    print("缓存测试完成")
    return True

def main():
    """主测试函数"""
    print("开始缓存优化测试...")
    print()
    
    try:
        # 测试基本缓存功能
        test1_result = test_cache_functionality()
        print(f"✓ 基本缓存功能测试: {'通过' if test1_result else '失败'}")
        print()
        
        # 测试缓存过期机制
        test2_result = test_cache_expiration()
        print(f"✓ 缓存过期机制测试: {'通过' if test2_result else '失败'}")
        print()
        
        # 测试缓存大小限制
        test3_result = test_cache_size_limit()
        print(f"✓ 缓存大小限制测试: {'通过' if test3_result else '失败'}")
        print()
        
        # 总结
        all_passed = test1_result and test2_result and test3_result
        print("=" * 60)
        print(f"缓存优化测试结果: {'全部通过' if all_passed else '部分失败'}")
        print("=" * 60)
        
        if all_passed:
            print("✅ 缓存系统工作正常，优化效果显著")
        else:
            print("❌ 缓存系统存在问题，需要进一步调试")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

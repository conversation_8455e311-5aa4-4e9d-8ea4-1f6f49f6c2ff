#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果可视化系统

提供回测结果、策略表现、风险指标等的可视化展示。
支持多种图表类型：净值曲线、收益分布、风险指标、相关性分析等。
"""

import warnings
from typing import Dict, Any, Optional, List, Tuple, Union
from dataclasses import dataclass
from pathlib import Path
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

from quantization.utils.logger import get_logger

# 可选依赖
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.figure import Figure
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.offline as pyo
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False


@dataclass
class VisualizationConfig:
    """可视化配置"""
    style: str = "seaborn"           # 图表样式
    color_palette: str = "husl"      # 颜色调色板
    figure_size: Tuple[int, int] = (12, 8)  # 图表尺寸
    dpi: int = 100                   # 分辨率
    save_format: str = "png"         # 保存格式
    interactive: bool = True         # 是否使用交互式图表
    chinese_font: bool = True        # 是否使用中文字体


class BaseVisualizer:
    """基础可视化器"""
    
    def __init__(self, config: Optional[VisualizationConfig] = None):
        self.logger = get_logger(self.__class__.__name__)
        self.config = config or VisualizationConfig()
        
        # 检查依赖
        if not MATPLOTLIB_AVAILABLE and not PLOTLY_AVAILABLE:
            raise ImportError("需要安装matplotlib或plotly: pip install matplotlib plotly")
        
        # 设置样式
        if MATPLOTLIB_AVAILABLE:
            self._setup_matplotlib_style()
    
    def _setup_matplotlib_style(self):
        """设置matplotlib样式"""
        try:
            if self.config.style in plt.style.available:
                plt.style.use(self.config.style)
            
            if MATPLOTLIB_AVAILABLE and hasattr(sns, 'set_palette'):
                sns.set_palette(self.config.color_palette)
                
        except Exception as e:
            self.logger.warning(f"设置matplotlib样式失败: {e}")
    
    def save_figure(self, fig, filename: str, output_dir: str = "output/charts"):
        """保存图表"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            file_path = output_path / f"{filename}.{self.config.save_format}"
            
            if hasattr(fig, 'savefig'):  # matplotlib
                fig.savefig(file_path, dpi=self.config.dpi, bbox_inches='tight')
            elif hasattr(fig, 'write_html'):  # plotly
                if self.config.save_format.lower() in ['html']:
                    fig.write_html(str(file_path.with_suffix('.html')))
                else:
                    fig.write_image(str(file_path))
            
            self.logger.info(f"图表已保存: {file_path}")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"保存图表失败: {e}")
            return None


class BacktestVisualizer(BaseVisualizer):
    """回测结果可视化器"""
    
    def plot_equity_curve(self, results: Dict[str, Any], 
                         benchmark_data: Optional[pd.DataFrame] = None,
                         save_path: Optional[str] = None) -> Optional[str]:
        """
        绘制净值曲线
        
        Args:
            results: 回测结果
            benchmark_data: 基准数据
            save_path: 保存路径
            
        Returns:
            图表文件路径
        """
        try:
            if self.config.interactive and PLOTLY_AVAILABLE:
                return self._plot_equity_curve_plotly(results, benchmark_data, save_path)
            elif MATPLOTLIB_AVAILABLE:
                return self._plot_equity_curve_matplotlib(results, benchmark_data, save_path)
            else:
                self.logger.error("没有可用的绘图库")
                return None
                
        except Exception as e:
            self.logger.error(f"绘制净值曲线失败: {e}")
            return None
    
    def _plot_equity_curve_matplotlib(self, results: Dict[str, Any],
                                    benchmark_data: Optional[pd.DataFrame] = None,
                                    save_path: Optional[str] = None) -> Optional[str]:
        """使用matplotlib绘制净值曲线"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.config.figure_size, 
                                      height_ratios=[3, 1])
        
        # 获取数据
        portfolio_data = pd.DataFrame(results.get('portfolio_history', []))
        if portfolio_data.empty:
            self.logger.warning("没有投资组合历史数据")
            return None
        
        portfolio_data['date'] = pd.to_datetime(portfolio_data['date'])
        portfolio_data.set_index('date', inplace=True)
        
        # 计算累计收益率
        portfolio_data['cumulative_return'] = (1 + portfolio_data['daily_return']).cumprod()
        
        # 绘制净值曲线
        ax1.plot(portfolio_data.index, portfolio_data['cumulative_return'], 
                label='策略净值', linewidth=2, color='blue')
        
        # 绘制基准
        if benchmark_data is not None and not benchmark_data.empty:
            benchmark_data['date'] = pd.to_datetime(benchmark_data['date'])
            benchmark_data.set_index('date', inplace=True)
            benchmark_data['cumulative_return'] = (1 + benchmark_data['return']).cumprod()
            
            ax1.plot(benchmark_data.index, benchmark_data['cumulative_return'],
                    label='基准', linewidth=2, color='red', alpha=0.7)
        
        ax1.set_title('策略净值曲线', fontsize=16, fontweight='bold')
        ax1.set_ylabel('累计收益率', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 绘制回撤
        running_max = portfolio_data['cumulative_return'].expanding().max()
        drawdown = (portfolio_data['cumulative_return'] - running_max) / running_max
        
        ax2.fill_between(portfolio_data.index, drawdown, 0, 
                        color='red', alpha=0.3, label='回撤')
        ax2.set_title('回撤曲线', fontsize=14)
        ax2.set_ylabel('回撤', fontsize=12)
        ax2.set_xlabel('日期', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 格式化日期轴
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        
        plt.tight_layout()
        
        # 保存图表
        if save_path:
            # 分离路径和文件名
            save_path_obj = Path(save_path)
            output_dir = str(save_path_obj.parent)
            filename = save_path_obj.name
            return self.save_figure(fig, filename, output_dir)

        return fig
    
    def _plot_equity_curve_plotly(self, results: Dict[str, Any],
                                benchmark_data: Optional[pd.DataFrame] = None,
                                save_path: Optional[str] = None) -> Optional[str]:
        """使用plotly绘制净值曲线"""
        # 创建子图
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('策略净值曲线', '回撤曲线'),
            vertical_spacing=0.1,
            row_heights=[0.7, 0.3]
        )
        
        # 获取数据
        portfolio_data = pd.DataFrame(results.get('portfolio_history', []))
        if portfolio_data.empty:
            self.logger.warning("没有投资组合历史数据")
            return None
        
        portfolio_data['date'] = pd.to_datetime(portfolio_data['date'])
        portfolio_data['cumulative_return'] = (1 + portfolio_data['daily_return']).cumprod()
        
        # 添加策略净值曲线
        fig.add_trace(
            go.Scatter(
                x=portfolio_data['date'],
                y=portfolio_data['cumulative_return'],
                mode='lines',
                name='策略净值',
                line=dict(color='blue', width=2)
            ),
            row=1, col=1
        )
        
        # 添加基准
        if benchmark_data is not None and not benchmark_data.empty:
            benchmark_data['date'] = pd.to_datetime(benchmark_data['date'])
            benchmark_data['cumulative_return'] = (1 + benchmark_data['return']).cumprod()
            
            fig.add_trace(
                go.Scatter(
                    x=benchmark_data['date'],
                    y=benchmark_data['cumulative_return'],
                    mode='lines',
                    name='基准',
                    line=dict(color='red', width=2, dash='dash')
                ),
                row=1, col=1
            )
        
        # 计算回撤
        running_max = portfolio_data['cumulative_return'].expanding().max()
        drawdown = (portfolio_data['cumulative_return'] - running_max) / running_max
        
        # 添加回撤曲线
        fig.add_trace(
            go.Scatter(
                x=portfolio_data['date'],
                y=drawdown,
                mode='lines',
                name='回撤',
                fill='tonexty',
                line=dict(color='red'),
                fillcolor='rgba(255, 0, 0, 0.3)'
            ),
            row=2, col=1
        )
        
        # 更新布局
        fig.update_layout(
            title='策略回测结果',
            height=600,
            showlegend=True,
            hovermode='x unified'
        )
        
        fig.update_xaxes(title_text="日期", row=2, col=1)
        fig.update_yaxes(title_text="累计收益率", row=1, col=1)
        fig.update_yaxes(title_text="回撤", row=2, col=1)
        
        # 保存图表
        if save_path:
            save_path_obj = Path(save_path)
            output_dir = str(save_path_obj.parent)
            filename = save_path_obj.name
            return self.save_figure(fig, filename, output_dir)

        return fig
    
    def plot_returns_distribution(self, results: Dict[str, Any],
                                save_path: Optional[str] = None) -> Optional[str]:
        """绘制收益率分布"""
        try:
            portfolio_data = pd.DataFrame(results.get('portfolio_history', []))
            if portfolio_data.empty:
                return None
            
            returns = portfolio_data['daily_return'].dropna()
            
            if self.config.interactive and PLOTLY_AVAILABLE:
                fig = go.Figure()
                
                # 直方图
                fig.add_trace(go.Histogram(
                    x=returns,
                    nbinsx=50,
                    name='收益率分布',
                    opacity=0.7
                ))
                
                # 正态分布拟合
                mu, sigma = returns.mean(), returns.std()
                x_norm = np.linspace(returns.min(), returns.max(), 100)
                y_norm = ((1 / (sigma * np.sqrt(2 * np.pi))) * 
                         np.exp(-0.5 * ((x_norm - mu) / sigma) ** 2))
                
                fig.add_trace(go.Scatter(
                    x=x_norm,
                    y=y_norm * len(returns) * (returns.max() - returns.min()) / 50,
                    mode='lines',
                    name='正态分布拟合',
                    line=dict(color='red', width=2)
                ))
                
                fig.update_layout(
                    title='日收益率分布',
                    xaxis_title='日收益率',
                    yaxis_title='频次',
                    showlegend=True
                )
                
            else:
                fig, ax = plt.subplots(figsize=self.config.figure_size)
                
                # 直方图
                ax.hist(returns, bins=50, alpha=0.7, density=True, label='收益率分布')
                
                # 正态分布拟合
                mu, sigma = returns.mean(), returns.std()
                x_norm = np.linspace(returns.min(), returns.max(), 100)
                y_norm = ((1 / (sigma * np.sqrt(2 * np.pi))) * 
                         np.exp(-0.5 * ((x_norm - mu) / sigma) ** 2))
                ax.plot(x_norm, y_norm, 'r-', linewidth=2, label='正态分布拟合')
                
                ax.set_title('日收益率分布', fontsize=16, fontweight='bold')
                ax.set_xlabel('日收益率', fontsize=12)
                ax.set_ylabel('密度', fontsize=12)
                ax.legend()
                ax.grid(True, alpha=0.3)
            
            if save_path:
                save_path_obj = Path(save_path)
                output_dir = str(save_path_obj.parent)
                filename = save_path_obj.name
                return self.save_figure(fig, filename, output_dir)

            return fig
            
        except Exception as e:
            self.logger.error(f"绘制收益率分布失败: {e}")
            return None
    
    def plot_risk_metrics(self, results: Dict[str, Any],
                         save_path: Optional[str] = None) -> Optional[str]:
        """绘制风险指标"""
        try:
            metrics = results.get('performance_metrics', {})
            if not metrics:
                return None
            
            # 准备数据
            risk_metrics = {
                '年化收益率': metrics.get('annualized_return', 0),
                '年化波动率': metrics.get('annualized_volatility', 0),
                '夏普比率': metrics.get('sharpe_ratio', 0),
                '最大回撤': abs(metrics.get('max_drawdown', 0)),
                '胜率': metrics.get('win_rate', 0),
                'Calmar比率': metrics.get('calmar_ratio', 0)
            }
            
            if self.config.interactive and PLOTLY_AVAILABLE:
                fig = go.Figure(data=[
                    go.Bar(
                        x=list(risk_metrics.keys()),
                        y=list(risk_metrics.values()),
                        marker_color=['green' if v >= 0 else 'red' for v in risk_metrics.values()]
                    )
                ])
                
                fig.update_layout(
                    title='风险指标概览',
                    xaxis_title='指标',
                    yaxis_title='数值',
                    showlegend=False
                )
                
            else:
                fig, ax = plt.subplots(figsize=self.config.figure_size)
                
                colors = ['green' if v >= 0 else 'red' for v in risk_metrics.values()]
                bars = ax.bar(risk_metrics.keys(), risk_metrics.values(), color=colors, alpha=0.7)
                
                # 添加数值标签
                for bar, value in zip(bars, risk_metrics.values()):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{value:.4f}', ha='center', va='bottom')
                
                ax.set_title('风险指标概览', fontsize=16, fontweight='bold')
                ax.set_ylabel('数值', fontsize=12)
                ax.grid(True, alpha=0.3)
                plt.xticks(rotation=45)
                plt.tight_layout()
            
            if save_path:
                save_path_obj = Path(save_path)
                output_dir = str(save_path_obj.parent)
                filename = save_path_obj.name
                return self.save_figure(fig, filename, output_dir)

            return fig
            
        except Exception as e:
            self.logger.error(f"绘制风险指标失败: {e}")
            return None
    
    def plot_monthly_returns(self, results: Dict[str, Any],
                           save_path: Optional[str] = None) -> Optional[str]:
        """绘制月度收益率热力图"""
        try:
            portfolio_data = pd.DataFrame(results.get('portfolio_history', []))
            if portfolio_data.empty:
                return None
            
            portfolio_data['date'] = pd.to_datetime(portfolio_data['date'])
            portfolio_data.set_index('date', inplace=True)
            
            # 计算月度收益率
            monthly_returns = portfolio_data['daily_return'].resample('M').apply(
                lambda x: (1 + x).prod() - 1
            )
            
            # 创建年月矩阵
            monthly_returns.index = monthly_returns.index.to_period('M')
            monthly_data = monthly_returns.to_frame('return')
            monthly_data['year'] = monthly_data.index.year
            monthly_data['month'] = monthly_data.index.month
            
            pivot_table = monthly_data.pivot(index='year', columns='month', values='return')
            
            if self.config.interactive and PLOTLY_AVAILABLE:
                fig = go.Figure(data=go.Heatmap(
                    z=pivot_table.values,
                    x=[f'{i}月' for i in range(1, 13)],
                    y=pivot_table.index,
                    colorscale='RdYlGn',
                    zmid=0,
                    text=np.round(pivot_table.values * 100, 2),
                    texttemplate='%{text}%',
                    textfont={"size": 10},
                    hoverongaps=False
                ))
                
                fig.update_layout(
                    title='月度收益率热力图',
                    xaxis_title='月份',
                    yaxis_title='年份'
                )
                
            else:
                fig, ax = plt.subplots(figsize=self.config.figure_size)
                
                sns.heatmap(pivot_table, annot=True, fmt='.2%', cmap='RdYlGn',
                           center=0, ax=ax, cbar_kws={'label': '月度收益率'})
                
                ax.set_title('月度收益率热力图', fontsize=16, fontweight='bold')
                ax.set_xlabel('月份', fontsize=12)
                ax.set_ylabel('年份', fontsize=12)
            
            if save_path:
                save_path_obj = Path(save_path)
                output_dir = str(save_path_obj.parent)
                filename = save_path_obj.name
                return self.save_figure(fig, filename, output_dir)

            return fig
            
        except Exception as e:
            self.logger.error(f"绘制月度收益率热力图失败: {e}")
            return None


class PortfolioVisualizer(BaseVisualizer):
    """投资组合可视化器"""
    
    def plot_portfolio_composition(self, positions: Dict[str, float],
                                 save_path: Optional[str] = None) -> Optional[str]:
        """绘制投资组合构成饼图"""
        try:
            if not positions:
                return None
            
            # 过滤小权重
            filtered_positions = {k: v for k, v in positions.items() if abs(v) > 0.01}
            
            if self.config.interactive and PLOTLY_AVAILABLE:
                fig = go.Figure(data=[go.Pie(
                    labels=list(filtered_positions.keys()),
                    values=list(filtered_positions.values()),
                    hole=0.3
                )])
                
                fig.update_layout(
                    title='投资组合构成',
                    showlegend=True
                )
                
            else:
                fig, ax = plt.subplots(figsize=self.config.figure_size)
                
                wedges, texts, autotexts = ax.pie(
                    filtered_positions.values(),
                    labels=filtered_positions.keys(),
                    autopct='%1.1f%%',
                    startangle=90
                )
                
                ax.set_title('投资组合构成', fontsize=16, fontweight='bold')
            
            if save_path:
                save_path_obj = Path(save_path)
                output_dir = str(save_path_obj.parent)
                filename = save_path_obj.name
                return self.save_figure(fig, filename, output_dir)

            return fig
            
        except Exception as e:
            self.logger.error(f"绘制投资组合构成失败: {e}")
            return None
    
    def plot_sector_allocation(self, sector_data: Dict[str, float],
                             save_path: Optional[str] = None) -> Optional[str]:
        """绘制行业配置"""
        try:
            if not sector_data:
                return None
            
            if self.config.interactive and PLOTLY_AVAILABLE:
                fig = go.Figure(data=[go.Bar(
                    x=list(sector_data.keys()),
                    y=list(sector_data.values()),
                    marker_color='lightblue'
                )])
                
                fig.update_layout(
                    title='行业配置',
                    xaxis_title='行业',
                    yaxis_title='权重 (%)',
                    showlegend=False
                )
                
            else:
                fig, ax = plt.subplots(figsize=self.config.figure_size)
                
                bars = ax.bar(sector_data.keys(), sector_data.values(), 
                             color='lightblue', alpha=0.7)
                
                # 添加数值标签
                for bar, value in zip(bars, sector_data.values()):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{value:.1f}%', ha='center', va='bottom')
                
                ax.set_title('行业配置', fontsize=16, fontweight='bold')
                ax.set_ylabel('权重 (%)', fontsize=12)
                plt.xticks(rotation=45)
                plt.tight_layout()
            
            if save_path:
                save_path_obj = Path(save_path)
                output_dir = str(save_path_obj.parent)
                filename = save_path_obj.name
                return self.save_figure(fig, filename, output_dir)

            return fig
            
        except Exception as e:
            self.logger.error(f"绘制行业配置失败: {e}")
            return None


class ResultVisualizer:
    """
    结果可视化管理器
    
    统一管理各种可视化功能，提供简单易用的接口。
    """
    
    def __init__(self, config: Optional[VisualizationConfig] = None):
        self.logger = get_logger(self.__class__.__name__)
        self.config = config or VisualizationConfig()
        
        # 初始化各种可视化器
        self.backtest_visualizer = BacktestVisualizer(self.config)
        self.portfolio_visualizer = PortfolioVisualizer(self.config)
    
    def create_backtest_report(self, results: Dict[str, Any],
                             output_dir: str = "output/reports") -> Dict[str, str]:
        """
        创建完整的回测报告
        
        Args:
            results: 回测结果
            output_dir: 输出目录
            
        Returns:
            生成的图表文件路径字典
        """
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            report_files = {}
            
            # 净值曲线
            equity_path = self.backtest_visualizer.plot_equity_curve(
                results, save_path=str(output_path / "equity_curve")
            )
            if equity_path:
                report_files['equity_curve'] = equity_path

            # 收益率分布
            returns_dist_path = self.backtest_visualizer.plot_returns_distribution(
                results, save_path=str(output_path / "returns_distribution")
            )
            if returns_dist_path:
                report_files['returns_distribution'] = returns_dist_path

            # 风险指标
            risk_metrics_path = self.backtest_visualizer.plot_risk_metrics(
                results, save_path=str(output_path / "risk_metrics")
            )
            if risk_metrics_path:
                report_files['risk_metrics'] = risk_metrics_path

            # 月度收益率热力图
            monthly_returns_path = self.backtest_visualizer.plot_monthly_returns(
                results, save_path=str(output_path / "monthly_returns")
            )
            if monthly_returns_path:
                report_files['monthly_returns'] = monthly_returns_path
            
            self.logger.info(f"回测报告已生成，包含 {len(report_files)} 个图表")
            return report_files
            
        except Exception as e:
            self.logger.error(f"创建回测报告失败: {e}")
            return {}
    
    def create_portfolio_report(self, portfolio_data: Dict[str, Any],
                              output_dir: str = "output/reports") -> Dict[str, str]:
        """创建投资组合报告"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            report_files = {}
            
            # 投资组合构成
            if 'positions' in portfolio_data:
                composition_path = self.portfolio_visualizer.plot_portfolio_composition(
                    portfolio_data['positions'],
                    save_path=str(output_path / "portfolio_composition")
                )
                if composition_path:
                    report_files['portfolio_composition'] = composition_path

            # 行业配置
            if 'sector_allocation' in portfolio_data:
                sector_path = self.portfolio_visualizer.plot_sector_allocation(
                    portfolio_data['sector_allocation'],
                    save_path=str(output_path / "sector_allocation")
                )
                if sector_path:
                    report_files['sector_allocation'] = sector_path
            
            self.logger.info(f"投资组合报告已生成，包含 {len(report_files)} 个图表")
            return report_files
            
        except Exception as e:
            self.logger.error(f"创建投资组合报告失败: {e}")
            return {}

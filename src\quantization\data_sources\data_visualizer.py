#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据统计和可视化模块
实现数据质量报告生成、统计分析、可视化展示功能
"""

import os
import sys
import warnings
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Union
import pandas as pd
import numpy as np
from dataclasses import dataclass
import logging
import json
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.font_manager import FontProperties
import seaborn as sns
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.quantization.data_sources.data_storage import DataStorage
from src.quantization.data_sources.data_validator import DataValidator

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

@dataclass
class VisualizationConfig:
    """可视化配置"""
    output_dir: str = "data/reports"
    figure_size: Tuple[int, int] = (12, 8)
    dpi: int = 300
    style: str = "seaborn-v0_8"
    color_palette: str = "husl"
    save_format: str = "png"

@dataclass
class StatisticsReport:
    """统计报告"""
    report_date: str
    total_stocks: int = 0
    total_trading_days: int = 0
    data_completeness: float = 0.0
    average_quality_score: float = 0.0
    quality_distribution: Dict[str, int] = None
    top_quality_stocks: List[Dict] = None
    low_quality_stocks: List[Dict] = None
    daily_statistics: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.quality_distribution is None:
            self.quality_distribution = {}
        if self.top_quality_stocks is None:
            self.top_quality_stocks = []
        if self.low_quality_stocks is None:
            self.low_quality_stocks = []
        if self.daily_statistics is None:
            self.daily_statistics = {}

class DataVisualizer:
    """数据统计和可视化器"""
    
    def __init__(self, storage: DataStorage = None, validator: DataValidator = None,
                 config: VisualizationConfig = None):
        """
        初始化数据可视化器
        
        Args:
            storage: 数据存储器
            validator: 数据验证器
            config: 可视化配置
        """
        self.storage = storage or DataStorage()
        self.validator = validator or DataValidator()
        self.config = config or VisualizationConfig()
        self.logger = logging.getLogger(__name__)
        
        # 确保输出目录存在
        os.makedirs(self.config.output_dir, exist_ok=True)
        
        # 设置matplotlib样式
        try:
            plt.style.use(self.config.style)
        except:
            plt.style.use('default')
        
        sns.set_palette(self.config.color_palette)
        
        self.logger.info("数据可视化器初始化完成")
    
    def generate_comprehensive_report(self, start_date: str, end_date: str,
                                    stock_codes: List[str] = None) -> StatisticsReport:
        """
        生成综合统计报告
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            stock_codes: 股票代码列表，None表示所有股票
            
        Returns:
            统计报告
        """
        self.logger.info(f"生成综合统计报告: {start_date} 到 {end_date}")
        
        report = StatisticsReport(
            report_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        
        try:
            # 1. 获取数据质量统计
            quality_stats = self.storage.get_data_quality_stats(start_date, end_date)
            
            if quality_stats:
                report.average_quality_score = quality_stats.get('average_quality_score', 0.0)
                report.quality_distribution = quality_stats.get('quality_distribution', {})
            
            # 2. 获取数据完整性统计
            if stock_codes:
                integrity_results = self.validator.validate_data_integrity(stock_codes, start_date, end_date)
                report.data_completeness = integrity_results.get('completeness_rate', 0.0)
                report.total_stocks = len(stock_codes)
            
            # 3. 计算交易日数量
            from src.quantization.data_sources.smart_data_downloader import SmartDataDownloader
            downloader = SmartDataDownloader()
            trading_days = downloader.get_trading_days_range(start_date, end_date)
            report.total_trading_days = len(trading_days)
            
            # 4. 生成日度统计
            report.daily_statistics = self._generate_daily_statistics(start_date, end_date)
            
            self.logger.info(f"综合统计报告生成完成")
            
        except Exception as e:
            self.logger.error(f"生成综合统计报告失败: {e}")
        
        return report
    
    def _generate_daily_statistics(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """生成日度统计"""
        try:
            # 这里应该实现具体的日度统计逻辑
            # 暂时返回模拟数据
            return {
                'daily_data_count': {},
                'daily_quality_scores': {},
                'daily_completeness': {}
            }
        except Exception as e:
            self.logger.error(f"生成日度统计失败: {e}")
            return {}
    
    def create_quality_distribution_chart(self, report: StatisticsReport, 
                                        output_path: str = None) -> str:
        """
        创建数据质量分布图
        
        Args:
            report: 统计报告
            output_path: 输出路径
            
        Returns:
            图表文件路径
        """
        if output_path is None:
            output_path = os.path.join(self.config.output_dir, "quality_distribution.png")
        
        try:
            fig, ax = plt.subplots(figsize=self.config.figure_size, dpi=self.config.dpi)
            
            if report.quality_distribution:
                labels = list(report.quality_distribution.keys())
                values = list(report.quality_distribution.values())
                colors = sns.color_palette(self.config.color_palette, len(labels))
                
                # 创建饼图
                wedges, texts, autotexts = ax.pie(values, labels=labels, colors=colors, 
                                                 autopct='%1.1f%%', startangle=90)
                
                # 设置标题
                ax.set_title('数据质量分布', fontsize=16, fontweight='bold', pad=20)
                
                # 添加图例
                ax.legend(wedges, [f'{label}: {value}' for label, value in zip(labels, values)],
                         title="质量等级", loc="center left", bbox_to_anchor=(1, 0, 0.5, 1))
            else:
                ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', fontsize=14)
                ax.set_title('数据质量分布', fontsize=16, fontweight='bold')
            
            plt.tight_layout()
            plt.savefig(output_path, format=self.config.save_format, 
                       bbox_inches='tight', dpi=self.config.dpi)
            plt.close()
            
            self.logger.info(f"数据质量分布图已生成: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"创建数据质量分布图失败: {e}")
            return ""
    
    def create_completeness_trend_chart(self, start_date: str, end_date: str,
                                      output_path: str = None) -> str:
        """
        创建数据完整性趋势图
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            output_path: 输出路径
            
        Returns:
            图表文件路径
        """
        if output_path is None:
            output_path = os.path.join(self.config.output_dir, "completeness_trend.png")
        
        try:
            fig, ax = plt.subplots(figsize=self.config.figure_size, dpi=self.config.dpi)
            
            # 生成模拟的完整性趋势数据
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            completeness_data = np.random.uniform(0.8, 1.0, len(date_range))
            
            # 绘制趋势线
            ax.plot(date_range, completeness_data, linewidth=2, marker='o', markersize=4)
            
            # 设置标题和标签
            ax.set_title('数据完整性趋势', fontsize=16, fontweight='bold', pad=20)
            ax.set_xlabel('日期', fontsize=12)
            ax.set_ylabel('完整性比率', fontsize=12)
            
            # 设置Y轴范围
            ax.set_ylim(0, 1.1)
            
            # 格式化日期轴
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))
            plt.xticks(rotation=45)
            
            # 添加网格
            ax.grid(True, alpha=0.3)
            
            # 添加平均线
            avg_completeness = np.mean(completeness_data)
            ax.axhline(y=avg_completeness, color='red', linestyle='--', alpha=0.7,
                      label=f'平均值: {avg_completeness:.2%}')
            ax.legend()
            
            plt.tight_layout()
            plt.savefig(output_path, format=self.config.save_format, 
                       bbox_inches='tight', dpi=self.config.dpi)
            plt.close()
            
            self.logger.info(f"数据完整性趋势图已生成: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"创建数据完整性趋势图失败: {e}")
            return ""
    
    def create_stock_quality_ranking(self, stock_codes: List[str], start_date: str, end_date: str,
                                   top_n: int = 20, output_path: str = None) -> str:
        """
        创建股票质量排名图
        
        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            top_n: 显示前N名
            output_path: 输出路径
            
        Returns:
            图表文件路径
        """
        if output_path is None:
            output_path = os.path.join(self.config.output_dir, "stock_quality_ranking.png")
        
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8), dpi=self.config.dpi)
            
            # 生成模拟的股票质量数据
            quality_scores = {code: np.random.uniform(0.6, 1.0) for code in stock_codes[:top_n]}
            
            # 排序
            sorted_stocks = sorted(quality_scores.items(), key=lambda x: x[1], reverse=True)
            
            # 最高质量股票
            top_stocks = sorted_stocks[:top_n//2]
            top_codes, top_scores = zip(*top_stocks) if top_stocks else ([], [])
            
            # 最低质量股票
            bottom_stocks = sorted_stocks[-top_n//2:]
            bottom_codes, bottom_scores = zip(*bottom_stocks) if bottom_stocks else ([], [])
            
            # 绘制最高质量股票
            if top_codes:
                bars1 = ax1.barh(range(len(top_codes)), top_scores, color='green', alpha=0.7)
                ax1.set_yticks(range(len(top_codes)))
                ax1.set_yticklabels(top_codes)
                ax1.set_xlabel('质量分数')
                ax1.set_title(f'质量最高的{len(top_codes)}只股票', fontweight='bold')
                ax1.set_xlim(0, 1)
                
                # 添加数值标签
                for i, (bar, score) in enumerate(zip(bars1, top_scores)):
                    ax1.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2, 
                            f'{score:.3f}', va='center', fontsize=10)
            
            # 绘制最低质量股票
            if bottom_codes:
                bars2 = ax2.barh(range(len(bottom_codes)), bottom_scores, color='red', alpha=0.7)
                ax2.set_yticks(range(len(bottom_codes)))
                ax2.set_yticklabels(bottom_codes)
                ax2.set_xlabel('质量分数')
                ax2.set_title(f'质量最低的{len(bottom_codes)}只股票', fontweight='bold')
                ax2.set_xlim(0, 1)
                
                # 添加数值标签
                for i, (bar, score) in enumerate(zip(bars2, bottom_scores)):
                    ax2.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2, 
                            f'{score:.3f}', va='center', fontsize=10)
            
            plt.tight_layout()
            plt.savefig(output_path, format=self.config.save_format, 
                       bbox_inches='tight', dpi=self.config.dpi)
            plt.close()
            
            self.logger.info(f"股票质量排名图已生成: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"创建股票质量排名图失败: {e}")
            return ""
    
    def create_data_volume_statistics(self, start_date: str, end_date: str,
                                    output_path: str = None) -> str:
        """
        创建数据量统计图
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            output_path: 输出路径
            
        Returns:
            图表文件路径
        """
        if output_path is None:
            output_path = os.path.join(self.config.output_dir, "data_volume_statistics.png")
        
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12), dpi=self.config.dpi)
            
            # 获取存储信息
            storage_info = self.storage.get_storage_info()
            
            # 1. 数据表记录数统计
            if 'tables_info' in storage_info:
                tables = list(storage_info['tables_info'].keys())
                counts = list(storage_info['tables_info'].values())
                
                bars1 = ax1.bar(tables, counts, color=sns.color_palette(self.config.color_palette, len(tables)))
                ax1.set_title('数据表记录数统计', fontweight='bold')
                ax1.set_ylabel('记录数')
                ax1.tick_params(axis='x', rotation=45)
                
                # 添加数值标签
                for bar, count in zip(bars1, counts):
                    ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(counts)*0.01,
                            f'{count:,}', ha='center', va='bottom')
            
            # 2. 数据库大小
            db_size = storage_info.get('database_size_mb', 0)
            ax2.pie([db_size, 100-db_size], labels=['已使用', '剩余空间'], autopct='%1.1f%%',
                   colors=['lightblue', 'lightgray'])
            ax2.set_title(f'数据库大小: {db_size:.2f} MB', fontweight='bold')
            
            # 3. 查询统计
            query_stats = storage_info.get('query_stats')
            if query_stats:
                stats_labels = ['查询总数', '缓存命中', '缓存未命中']
                stats_values = [query_stats.query_count, query_stats.cache_hits, query_stats.cache_misses]
                
                bars3 = ax3.bar(stats_labels, stats_values, color=['blue', 'green', 'orange'])
                ax3.set_title('查询统计', fontweight='bold')
                ax3.set_ylabel('次数')
                
                # 添加数值标签
                for bar, value in zip(bars3, stats_values):
                    ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(stats_values)*0.01,
                            f'{value}', ha='center', va='bottom')
            
            # 4. 缓存命中率
            if query_stats and query_stats.query_count > 0:
                hit_rate = query_stats.cache_hit_rate * 100
                ax4.pie([hit_rate, 100-hit_rate], labels=['命中', '未命中'], autopct='%1.1f%%',
                       colors=['lightgreen', 'lightcoral'])
                ax4.set_title(f'缓存命中率: {hit_rate:.1f}%', fontweight='bold')
            
            plt.tight_layout()
            plt.savefig(output_path, format=self.config.save_format, 
                       bbox_inches='tight', dpi=self.config.dpi)
            plt.close()
            
            self.logger.info(f"数据量统计图已生成: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"创建数据量统计图失败: {e}")
            return ""

    def generate_html_report(self, report: StatisticsReport, stock_codes: List[str],
                           start_date: str, end_date: str, output_path: Optional[str] = None) -> str:
        """
        生成HTML格式的综合报告

        Args:
            report: 统计报告
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            output_path: 输出路径

        Returns:
            HTML报告文件路径
        """
        if output_path is None:
            output_path = os.path.join(self.config.output_dir, "comprehensive_report.html")

        try:
            # 生成图表
            chart_paths = {}
            chart_paths['quality_distribution'] = self.create_quality_distribution_chart(report)
            chart_paths['completeness_trend'] = self.create_completeness_trend_chart(start_date, end_date)
            chart_paths['stock_ranking'] = self.create_stock_quality_ranking(stock_codes, start_date, end_date)
            chart_paths['data_volume'] = self.create_data_volume_statistics(start_date, end_date)

            # 生成HTML内容
            html_content = self._generate_html_content(report, chart_paths, start_date, end_date)

            # 保存HTML文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            self.logger.info(f"HTML报告已生成: {output_path}")
            return output_path

        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {e}")
            return ""

    def _generate_html_content(self, report: StatisticsReport, chart_paths: Dict[str, str],
                             start_date: str, end_date: str) -> str:
        """生成HTML内容"""
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创业板动态因子策略数据质量报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007acc;
            padding-bottom: 20px;
        }}
        .header h1 {{
            color: #007acc;
            margin: 0;
            font-size: 28px;
        }}
        .header p {{
            color: #666;
            margin: 10px 0 0 0;
            font-size: 16px;
        }}
        .summary {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .summary-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }}
        .summary-card h3 {{
            margin: 0 0 10px 0;
            font-size: 14px;
            opacity: 0.9;
        }}
        .summary-card .value {{
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }}
        .section {{
            margin-bottom: 40px;
        }}
        .section h2 {{
            color: #333;
            border-left: 4px solid #007acc;
            padding-left: 15px;
            margin-bottom: 20px;
        }}
        .chart-container {{
            text-align: center;
            margin: 20px 0;
        }}
        .chart-container img {{
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
        }}
        .table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        .table th, .table td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        .table th {{
            background-color: #f8f9fa;
            font-weight: bold;
        }}
        .table tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        .footer {{
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 14px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>创业板动态因子策略数据质量报告</h1>
            <p>报告生成时间: {report.report_date}</p>
            <p>数据时间范围: {start_date} 至 {end_date}</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>总股票数量</h3>
                <p class="value">{report.total_stocks}</p>
            </div>
            <div class="summary-card">
                <h3>交易日数量</h3>
                <p class="value">{report.total_trading_days}</p>
            </div>
            <div class="summary-card">
                <h3>数据完整性</h3>
                <p class="value">{report.data_completeness:.1%}</p>
            </div>
            <div class="summary-card">
                <h3>平均质量分数</h3>
                <p class="value">{report.average_quality_score:.3f}</p>
            </div>
        </div>

        <div class="section">
            <h2>数据质量分布</h2>
            <div class="chart-container">
                <img src="{os.path.basename(chart_paths.get('quality_distribution', ''))}" alt="数据质量分布图">
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>质量等级</th>
                        <th>数量</th>
                        <th>占比</th>
                    </tr>
                </thead>
                <tbody>
                    {self._generate_quality_table_rows(report.quality_distribution)}
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>数据完整性趋势</h2>
            <div class="chart-container">
                <img src="{os.path.basename(chart_paths.get('completeness_trend', ''))}" alt="数据完整性趋势图">
            </div>
        </div>

        <div class="section">
            <h2>股票质量排名</h2>
            <div class="chart-container">
                <img src="{os.path.basename(chart_paths.get('stock_ranking', ''))}" alt="股票质量排名图">
            </div>
        </div>

        <div class="section">
            <h2>数据量统计</h2>
            <div class="chart-container">
                <img src="{os.path.basename(chart_paths.get('data_volume', ''))}" alt="数据量统计图">
            </div>
        </div>

        <div class="footer">
            <p>本报告由创业板动态因子策略TICK回测系统自动生成</p>
            <p>© 2024 量化交易系统</p>
        </div>
    </div>
</body>
</html>
        """

        return html_template

    def _generate_quality_table_rows(self, quality_distribution: Dict[str, int]) -> str:
        """生成质量分布表格行"""
        if not quality_distribution:
            return "<tr><td colspan='3'>暂无数据</td></tr>"

        total = sum(quality_distribution.values())
        rows = []

        quality_labels = {
            'excellent': '优秀 (≥90%)',
            'good': '良好 (80-90%)',
            'fair': '一般 (70-80%)',
            'poor': '较差 (<70%)'
        }

        for level, count in quality_distribution.items():
            label = quality_labels.get(level, level)
            percentage = (count / total * 100) if total > 0 else 0
            rows.append(f"<tr><td>{label}</td><td>{count}</td><td>{percentage:.1f}%</td></tr>")

        return "".join(rows)

    def export_data_summary(self, start_date: str, end_date: str,
                          output_path: Optional[str] = None) -> str:
        """
        导出数据摘要到Excel

        Args:
            start_date: 开始日期
            end_date: 结束日期
            output_path: 输出路径

        Returns:
            Excel文件路径
        """
        if output_path is None:
            output_path = os.path.join(self.config.output_dir, "data_summary.xlsx")

        try:
            # 获取存储信息
            storage_info = self.storage.get_storage_info()

            # 创建Excel写入器
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 1. 数据表统计
                if 'tables_info' in storage_info:
                    tables_df = pd.DataFrame(list(storage_info['tables_info'].items()),
                                           columns=['表名', '记录数'])
                    tables_df.to_excel(writer, sheet_name='数据表统计', index=False)

                # 2. 查询统计
                query_stats = storage_info.get('query_stats')
                if query_stats:
                    query_df = pd.DataFrame([{
                        '查询总数': query_stats.query_count,
                        '缓存命中': query_stats.cache_hits,
                        '缓存未命中': query_stats.cache_misses,
                        '缓存命中率': f"{query_stats.cache_hit_rate:.2%}",
                        '平均查询时间': f"{query_stats.avg_time:.3f}秒"
                    }])
                    query_df.to_excel(writer, sheet_name='查询统计', index=False)

                # 3. 数据质量统计
                quality_stats = self.storage.get_data_quality_stats(start_date, end_date)
                if quality_stats:
                    quality_df = pd.DataFrame([{
                        '总记录数': quality_stats.get('total_records', 0),
                        '平均质量分数': quality_stats.get('average_quality_score', 0),
                        '平均完整性': quality_stats.get('average_completeness', 0),
                        '平均准确性': quality_stats.get('average_accuracy', 0),
                        '平均一致性': quality_stats.get('average_consistency', 0),
                        '最低质量分数': quality_stats.get('min_quality_score', 0),
                        '最高质量分数': quality_stats.get('max_quality_score', 0)
                    }])
                    quality_df.to_excel(writer, sheet_name='数据质量统计', index=False)

            self.logger.info(f"数据摘要已导出: {output_path}")
            return output_path

        except Exception as e:
            self.logger.error(f"导出数据摘要失败: {e}")
            return ""

    def create_dashboard_summary(self, report: StatisticsReport, stock_codes: List[str],
                               start_date: str, end_date: str) -> Dict[str, Any]:
        """
        创建仪表板摘要

        Args:
            report: 统计报告
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            仪表板数据
        """
        try:
            # 生成所有图表
            chart_paths = {}
            chart_paths['quality_distribution'] = self.create_quality_distribution_chart(report)
            chart_paths['completeness_trend'] = self.create_completeness_trend_chart(start_date, end_date)
            chart_paths['stock_ranking'] = self.create_stock_quality_ranking(stock_codes, start_date, end_date)
            chart_paths['data_volume'] = self.create_data_volume_statistics(start_date, end_date)

            # 生成HTML报告
            html_report = self.generate_html_report(report, stock_codes, start_date, end_date)

            # 导出Excel摘要
            excel_summary = self.export_data_summary(start_date, end_date)

            dashboard_data = {
                'report': report,
                'chart_paths': chart_paths,
                'html_report': html_report,
                'excel_summary': excel_summary,
                'generation_time': datetime.now().isoformat(),
                'summary_stats': {
                    'total_stocks': report.total_stocks,
                    'total_trading_days': report.total_trading_days,
                    'data_completeness': report.data_completeness,
                    'average_quality_score': report.average_quality_score,
                    'quality_distribution': report.quality_distribution
                }
            }

            # 保存仪表板数据
            dashboard_path = os.path.join(self.config.output_dir, "dashboard_data.json")
            with open(dashboard_path, 'w', encoding='utf-8') as f:
                # 转换不可序列化的对象
                serializable_data = {
                    'chart_paths': dashboard_data['chart_paths'],
                    'html_report': dashboard_data['html_report'],
                    'excel_summary': dashboard_data['excel_summary'],
                    'generation_time': dashboard_data['generation_time'],
                    'summary_stats': dashboard_data['summary_stats']
                }
                json.dump(serializable_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"仪表板摘要已创建: {dashboard_path}")
            return dashboard_data

        except Exception as e:
            self.logger.error(f"创建仪表板摘要失败: {e}")
            return {}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web仪表板

提供基于Web的用户界面，包含策略管理、回测执行、结果展示等功能。
使用Streamlit或Dash构建交互式界面。
"""

import time
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta, date
from pathlib import Path
import pandas as pd
import numpy as np

from quantization.utils.logger import get_logger
from quantization.config.config_manager import config_manager

# 可选依赖
try:
    import streamlit as st
    import streamlit.components.v1 as components
    STREAMLIT_AVAILABLE = True
except ImportError:
    STREAMLIT_AVAILABLE = False

try:
    import plotly.graph_objects as go
    import plotly.express as px
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False


class QuantizationDashboard:
    """
    量化交易仪表板
    
    提供Web界面进行策略管理、回测执行、结果分析等操作。
    """
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        
        if not STREAMLIT_AVAILABLE:
            raise ImportError("Streamlit未安装，请运行: pip install streamlit")
        
        # 初始化会话状态
        self._init_session_state()
        
        # 组件引用（需要注入）
        self.strategy_manager = None
        self.backtest_engine = None
        self.data_manager = None
        self.visualizer = None
    
    def _init_session_state(self):
        """初始化会话状态"""
        if 'strategies' not in st.session_state:
            st.session_state.strategies = {}
        
        if 'backtest_results' not in st.session_state:
            st.session_state.backtest_results = {}
        
        if 'current_strategy' not in st.session_state:
            st.session_state.current_strategy = None
        
        if 'backtest_running' not in st.session_state:
            st.session_state.backtest_running = False
    
    def run(self):
        """运行仪表板"""
        st.set_page_config(
            page_title="量化交易系统",
            page_icon="📈",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # 侧边栏导航
        self._render_sidebar()
        
        # 主内容区域
        self._render_main_content()
    
    def _render_sidebar(self):
        """渲染侧边栏"""
        st.sidebar.title("📈 量化交易系统")
        st.sidebar.markdown("---")
        
        # 导航菜单
        page = st.sidebar.selectbox(
            "选择页面",
            ["概览", "策略管理", "回测执行", "结果分析", "数据查询", "系统配置"]
        )
        
        st.session_state.current_page = page
        
        # 系统状态
        st.sidebar.markdown("### 系统状态")
        
        # 模拟系统状态
        status_data = {
            "数据源": "🟢 正常",
            "数据库": "🟢 正常", 
            "API服务": "🟢 正常",
            "缓存": "🟢 正常"
        }
        
        for component, status in status_data.items():
            st.sidebar.text(f"{component}: {status}")
        
        # 快速统计
        st.sidebar.markdown("### 快速统计")
        col1, col2 = st.sidebar.columns(2)
        
        with col1:
            st.metric("策略数量", len(st.session_state.strategies))
        
        with col2:
            st.metric("回测任务", len(st.session_state.backtest_results))
    
    def _render_main_content(self):
        """渲染主内容"""
        page = st.session_state.get('current_page', '概览')
        
        if page == "概览":
            self._render_overview_page()
        elif page == "策略管理":
            self._render_strategy_management_page()
        elif page == "回测执行":
            self._render_backtest_page()
        elif page == "结果分析":
            self._render_analysis_page()
        elif page == "数据查询":
            self._render_data_query_page()
        elif page == "系统配置":
            self._render_config_page()
    
    def _render_overview_page(self):
        """渲染概览页面"""
        st.title("📊 系统概览")
        
        # 关键指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                label="总策略数",
                value=len(st.session_state.strategies),
                delta=1 if len(st.session_state.strategies) > 0 else 0
            )
        
        with col2:
            st.metric(
                label="活跃策略",
                value=sum(1 for s in st.session_state.strategies.values() if s.get('enabled', False)),
                delta=0
            )
        
        with col3:
            st.metric(
                label="回测任务",
                value=len(st.session_state.backtest_results),
                delta=0
            )
        
        with col4:
            # 计算平均收益率
            avg_return = 0.0
            if st.session_state.backtest_results:
                returns = [r.get('performance_metrics', {}).get('total_return', 0) 
                          for r in st.session_state.backtest_results.values()]
                avg_return = np.mean(returns) if returns else 0.0
            
            st.metric(
                label="平均收益率",
                value=f"{avg_return:.2%}",
                delta=f"{avg_return:.2%}"
            )
        
        st.markdown("---")
        
        # 最近活动
        st.subheader("📈 最近活动")
        
        if st.session_state.backtest_results:
            # 显示最近的回测结果
            recent_results = list(st.session_state.backtest_results.items())[-5:]
            
            for task_id, result in recent_results:
                with st.expander(f"回测任务: {task_id[:8]}..."):
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.write(f"**策略**: {result.get('strategy_name', 'Unknown')}")
                        st.write(f"**开始日期**: {result.get('start_date', 'N/A')}")
                    
                    with col2:
                        metrics = result.get('performance_metrics', {})
                        st.write(f"**总收益率**: {metrics.get('total_return', 0):.2%}")
                        st.write(f"**夏普比率**: {metrics.get('sharpe_ratio', 0):.2f}")
                    
                    with col3:
                        st.write(f"**最大回撤**: {metrics.get('max_drawdown', 0):.2%}")
                        st.write(f"**胜率**: {metrics.get('win_rate', 0):.2%}")
        else:
            st.info("暂无回测记录")
        
        # 系统资源使用情况
        st.subheader("💻 系统资源")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 模拟CPU使用率
            cpu_usage = np.random.uniform(20, 80)
            st.metric("CPU使用率", f"{cpu_usage:.1f}%")
            
            # 进度条显示
            st.progress(cpu_usage / 100)
        
        with col2:
            # 模拟内存使用率
            memory_usage = np.random.uniform(30, 70)
            st.metric("内存使用率", f"{memory_usage:.1f}%")
            
            # 进度条显示
            st.progress(memory_usage / 100)
    
    def _render_strategy_management_page(self):
        """渲染策略管理页面"""
        st.title("🎯 策略管理")
        
        # 策略列表
        st.subheader("策略列表")
        
        if st.session_state.strategies:
            # 创建策略表格
            strategy_data = []
            for name, strategy in st.session_state.strategies.items():
                strategy_data.append({
                    "策略名称": name,
                    "描述": strategy.get('description', ''),
                    "状态": "启用" if strategy.get('enabled', False) else "禁用",
                    "创建时间": strategy.get('created_at', ''),
                    "参数数量": len(strategy.get('parameters', {}))
                })
            
            df = pd.DataFrame(strategy_data)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("暂无策略")
        
        st.markdown("---")
        
        # 创建新策略
        st.subheader("创建新策略")
        
        with st.form("create_strategy_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                strategy_name = st.text_input("策略名称", placeholder="输入策略名称")
                strategy_description = st.text_area("策略描述", placeholder="输入策略描述")
            
            with col2:
                strategy_enabled = st.checkbox("启用策略", value=True)
                
                # 策略参数
                st.write("**策略参数**")
                param_count = st.number_input("参数数量", min_value=0, max_value=10, value=2)
                
                parameters = {}
                for i in range(param_count):
                    param_name = st.text_input(f"参数{i+1}名称", key=f"param_name_{i}")
                    param_value = st.text_input(f"参数{i+1}值", key=f"param_value_{i}")
                    
                    if param_name and param_value:
                        try:
                            # 尝试转换为数值
                            parameters[param_name] = float(param_value)
                        except ValueError:
                            parameters[param_name] = param_value
            
            submitted = st.form_submit_button("创建策略")
            
            if submitted and strategy_name:
                # 添加策略到会话状态
                st.session_state.strategies[strategy_name] = {
                    'description': strategy_description,
                    'enabled': strategy_enabled,
                    'parameters': parameters,
                    'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                st.success(f"策略 '{strategy_name}' 创建成功！")
                st.experimental_rerun()
    
    def _render_backtest_page(self):
        """渲染回测页面"""
        st.title("🔄 回测执行")
        
        if not st.session_state.strategies:
            st.warning("请先创建策略")
            return
        
        # 回测配置
        st.subheader("回测配置")
        
        with st.form("backtest_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                # 策略选择
                strategy_name = st.selectbox(
                    "选择策略",
                    list(st.session_state.strategies.keys())
                )
                
                # 日期范围
                start_date = st.date_input(
                    "开始日期",
                    value=date.today() - timedelta(days=365)
                )
                
                end_date = st.date_input(
                    "结束日期",
                    value=date.today()
                )
            
            with col2:
                # 初始资金
                initial_capital = st.number_input(
                    "初始资金",
                    min_value=10000.0,
                    value=1000000.0,
                    step=10000.0,
                    format="%.0f"
                )
                
                # 基准选择
                benchmark = st.selectbox(
                    "基准指数",
                    ["沪深300", "中证500", "创业板指", "无基准"],
                    index=0
                )
            
            # 高级设置
            with st.expander("高级设置"):
                commission_rate = st.number_input(
                    "手续费率",
                    min_value=0.0,
                    max_value=0.01,
                    value=0.0003,
                    step=0.0001,
                    format="%.4f"
                )
                
                slippage_rate = st.number_input(
                    "滑点率",
                    min_value=0.0,
                    max_value=0.01,
                    value=0.001,
                    step=0.0001,
                    format="%.4f"
                )
            
            submitted = st.form_submit_button(
                "开始回测",
                disabled=st.session_state.backtest_running
            )
            
            if submitted:
                # 执行回测
                self._run_backtest(
                    strategy_name=strategy_name,
                    start_date=start_date.strftime('%Y-%m-%d'),
                    end_date=end_date.strftime('%Y-%m-%d'),
                    initial_capital=initial_capital,
                    benchmark=benchmark if benchmark != "无基准" else None,
                    commission_rate=commission_rate,
                    slippage_rate=slippage_rate
                )
        
        # 回测历史
        st.subheader("回测历史")
        
        if st.session_state.backtest_results:
            # 显示回测结果表格
            results_data = []
            for task_id, result in st.session_state.backtest_results.items():
                metrics = result.get('performance_metrics', {})
                results_data.append({
                    "任务ID": task_id[:8] + "...",
                    "策略": result.get('strategy_name', ''),
                    "开始日期": result.get('start_date', ''),
                    "结束日期": result.get('end_date', ''),
                    "总收益率": f"{metrics.get('total_return', 0):.2%}",
                    "夏普比率": f"{metrics.get('sharpe_ratio', 0):.2f}",
                    "最大回撤": f"{metrics.get('max_drawdown', 0):.2%}",
                    "状态": result.get('status', 'completed')
                })
            
            df = pd.DataFrame(results_data)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("暂无回测记录")
    
    def _run_backtest(self, **kwargs):
        """执行回测"""
        st.session_state.backtest_running = True
        
        # 显示进度
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        try:
            # 模拟回测过程
            for i in range(100):
                time.sleep(0.01)  # 模拟计算时间
                progress_bar.progress(i + 1)
                status_text.text(f'回测进行中... {i+1}%')
            
            # 生成模拟结果
            task_id = f"task_{int(time.time())}"
            
            # 模拟回测结果
            np.random.seed(42)
            n_days = 252
            daily_returns = np.random.normal(0.0005, 0.02, n_days)
            
            cumulative_returns = np.cumprod(1 + daily_returns)
            total_return = cumulative_returns[-1] - 1
            
            # 计算性能指标
            annualized_return = np.mean(daily_returns) * 252
            volatility = np.std(daily_returns) * np.sqrt(252)
            sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
            
            # 计算最大回撤
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdown)
            
            result = {
                'strategy_name': kwargs['strategy_name'],
                'start_date': kwargs['start_date'],
                'end_date': kwargs['end_date'],
                'initial_capital': kwargs['initial_capital'],
                'performance_metrics': {
                    'total_return': total_return,
                    'annualized_return': annualized_return,
                    'volatility': volatility,
                    'sharpe_ratio': sharpe_ratio,
                    'max_drawdown': max_drawdown,
                    'win_rate': np.mean(daily_returns > 0)
                },
                'portfolio_history': [
                    {
                        'date': (datetime.now() - timedelta(days=n_days-i)).strftime('%Y-%m-%d'),
                        'daily_return': daily_returns[i],
                        'cumulative_return': cumulative_returns[i] - 1,
                        'portfolio_value': kwargs['initial_capital'] * cumulative_returns[i]
                    }
                    for i in range(n_days)
                ],
                'status': 'completed',
                'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 保存结果
            st.session_state.backtest_results[task_id] = result
            
            status_text.text('回测完成！')
            st.success(f"回测完成！任务ID: {task_id}")
            
        except Exception as e:
            st.error(f"回测失败: {str(e)}")
        
        finally:
            st.session_state.backtest_running = False
            time.sleep(1)
            st.experimental_rerun()
    
    def _render_analysis_page(self):
        """渲染结果分析页面"""
        st.title("📈 结果分析")
        
        if not st.session_state.backtest_results:
            st.warning("暂无回测结果")
            return
        
        # 选择回测结果
        task_ids = list(st.session_state.backtest_results.keys())
        selected_task = st.selectbox(
            "选择回测任务",
            task_ids,
            format_func=lambda x: f"{x[:8]}... - {st.session_state.backtest_results[x]['strategy_name']}"
        )
        
        if selected_task:
            result = st.session_state.backtest_results[selected_task]
            
            # 性能指标
            st.subheader("📊 性能指标")
            
            metrics = result['performance_metrics']
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("总收益率", f"{metrics['total_return']:.2%}")
            
            with col2:
                st.metric("年化收益率", f"{metrics['annualized_return']:.2%}")
            
            with col3:
                st.metric("夏普比率", f"{metrics['sharpe_ratio']:.2f}")
            
            with col4:
                st.metric("最大回撤", f"{metrics['max_drawdown']:.2%}")
            
            # 净值曲线
            st.subheader("📈 净值曲线")
            
            if PLOTLY_AVAILABLE:
                portfolio_data = pd.DataFrame(result['portfolio_history'])
                portfolio_data['date'] = pd.to_datetime(portfolio_data['date'])
                
                fig = go.Figure()
                
                fig.add_trace(go.Scatter(
                    x=portfolio_data['date'],
                    y=portfolio_data['portfolio_value'],
                    mode='lines',
                    name='策略净值',
                    line=dict(color='blue', width=2)
                ))
                
                fig.update_layout(
                    title='策略净值曲线',
                    xaxis_title='日期',
                    yaxis_title='净值',
                    height=400
                )
                
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("需要安装plotly来显示图表")
            
            # 收益率分布
            st.subheader("📊 收益率分布")
            
            if PLOTLY_AVAILABLE:
                returns = [d['daily_return'] for d in result['portfolio_history']]
                
                fig = go.Figure(data=[go.Histogram(x=returns, nbinsx=50)])
                fig.update_layout(
                    title='日收益率分布',
                    xaxis_title='日收益率',
                    yaxis_title='频次',
                    height=400
                )
                
                st.plotly_chart(fig, use_container_width=True)
    
    def _render_data_query_page(self):
        """渲染数据查询页面"""
        st.title("🔍 数据查询")
        
        # 查询表单
        with st.form("data_query_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                symbol = st.text_input("股票代码", placeholder="例如: 000001.SZ")
                start_date = st.date_input("开始日期", value=date.today() - timedelta(days=30))
            
            with col2:
                data_type = st.selectbox("数据类型", ["日线数据", "分钟数据", "基本面数据"])
                end_date = st.date_input("结束日期", value=date.today())
            
            submitted = st.form_submit_button("查询数据")
            
            if submitted and symbol:
                # 模拟数据查询
                with st.spinner("查询中..."):
                    time.sleep(1)  # 模拟查询时间
                    
                    # 生成模拟数据
                    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
                    n_days = len(date_range)
                    
                    if n_days > 0:
                        np.random.seed(42)
                        base_price = 10.0
                        
                        data = []
                        for i, dt in enumerate(date_range):
                            price_change = np.random.normal(0, 0.02)
                            base_price *= (1 + price_change)
                            
                            data.append({
                                '日期': dt.strftime('%Y-%m-%d'),
                                '开盘价': base_price * (1 + np.random.uniform(-0.01, 0.01)),
                                '最高价': base_price * (1 + np.random.uniform(0, 0.02)),
                                '最低价': base_price * (1 + np.random.uniform(-0.02, 0)),
                                '收盘价': base_price,
                                '成交量': np.random.randint(1000000, 10000000),
                                '成交额': base_price * np.random.randint(1000000, 10000000)
                            })
                        
                        df = pd.DataFrame(data)
                        
                        st.subheader(f"📊 {symbol} 数据")
                        st.dataframe(df, use_container_width=True)
                        
                        # 价格走势图
                        if PLOTLY_AVAILABLE:
                            fig = go.Figure()
                            
                            fig.add_trace(go.Scatter(
                                x=df['日期'],
                                y=df['收盘价'],
                                mode='lines',
                                name='收盘价',
                                line=dict(color='blue', width=2)
                            ))
                            
                            fig.update_layout(
                                title=f'{symbol} 价格走势',
                                xaxis_title='日期',
                                yaxis_title='价格',
                                height=400
                            )
                            
                            st.plotly_chart(fig, use_container_width=True)
                    else:
                        st.warning("日期范围无效")
    
    def _render_config_page(self):
        """渲染系统配置页面"""
        st.title("⚙️ 系统配置")
        
        # 配置分类
        config_tabs = st.tabs(["数据源配置", "回测配置", "API配置", "系统配置"])
        
        with config_tabs[0]:
            st.subheader("数据源配置")
            
            akshare_enabled = st.checkbox(
                "启用akshare数据源",
                value=config_manager.get("data.akshare.enabled", True)
            )
            
            akshare_timeout = st.number_input(
                "akshare超时时间(秒)",
                min_value=1,
                max_value=300,
                value=config_manager.get("data.akshare.timeout", 30)
            )
            
            cache_enabled = st.checkbox(
                "启用数据缓存",
                value=config_manager.get("data.cache.enabled", True)
            )
            
            if st.button("保存数据源配置"):
                config_manager.set("data.akshare.enabled", akshare_enabled)
                config_manager.set("data.akshare.timeout", akshare_timeout)
                config_manager.set("data.cache.enabled", cache_enabled)
                st.success("数据源配置已保存")
        
        with config_tabs[1]:
            st.subheader("回测配置")
            
            initial_capital = st.number_input(
                "默认初始资金",
                min_value=1000.0,
                value=config_manager.get("backtest.initial_capital", 1000000.0)
            )
            
            commission_rate = st.number_input(
                "默认手续费率",
                min_value=0.0,
                max_value=0.01,
                value=config_manager.get("backtest.commission_rate", 0.0003),
                format="%.4f"
            )
            
            if st.button("保存回测配置"):
                config_manager.set("backtest.initial_capital", initial_capital)
                config_manager.set("backtest.commission_rate", commission_rate)
                st.success("回测配置已保存")
        
        with config_tabs[2]:
            st.subheader("API配置")
            
            api_host = st.text_input(
                "API主机",
                value=config_manager.get("api.host", "0.0.0.0")
            )
            
            api_port = st.number_input(
                "API端口",
                min_value=1024,
                max_value=65535,
                value=config_manager.get("api.port", 8000)
            )
            
            if st.button("保存API配置"):
                config_manager.set("api.host", api_host)
                config_manager.set("api.port", api_port)
                st.success("API配置已保存")
        
        with config_tabs[3]:
            st.subheader("系统配置")
            
            log_level = st.selectbox(
                "日志级别",
                ["DEBUG", "INFO", "WARNING", "ERROR"],
                index=["DEBUG", "INFO", "WARNING", "ERROR"].index(
                    config_manager.get("system.log_level", "INFO")
                )
            )
            
            max_workers = st.number_input(
                "最大工作线程数",
                min_value=1,
                max_value=32,
                value=config_manager.get("system.max_workers", 8)
            )
            
            if st.button("保存系统配置"):
                config_manager.set("system.log_level", log_level)
                config_manager.set("system.max_workers", max_workers)
                st.success("系统配置已保存")
    
    def set_strategy_manager(self, strategy_manager):
        """设置策略管理器"""
        self.strategy_manager = strategy_manager
    
    def set_backtest_engine(self, backtest_engine):
        """设置回测引擎"""
        self.backtest_engine = backtest_engine
    
    def set_data_manager(self, data_manager):
        """设置数据管理器"""
        self.data_manager = data_manager
    
    def set_visualizer(self, visualizer):
        """设置可视化器"""
        self.visualizer = visualizer


def main():
    """主函数"""
    dashboard = QuantizationDashboard()
    dashboard.run()


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据缓存管理系统

实现高效的数据缓存机制，包括内存缓存、磁盘缓存、缓存策略管理和自动清理功能。
支持多级缓存、数据压缩和智能预加载。
"""

import asyncio
import time
import hashlib
import pickle
import gzip
import threading
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import json
import sqlite3
from collections import OrderedDict, defaultdict
import weakref
import gc

import pandas as pd
import numpy as np

from quantization.utils.logger import get_logger


class CacheLevel(Enum):
    """缓存级别"""
    MEMORY = "memory"      # 内存缓存
    DISK = "disk"          # 磁盘缓存
    DATABASE = "database"  # 数据库缓存


class CacheStrategy(Enum):
    """缓存策略"""
    LRU = "lru"           # 最近最少使用
    LFU = "lfu"           # 最少使用频率
    FIFO = "fifo"         # 先进先出
    TTL = "ttl"           # 生存时间
    ADAPTIVE = "adaptive"  # 自适应策略


class CompressionType(Enum):
    """压缩类型"""
    NONE = "none"
    GZIP = "gzip"
    PICKLE = "pickle"
    PARQUET = "parquet"


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    data: Any
    size: int
    created_time: float
    last_accessed: float
    access_count: int = 0
    ttl: Optional[float] = None
    compression: CompressionType = CompressionType.NONE
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_time > self.ttl
    
    def update_access(self):
        """更新访问信息"""
        self.last_accessed = time.time()
        self.access_count += 1


@dataclass
class CacheStats:
    """缓存统计信息"""
    total_entries: int = 0
    total_size: int = 0
    hit_count: int = 0
    miss_count: int = 0
    eviction_count: int = 0
    compression_ratio: float = 0.0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hit_count + self.miss_count
        return self.hit_count / max(1, total)
    
    @property
    def average_size(self) -> float:
        """平均条目大小"""
        return self.total_size / max(1, self.total_entries)


class MemoryCache:
    """
    内存缓存管理器
    
    实现高效的内存缓存，支持多种淘汰策略。
    """
    
    def __init__(self, max_size: int = 1024 * 1024 * 1024,  # 1GB
                 max_entries: int = 10000,
                 strategy: CacheStrategy = CacheStrategy.LRU):
        self.max_size = max_size
        self.max_entries = max_entries
        self.strategy = strategy
        self.logger = get_logger(self.__class__.__name__)
        
        # 缓存存储
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._access_frequency: Dict[str, int] = defaultdict(int)
        self._lock = threading.RLock()
        
        # 统计信息
        self.stats = CacheStats()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        with self._lock:
            if key not in self._cache:
                self.stats.miss_count += 1
                return None
            
            entry = self._cache[key]
            
            # 检查是否过期
            if entry.is_expired():
                del self._cache[key]
                self.stats.miss_count += 1
                self.stats.eviction_count += 1
                return None
            
            # 更新访问信息
            entry.update_access()
            self._access_frequency[key] += 1
            
            # 根据策略调整位置
            if self.strategy == CacheStrategy.LRU:
                # 移动到末尾（最近使用）
                self._cache.move_to_end(key)
            
            self.stats.hit_count += 1
            return entry.data
    
    def put(self, key: str, data: Any, ttl: Optional[float] = None,
            compression: CompressionType = CompressionType.NONE,
            metadata: Optional[Dict[str, Any]] = None) -> bool:
        """存储缓存数据"""
        with self._lock:
            try:
                # 计算数据大小
                data_size = self._calculate_size(data)
                
                # 检查是否需要压缩
                if compression != CompressionType.NONE:
                    data, data_size = self._compress_data(data, compression)
                
                # 创建缓存条目
                entry = CacheEntry(
                    key=key,
                    data=data,
                    size=data_size,
                    created_time=time.time(),
                    last_accessed=time.time(),
                    ttl=ttl,
                    compression=compression,
                    metadata=metadata or {}
                )
                
                # 检查是否需要淘汰
                self._evict_if_needed(data_size)
                
                # 存储条目
                if key in self._cache:
                    # 更新现有条目
                    old_entry = self._cache[key]
                    self.stats.total_size -= old_entry.size
                
                self._cache[key] = entry
                self.stats.total_size += data_size
                self.stats.total_entries = len(self._cache)
                
                return True
                
            except Exception as e:
                self.logger.error(f"存储缓存失败: {e}")
                return False
    
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        with self._lock:
            if key in self._cache:
                entry = self._cache.pop(key)
                self.stats.total_size -= entry.size
                self.stats.total_entries = len(self._cache)
                if key in self._access_frequency:
                    del self._access_frequency[key]
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._access_frequency.clear()
            self.stats.total_size = 0
            self.stats.total_entries = 0
    
    def _evict_if_needed(self, new_size: int):
        """根据策略淘汰缓存条目"""
        # 检查条目数量限制
        while len(self._cache) >= self.max_entries:
            self._evict_one()
        
        # 检查大小限制
        while self.stats.total_size + new_size > self.max_size and self._cache:
            self._evict_one()
    
    def _evict_one(self):
        """淘汰一个缓存条目"""
        if not self._cache:
            return
        
        if self.strategy == CacheStrategy.LRU:
            # 淘汰最久未使用的
            key, entry = self._cache.popitem(last=False)
        elif self.strategy == CacheStrategy.LFU:
            # 淘汰使用频率最低的
            min_freq_key = min(self._access_frequency.keys(),
                             key=lambda k: self._access_frequency[k])
            entry = self._cache.pop(min_freq_key)
            del self._access_frequency[min_freq_key]
        elif self.strategy == CacheStrategy.FIFO:
            # 淘汰最早进入的
            key, entry = self._cache.popitem(last=False)
        else:
            # 默认LRU
            key, entry = self._cache.popitem(last=False)
        
        self.stats.total_size -= entry.size
        self.stats.eviction_count += 1
    
    def _calculate_size(self, data: Any) -> int:
        """计算数据大小"""
        try:
            if isinstance(data, pd.DataFrame):
                return data.memory_usage(deep=True).sum()
            elif isinstance(data, np.ndarray):
                return data.nbytes
            elif isinstance(data, (str, bytes)):
                return len(data)
            else:
                # 使用pickle估算大小
                return len(pickle.dumps(data))
        except Exception:
            return 1024  # 默认1KB
    
    def _compress_data(self, data: Any, compression: CompressionType) -> Tuple[Any, int]:
        """压缩数据"""
        try:
            if compression == CompressionType.GZIP:
                compressed = gzip.compress(pickle.dumps(data))
                return compressed, len(compressed)
            elif compression == CompressionType.PICKLE:
                pickled = pickle.dumps(data)
                return pickled, len(pickled)
            elif compression == CompressionType.PARQUET and isinstance(data, pd.DataFrame):
                # 对于DataFrame，使用parquet格式
                import io
                buffer = io.BytesIO()
                data.to_parquet(buffer)
                compressed = buffer.getvalue()
                return compressed, len(compressed)
            else:
                return data, self._calculate_size(data)
        except Exception as e:
            self.logger.warning(f"数据压缩失败: {e}")
            return data, self._calculate_size(data)
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        with self._lock:
            return self.stats


class DiskCache:
    """
    磁盘缓存管理器
    
    实现持久化的磁盘缓存。
    """
    
    def __init__(self, cache_dir: str = "cache",
                 max_size: int = 10 * 1024 * 1024 * 1024,  # 10GB
                 compression: CompressionType = CompressionType.GZIP):
        self.cache_dir = Path(cache_dir)
        self.max_size = max_size
        self.compression = compression
        self.logger = get_logger(self.__class__.__name__)
        
        # 创建缓存目录
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 元数据数据库
        self.db_path = self.cache_dir / "cache_metadata.db"
        self._init_database()
        
        # 统计信息
        self.stats = CacheStats()
        self._lock = threading.RLock()
        
        # 初始化统计信息
        self._update_stats()
    
    def _init_database(self):
        """初始化元数据数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS cache_entries (
                    key TEXT PRIMARY KEY,
                    filename TEXT NOT NULL,
                    size INTEGER NOT NULL,
                    created_time REAL NOT NULL,
                    last_accessed REAL NOT NULL,
                    access_count INTEGER DEFAULT 0,
                    ttl REAL,
                    compression TEXT NOT NULL,
                    metadata TEXT
                )
            """)
            conn.commit()
    
    def get(self, key: str) -> Optional[Any]:
        """获取磁盘缓存数据"""
        with self._lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute(
                        "SELECT filename, ttl, compression, created_time FROM cache_entries WHERE key = ?",
                        (key,)
                    )
                    row = cursor.fetchone()
                    
                    if not row:
                        self.stats.miss_count += 1
                        return None
                    
                    filename, ttl, compression, created_time = row
                    
                    # 检查是否过期
                    if ttl and time.time() - created_time > ttl:
                        self.delete(key)
                        self.stats.miss_count += 1
                        return None
                    
                    # 读取文件
                    file_path = self.cache_dir / filename
                    if not file_path.exists():
                        self.delete(key)
                        self.stats.miss_count += 1
                        return None
                    
                    data = self._load_data(file_path, CompressionType(compression))
                    
                    # 更新访问信息
                    conn.execute(
                        "UPDATE cache_entries SET last_accessed = ?, access_count = access_count + 1 WHERE key = ?",
                        (time.time(), key)
                    )
                    conn.commit()
                    
                    self.stats.hit_count += 1
                    return data
                    
            except Exception as e:
                self.logger.error(f"读取磁盘缓存失败: {e}")
                self.stats.miss_count += 1
                return None
    
    def put(self, key: str, data: Any, ttl: Optional[float] = None,
            metadata: Optional[Dict[str, Any]] = None) -> bool:
        """存储磁盘缓存数据"""
        with self._lock:
            try:
                # 生成文件名
                filename = self._generate_filename(key)
                file_path = self.cache_dir / filename
                
                # 保存数据
                data_size = self._save_data(file_path, data, self.compression)
                
                # 检查是否需要清理空间
                self._cleanup_if_needed(data_size)
                
                # 更新数据库
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("""
                        INSERT OR REPLACE INTO cache_entries 
                        (key, filename, size, created_time, last_accessed, ttl, compression, metadata)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        key, filename, data_size, time.time(), time.time(),
                        ttl, self.compression.value, json.dumps(metadata or {})
                    ))
                    conn.commit()
                
                self._update_stats()
                return True
                
            except Exception as e:
                self.logger.error(f"存储磁盘缓存失败: {e}")
                return False
    
    def delete(self, key: str) -> bool:
        """删除磁盘缓存条目"""
        with self._lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute("SELECT filename FROM cache_entries WHERE key = ?", (key,))
                    row = cursor.fetchone()
                    
                    if row:
                        filename = row[0]
                        file_path = self.cache_dir / filename
                        
                        # 删除文件
                        if file_path.exists():
                            file_path.unlink()
                        
                        # 删除数据库记录
                        conn.execute("DELETE FROM cache_entries WHERE key = ?", (key,))
                        conn.commit()
                        
                        self._update_stats()
                        return True
                
                return False
                
            except Exception as e:
                self.logger.error(f"删除磁盘缓存失败: {e}")
                return False
    
    def clear(self):
        """清空磁盘缓存"""
        with self._lock:
            try:
                # 删除所有缓存文件
                for file_path in self.cache_dir.glob("cache_*"):
                    if file_path.is_file():
                        file_path.unlink()
                
                # 清空数据库
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("DELETE FROM cache_entries")
                    conn.commit()
                
                self._update_stats()
                
            except Exception as e:
                self.logger.error(f"清空磁盘缓存失败: {e}")
    
    def _generate_filename(self, key: str) -> str:
        """生成缓存文件名"""
        hash_obj = hashlib.md5(key.encode())
        return f"cache_{hash_obj.hexdigest()}.dat"
    
    def _save_data(self, file_path: Path, data: Any, compression: CompressionType) -> int:
        """保存数据到文件"""
        if compression == CompressionType.GZIP:
            with gzip.open(file_path, 'wb') as f:
                pickle.dump(data, f)
        elif compression == CompressionType.PARQUET and isinstance(data, pd.DataFrame):
            data.to_parquet(file_path)
        else:
            with open(file_path, 'wb') as f:
                pickle.dump(data, f)
        
        return file_path.stat().st_size
    
    def _load_data(self, file_path: Path, compression: CompressionType) -> Any:
        """从文件加载数据"""
        if compression == CompressionType.GZIP:
            with gzip.open(file_path, 'rb') as f:
                return pickle.load(f)
        elif compression == CompressionType.PARQUET:
            return pd.read_parquet(file_path)
        else:
            with open(file_path, 'rb') as f:
                return pickle.load(f)
    
    def _cleanup_if_needed(self, new_size: int):
        """清理磁盘空间"""
        current_size = self._get_total_size()
        
        if current_size + new_size > self.max_size:
            # 获取所有条目，按最后访问时间排序
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT key, size FROM cache_entries ORDER BY last_accessed ASC"
                )
                entries = cursor.fetchall()
            
            # 删除最久未访问的条目
            for key, size in entries:
                if current_size + new_size <= self.max_size:
                    break
                
                if self.delete(key):
                    current_size -= size
    
    def _get_total_size(self) -> int:
        """获取总缓存大小"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT SUM(size) FROM cache_entries")
                result = cursor.fetchone()
                return result[0] or 0
        except Exception:
            return 0
    
    def _update_stats(self):
        """更新统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT COUNT(*), SUM(size) FROM cache_entries")
                count, total_size = cursor.fetchone()
                
                self.stats.total_entries = count or 0
                self.stats.total_size = total_size or 0
        except Exception as e:
            self.logger.warning(f"更新统计信息失败: {e}")
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        with self._lock:
            self._update_stats()
            return self.stats


class MultiLevelCacheManager:
    """
    多级缓存管理器

    整合内存缓存和磁盘缓存，提供透明的多级缓存服务。
    """

    def __init__(self,
                 memory_cache_config: Optional[Dict[str, Any]] = None,
                 disk_cache_config: Optional[Dict[str, Any]] = None,
                 enable_preload: bool = True):
        self.logger = get_logger(self.__class__.__name__)

        # 初始化缓存层
        memory_config = memory_cache_config or {}
        self.memory_cache = MemoryCache(
            max_size=memory_config.get('max_size', 1024 * 1024 * 1024),
            max_entries=memory_config.get('max_entries', 10000),
            strategy=CacheStrategy(memory_config.get('strategy', 'lru'))
        )

        disk_config = disk_cache_config or {}
        self.disk_cache = DiskCache(
            cache_dir=disk_config.get('cache_dir', 'cache'),
            max_size=disk_config.get('max_size', 10 * 1024 * 1024 * 1024),
            compression=CompressionType(disk_config.get('compression', 'gzip'))
        )

        # 预加载配置
        self.enable_preload = enable_preload
        self._preload_queue: Optional[asyncio.Queue] = None
        self._preload_task = None

        if enable_preload:
            try:
                self._preload_queue = asyncio.Queue()
            except RuntimeError:
                # 如果没有事件循环，暂时不创建队列
                self._preload_queue = None

        # 缓存键映射（用于预加载）
        self._key_patterns: Dict[str, Callable[[str], bool]] = {}

        # 统计信息
        self._combined_stats = CacheStats()
        self._lock = threading.RLock()

        # 启动预加载任务
        if self.enable_preload:
            self._start_preload_task()

    def get(self, key: str, preload_related: bool = True) -> Optional[Any]:
        """
        获取缓存数据（多级查找）

        Args:
            key: 缓存键
            preload_related: 是否预加载相关数据

        Returns:
            缓存的数据或None
        """
        # 首先尝试内存缓存
        data = self.memory_cache.get(key)
        if data is not None:
            if preload_related and self.enable_preload:
                self._schedule_preload(key)
            return data

        # 尝试磁盘缓存
        data = self.disk_cache.get(key)
        if data is not None:
            # 将数据提升到内存缓存
            self.memory_cache.put(key, data)

            if preload_related and self.enable_preload:
                self._schedule_preload(key)

            return data

        return None

    def put(self, key: str, data: Any,
            cache_level: CacheLevel = CacheLevel.MEMORY,
            ttl: Optional[float] = None,
            compression: CompressionType = CompressionType.NONE,
            metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        存储缓存数据

        Args:
            key: 缓存键
            data: 要缓存的数据
            cache_level: 缓存级别
            ttl: 生存时间
            compression: 压缩类型
            metadata: 元数据

        Returns:
            是否成功存储
        """
        success = True

        if cache_level == CacheLevel.MEMORY:
            success = self.memory_cache.put(key, data, ttl, compression, metadata)
        elif cache_level == CacheLevel.DISK:
            success = self.disk_cache.put(key, data, ttl, metadata)
        else:
            # 同时存储到内存和磁盘
            mem_success = self.memory_cache.put(key, data, ttl, compression, metadata)
            disk_success = self.disk_cache.put(key, data, ttl, metadata)
            success = mem_success or disk_success

        return success

    def delete(self, key: str) -> bool:
        """删除缓存数据（所有级别）"""
        mem_deleted = self.memory_cache.delete(key)
        disk_deleted = self.disk_cache.delete(key)
        return mem_deleted or disk_deleted

    def clear(self, cache_level: Optional[CacheLevel] = None):
        """清空缓存"""
        if cache_level is None or cache_level == CacheLevel.MEMORY:
            self.memory_cache.clear()

        if cache_level is None or cache_level == CacheLevel.DISK:
            self.disk_cache.clear()

    def register_preload_pattern(self, pattern_name: str,
                               pattern_func: Callable[[str], bool]):
        """
        注册预加载模式

        Args:
            pattern_name: 模式名称
            pattern_func: 模式匹配函数，接收key返回是否匹配
        """
        self._key_patterns[pattern_name] = pattern_func

    def _schedule_preload(self, accessed_key: str):
        """调度预加载任务"""
        if not self.enable_preload or not self._preload_queue:
            return

        try:
            # 异步添加到预加载队列
            asyncio.create_task(self._preload_queue.put(accessed_key))
        except Exception as e:
            self.logger.warning(f"调度预加载失败: {e}")

    def _start_preload_task(self):
        """启动预加载任务"""
        if not self.enable_preload:
            return

        async def preload_worker():
            """预加载工作协程"""
            if self._preload_queue is None:
                return

            while True:
                try:
                    accessed_key = await self._preload_queue.get()
                    await self._preload_related_data(accessed_key)
                    self._preload_queue.task_done()
                except Exception as e:
                    self.logger.error(f"预加载任务出错: {e}")

        # 在事件循环中启动预加载任务
        try:
            loop = asyncio.get_event_loop()
            self._preload_task = loop.create_task(preload_worker())
        except RuntimeError:
            # 如果没有事件循环，创建一个新的
            self._preload_task = asyncio.create_task(preload_worker())

    async def _preload_related_data(self, accessed_key: str):
        """预加载相关数据"""
        try:
            # 根据注册的模式查找相关键
            related_keys = []

            for pattern_name, pattern_func in self._key_patterns.items():
                if pattern_func(accessed_key):
                    # 生成相关键（这里需要根据具体业务逻辑实现）
                    related_keys.extend(self._generate_related_keys(accessed_key, pattern_name))

            # 预加载相关数据
            for key in related_keys[:5]:  # 限制预加载数量
                if self.memory_cache.get(key) is None:
                    # 尝试从磁盘加载到内存
                    data = self.disk_cache.get(key)
                    if data is not None:
                        self.memory_cache.put(key, data)

        except Exception as e:
            self.logger.warning(f"预加载相关数据失败: {e}")

    def _generate_related_keys(self, accessed_key: str, pattern_name: str) -> List[str]:
        """生成相关键（需要根据具体业务逻辑实现）"""
        # 这里是一个简单的示例实现
        related_keys = []

        if pattern_name == "stock_data":
            # 对于股票数据，预加载相邻日期的数据
            if "_" in accessed_key:
                parts = accessed_key.split("_")
                if len(parts) >= 2:
                    base_key = "_".join(parts[:-1])
                    try:
                        date_part = parts[-1]
                        # 生成相邻日期的键
                        for i in range(1, 4):
                            related_keys.append(f"{base_key}_{date_part}_{i}")
                    except Exception:
                        pass

        return related_keys

    def get_combined_stats(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        with self._lock:
            memory_stats = self.memory_cache.get_stats()
            disk_stats = self.disk_cache.get_stats()

            return {
                'memory_cache': {
                    'total_entries': memory_stats.total_entries,
                    'total_size': memory_stats.total_size,
                    'hit_rate': memory_stats.hit_rate,
                    'average_size': memory_stats.average_size
                },
                'disk_cache': {
                    'total_entries': disk_stats.total_entries,
                    'total_size': disk_stats.total_size,
                    'hit_rate': disk_stats.hit_rate,
                    'average_size': disk_stats.average_size
                },
                'combined': {
                    'total_entries': memory_stats.total_entries + disk_stats.total_entries,
                    'total_size': memory_stats.total_size + disk_stats.total_size,
                    'overall_hit_rate': (memory_stats.hit_count + disk_stats.hit_count) /
                                      max(1, memory_stats.hit_count + memory_stats.miss_count +
                                          disk_stats.hit_count + disk_stats.miss_count)
                }
            }

    def optimize_cache(self):
        """优化缓存性能"""
        try:
            # 清理过期条目
            self._cleanup_expired_entries()

            # 内存缓存优化
            self._optimize_memory_cache()

            # 磁盘缓存优化
            self._optimize_disk_cache()

            self.logger.info("缓存优化完成")

        except Exception as e:
            self.logger.error(f"缓存优化失败: {e}")

    def _cleanup_expired_entries(self):
        """清理过期条目"""
        # 这里需要实现具体的过期清理逻辑
        pass

    def _optimize_memory_cache(self):
        """优化内存缓存"""
        # 触发垃圾回收
        gc.collect()

        # 可以添加更多内存优化逻辑
        pass

    def _optimize_disk_cache(self):
        """优化磁盘缓存"""
        # 可以添加磁盘碎片整理等优化逻辑
        pass

    def __del__(self):
        """析构函数"""
        if self._preload_task and not self._preload_task.done():
            self._preload_task.cancel()


# 全局缓存管理器实例
cache_manager = MultiLevelCacheManager()

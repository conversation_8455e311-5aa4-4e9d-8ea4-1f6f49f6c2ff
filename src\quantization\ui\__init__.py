#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户界面模块

提供基于Web的用户界面，包含策略管理、回测执行、结果展示等功能。
"""

try:
    from .dashboard import QuantizationDashboard
    DASHBOARD_AVAILABLE = True
except ImportError:
    DASHBOARD_AVAILABLE = False

__all__ = []

if DASHBOARD_AVAILABLE:
    __all__.append('QuantizationDashboard')

__version__ = "1.0.0"
__author__ = "Quantization Team"
__description__ = "量化交易用户界面模块"

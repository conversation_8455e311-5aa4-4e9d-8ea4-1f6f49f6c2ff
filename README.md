# A-Share Data Acquisition Framework

A comprehensive, modular framework for acquiring Chinese stock market (A-Share) data for backtesting purposes. The framework provides robust data collection from multiple sources, intelligent caching, data validation, and a clean interface for backtesting systems.

## Features

### 🚀 **Multi-Source Data Acquisition**
- **Primary Source**: akshare library API for reliable A-Share data
- **Fallback Sources**: Web scraping from Tencent Finance and East Money
- **Automatic Failover**: Seamlessly switches between sources when one fails

### 📊 **Comprehensive Data Types**
- **Stock Price Data**: OHLCV (Open, High, Low, Close, Volume) with adjustments
- **Market Indices**: Shanghai Composite, Shenzhen Component, ChiNext, CSI 300
- **Financial Data**: Balance sheets, income statements, cash flow statements
- **Stock Information**: Company details, sector classification, market cap

### 🏗️ **Robust Architecture**
- **Modular Design**: Easy to extend and maintain
- **Thread-Safe**: Concurrent data fetching for multiple stocks
- **Rate Limiting**: Respects API limits and prevents blocking
- **Error Handling**: Comprehensive error handling with retry mechanisms

### 💾 **Intelligent Storage & Caching**
- **Local Database**: SQLite storage with optional PostgreSQL support
- **Smart Caching**: Configurable TTL-based caching system
- **Data Validation**: Automatic data quality checks and cleaning
- **Deduplication**: Handles duplicate data automatically

### 🔧 **Backtesting Integration**
- **Clean Interface**: Purpose-built for backtesting systems
- **Data Preprocessing**: Technical indicators, normalization, resampling
- **Portfolio Support**: Multi-stock data alignment and matrix operations
- **Performance Optimized**: Efficient data access patterns

## Installation

### Prerequisites
```bash
# Install required packages
pip install pandas numpy requests beautifulsoup4 akshare sqlite3
```

### Framework Setup
1. Clone or download the framework to your project directory
2. Ensure the `data_acquisition` folder is in your Python path
3. Create necessary directories (will be created automatically on first run)

## Quick Start

### Basic Usage

```python
from data_acquisition import DataManager

# Initialize data manager
dm = DataManager()

# Get stock data
data = dm.get_stock_data('000001.SZ', '2023-01-01', '2023-12-31')
print(f"Retrieved {len(data)} records")
print(data.head())

# Get multiple stocks
stocks = ['000001.SZ', '000002.SZ', '600000.SH']
all_data = dm.get_multiple_stocks_data(stocks, '2023-01-01', '2023-12-31')

# Cleanup
dm.cleanup()
```

### Backtesting Interface

```python
from data_acquisition import BacktestingDataInterface

# Initialize backtesting interface
bt = BacktestingDataInterface()

# Get price matrix for portfolio analysis
stocks = ['000001.SZ', '000002.SZ', '600000.SH']
price_matrix = bt.get_price_matrix(stocks, '2023-01-01', '2023-12-31')

# Get returns matrix
returns_matrix = bt.get_returns_matrix(stocks, '2023-01-01', '2023-12-31')

# Get market data
market_data = bt.get_market_data('2023-01-01', '2023-12-31')

bt.cleanup()
```

## Configuration

### Environment Variables
```bash
# Database settings
export DATABASE_URL="sqlite:///data/ashare_data.db"
export CACHE_ENABLED="true"
export CACHE_TTL_DAYS="7"

# Rate limiting
export AKSHARE_RATE_LIMIT="0.5"
export WEB_SCRAPER_RATE_LIMIT="1.0"
export MAX_RETRIES="3"

# Logging
export LOG_LEVEL="INFO"
```

### Custom Configuration
```python
from data_acquisition.config import Config

# Create custom config
config = Config()
config.CACHE_TTL_DAYS = 1
config.AKSHARE_RATE_LIMIT = 0.1
config.LOG_LEVEL = 'DEBUG'

# Use with data manager
dm = DataManager(config)
```

## Stock Code Formats

The framework supports standard Chinese stock code formats:

- **Shanghai Stock Exchange**: `600000.SH` (Main Board), `688000.SH` (STAR Market)
- **Shenzhen Stock Exchange**: `000001.SZ` (Main Board), `300001.SZ` (ChiNext)

```python
from data_acquisition.utils import normalize_stock_code, validate_stock_codes

# Normalize codes
code = normalize_stock_code('000001')  # Returns '000001.SZ'

# Validate multiple codes
valid, invalid = validate_stock_codes(['000001', '600000.SH', 'INVALID'])
```

## Data Validation & Quality

The framework includes comprehensive data validation:

```python
from data_acquisition.utils import DataValidator

validator = DataValidator()

# Validate price data
is_valid, errors = validator.validate_price_data(data, '000001.SZ')

# Clean data
cleaned_data = validator.clean_data(data, '000001.SZ')
```

## Examples

### Single Stock Analysis
```python
# See examples/basic_usage.py
python examples/basic_usage.py
```

### Portfolio Backtesting
```python
# See examples/backtesting_example.py
python examples/backtesting_example.py
```

## Architecture

```
data_acquisition/
├── core/                   # Core data acquisition
│   ├── base_provider.py   # Abstract provider base
│   ├── akshare_provider.py # Akshare data provider
│   ├── web_scraper.py     # Web scraping provider
│   └── data_manager.py    # Central coordinator
├── storage/               # Data storage
│   ├── database.py        # Database operations
│   └── cache_manager.py   # Caching system
├── utils/                 # Utilities
│   ├── stock_codes.py     # Stock code handling
│   ├── rate_limiter.py    # Rate limiting
│   ├── data_validator.py  # Data validation
│   └── logger.py          # Logging
├── config/                # Configuration
│   └── settings.py        # Settings management
└── backtesting_interface/ # Backtesting interface
    ├── data_interface.py  # Clean data interface
    └── data_preprocessor.py # Data preprocessing
```

## Performance Considerations

### Caching Strategy
- **Cache Hit Rate**: Typically 80%+ for repeated queries
- **Storage Efficiency**: Compressed pickle format
- **TTL Management**: Configurable expiration times

### Rate Limiting
- **Akshare**: 0.5 seconds between requests (configurable)
- **Web Scraping**: 1.0 seconds between requests
- **Concurrent Fetching**: Up to 5 parallel requests

### Memory Usage
- **Streaming**: Large datasets processed in chunks
- **Cleanup**: Automatic resource cleanup
- **Optimization**: Efficient pandas operations

## Error Handling

The framework provides robust error handling:

```python
try:
    data = dm.get_stock_data('000001.SZ', '2023-01-01', '2023-12-31')
except Exception as e:
    print(f"Error: {e}")
    # Framework logs detailed error information
```

## Logging

Comprehensive logging system:

```python
from data_acquisition.utils import get_logger

logger = get_logger('my_module')
logger.info("Custom log message")
```

Log files are stored in `logs/` directory with automatic rotation.

## Contributing

1. Follow the modular architecture
2. Add comprehensive error handling
3. Include unit tests for new features
4. Update documentation

## License

This framework is provided as-is for educational and research purposes. Please ensure compliance with data source terms of service.

## Support

For issues and questions:
1. Check the examples in `examples/` directory
2. Review the configuration in `config/settings.py`
3. Enable debug logging for detailed information

## Roadmap

- [ ] Support for more data sources
- [ ] Real-time data streaming
- [ ] Advanced technical indicators
- [ ] Machine learning features
- [ ] Cloud storage backends
- [ ] REST API interface

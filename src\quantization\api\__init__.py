#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口模块

提供RESTful API接口，支持策略管理、回测执行、数据查询等功能。
"""

try:
    from .api_server import (
        QuantizationAPI,
        APIResponse,
        RateLimiter,
        APIMetrics,
        api_server
    )
    API_AVAILABLE = True
except ImportError:
    API_AVAILABLE = False

__all__ = []

if API_AVAILABLE:
    __all__.extend([
        'QuantizationAPI',
        'APIResponse',
        'RateLimiter',
        'APIMetrics',
        'api_server'
    ])

__version__ = "1.0.0"
__author__ = "Quantization Team"
__description__ = "量化交易API接口模块"

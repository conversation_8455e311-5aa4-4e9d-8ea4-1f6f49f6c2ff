"""
A股数据采集框架基础测试脚本

专为Python 3.11环境设计的简化测试脚本
测试框架的基本功能，不依赖可能缺失的第三方包
"""

import sys
import os
from pathlib import Path

def test_python_version():
    """测试Python版本"""
    print("🐍 检查Python版本...")
    
    version = sys.version_info
    print(f"   当前版本: Python {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    elif version >= (3, 12):
        print("⚠️  Python 3.12+可能存在兼容性问题")
    
    print("✅ Python版本检查通过")
    return True

def test_builtin_modules():
    """测试Python内置模块"""
    print("\n📦 测试Python内置模块...")
    
    builtin_modules = [
        'sqlite3',
        'logging', 
        'threading',
        'concurrent.futures',
        'json',
        'pickle',
        'hashlib',
        'datetime',
        'pathlib'
    ]
    
    success_count = 0
    for module_name in builtin_modules:
        try:
            __import__(module_name)
            print(f"   ✅ {module_name}")
            success_count += 1
        except ImportError:
            print(f"   ❌ {module_name}")
    
    print(f"✅ 内置模块测试: {success_count}/{len(builtin_modules)} 通过")
    return success_count == len(builtin_modules)

def test_third_party_packages():
    """测试第三方包"""
    print("\n📦 测试第三方依赖包...")
    
    packages = {
        'pandas': '数据处理',
        'numpy': '数值计算',
        'requests': 'HTTP请求',
        'beautifulsoup4': 'HTML解析',
        'akshare': 'A股数据源',
        'matplotlib': '图表绘制'
    }
    
    available_packages = []
    missing_packages = []
    
    for package, description in packages.items():
        try:
            if package == 'beautifulsoup4':
                import bs4
                print(f"   ✅ {package} ({description})")
                available_packages.append(package)
            else:
                __import__(package)
                print(f"   ✅ {package} ({description})")
                available_packages.append(package)
        except ImportError:
            print(f"   ❌ {package} ({description}) - 未安装")
            missing_packages.append(package)
    
    print(f"\n📊 第三方包状态: {len(available_packages)}/{len(packages)} 可用")
    
    if missing_packages:
        print(f"⚠️  缺失的包: {', '.join(missing_packages)}")
        print("   可以运行以下命令安装:")
        print(f"   pip install {' '.join(missing_packages)}")
    
    # 只要有基础包就认为通过
    essential_packages = ['requests']
    has_essential = any(pkg in available_packages for pkg in essential_packages)
    
    return has_essential, available_packages, missing_packages

def test_framework_structure():
    """测试框架目录结构"""
    print("\n📁 检查框架目录结构...")
    
    required_dirs = [
        'data_acquisition',
        'data_acquisition/core',
        'data_acquisition/storage', 
        'data_acquisition/utils',
        'data_acquisition/config',
        'data_acquisition/backtesting_interface',
        'examples'
    ]
    
    required_files = [
        'data_acquisition/__init__.py',
        'data_acquisition/core/__init__.py',
        'data_acquisition/config/settings.py',
        'README_CN.md',
        'requirements.txt'
    ]
    
    missing_items = []
    
    # 检查目录
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"   ✅ 目录: {dir_path}")
        else:
            print(f"   ❌ 目录: {dir_path}")
            missing_items.append(dir_path)
    
    # 检查文件
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"   ✅ 文件: {file_path}")
        else:
            print(f"   ❌ 文件: {file_path}")
            missing_items.append(file_path)
    
    if missing_items:
        print(f"\n⚠️  缺失的项目: {len(missing_items)} 个")
        return False
    else:
        print("✅ 框架结构检查通过")
        return True

def test_basic_imports():
    """测试基础导入"""
    print("\n🔧 测试框架基础导入...")
    
    import_tests = [
        ('配置模块', 'data_acquisition.config.settings', 'Config'),
        ('工具模块', 'data_acquisition.utils.stock_codes', 'normalize_stock_code'),
        ('存储模块', 'data_acquisition.storage.database', 'DatabaseManager'),
    ]
    
    success_count = 0
    
    for test_name, module_path, class_or_func in import_tests:
        try:
            module = __import__(module_path, fromlist=[class_or_func])
            getattr(module, class_or_func)
            print(f"   ✅ {test_name}")
            success_count += 1
        except ImportError as e:
            print(f"   ❌ {test_name}: 导入错误 - {e}")
        except AttributeError as e:
            print(f"   ❌ {test_name}: 属性错误 - {e}")
        except Exception as e:
            print(f"   ⚠️  {test_name}: 其他错误 - {e}")
    
    print(f"✅ 基础导入测试: {success_count}/{len(import_tests)} 通过")
    return success_count >= len(import_tests) * 0.8  # 80%通过率

def test_config_system():
    """测试配置系统"""
    print("\n⚙️  测试配置系统...")
    
    try:
        from data_acquisition.config.settings import Config, get_config
        
        # 测试默认配置
        config = Config()
        print(f"   ✅ 默认配置创建成功")
        
        # 测试配置属性
        attrs_to_check = ['CACHE_ENABLED', 'DATABASE_URL', 'LOG_LEVEL']
        for attr in attrs_to_check:
            if hasattr(config, attr):
                value = getattr(config, attr)
                print(f"   ✅ 配置项 {attr}: {value}")
            else:
                print(f"   ❌ 缺少配置项: {attr}")
        
        # 测试配置工厂
        dev_config = get_config('dev')
        print(f"   ✅ 开发配置创建成功")
        
        # 测试目录创建
        config.create_directories()
        print(f"   ✅ 目录创建成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 配置系统错误: {e}")
        return False

def test_stock_codes():
    """测试股票代码工具"""
    print("\n📈 测试股票代码工具...")
    
    try:
        from data_acquisition.utils.stock_codes import normalize_stock_code, validate_stock_codes
        
        # 测试代码标准化
        test_cases = [
            ('000001', '000001.SZ'),
            ('600000', '600000.SH'),
            ('300001', '300001.SZ'),
            ('688001', '688001.SH')
        ]
        
        for input_code, expected in test_cases:
            result = normalize_stock_code(input_code)
            if result == expected:
                print(f"   ✅ {input_code} -> {result}")
            else:
                print(f"   ❌ {input_code} -> {result} (期望: {expected})")
        
        # 测试批量验证
        test_codes = ['000001', '600000.SH', 'INVALID', '300001.SZ']
        valid, invalid = validate_stock_codes(test_codes)
        print(f"   ✅ 批量验证: {len(valid)} 有效, {len(invalid)} 无效")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 股票代码工具错误: {e}")
        return False

def create_test_directories():
    """创建测试所需的目录"""
    print("\n📁 创建测试目录...")
    
    test_dirs = ['data', 'data/cache', 'logs', 'results']
    
    for dir_path in test_dirs:
        try:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            print(f"   ✅ 创建目录: {dir_path}")
        except Exception as e:
            print(f"   ❌ 创建目录失败 {dir_path}: {e}")

def run_basic_tests():
    """运行基础测试"""
    print("🧪 A股数据采集框架 - 基础测试")
    print("=" * 50)
    print("专为Python 3.11环境设计的简化测试")
    print("=" * 50)
    
    tests = [
        ("Python版本", test_python_version),
        ("内置模块", test_builtin_modules),
        ("框架结构", test_framework_structure),
        ("基础导入", test_basic_imports),
        ("配置系统", test_config_system),
        ("股票代码工具", test_stock_codes)
    ]
    
    # 创建测试目录
    create_test_directories()
    
    # 运行测试
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔄 测试 {test_name}...")
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 测试第三方包（单独处理）
    print(f"\n🔄 测试第三方依赖包...")
    has_essential, available, missing = test_third_party_packages()
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 项基础测试通过")
    
    if passed >= total * 0.8:  # 80%通过率
        print("🎉 基础测试通过！框架核心功能可用。")
        
        if missing:
            print(f"\n📦 建议安装缺失的包以获得完整功能:")
            print(f"   pip install {' '.join(missing)}")
        
        print(f"\n📚 接下来可以:")
        print(f"   1. 安装缺失的依赖包")
        print(f"   2. 运行完整测试: python test_framework_cn.py")
        print(f"   3. 查看使用示例: python examples/basic_usage_cn.py")
        
    else:
        print("⚠️  基础测试未完全通过，请检查框架安装。")
        print(f"\n🔧 故障排除:")
        print(f"   1. 确保在正确的目录中运行测试")
        print(f"   2. 检查框架文件是否完整")
        print(f"   3. 运行安装脚本: python setup_framework_cn.py")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = run_basic_tests()
    sys.exit(0 if success else 1)

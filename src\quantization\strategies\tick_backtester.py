#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TICK数据回测引擎
用于创业板动态因子策略的高频回测
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass
from enum import Enum
import sqlite3
from .tick_data_manager import TickDataManager


class OrderType(Enum):
    """订单类型"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    PARTIAL_FILLED = "partial_filled"


@dataclass
class Order:
    """订单类"""
    order_id: str
    stock_code: str
    order_type: OrderType
    quantity: int
    price: float
    timestamp: datetime
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: int = 0
    filled_price: float = 0.0
    commission: float = 0.0


@dataclass
class Position:
    """持仓类"""
    stock_code: str
    quantity: int
    avg_cost: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float = 0.0


@dataclass
class BacktestResult:
    """回测结果类"""
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    total_trades: int
    profit_trades: int
    loss_trades: int
    avg_profit: float
    avg_loss: float
    positions: List[Position]
    orders: List[Order]
    equity_curve: pd.DataFrame


class TickBacktester:
    """TICK数据回测引擎"""
    
    def __init__(self, initial_capital: float = 1000000, commission_rate: float = 0.0003,
                 slippage: float = 0.001, db_path: str = "data/chinext_minute_data.db"):
        """
        初始化回测引擎

        Args:
            initial_capital: 初始资金
            commission_rate: 手续费率
            slippage: 滑点率
            db_path: 数据库路径
        """
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage = slippage
        self.db_path = db_path
        self.logger = logging.getLogger(self.__class__.__name__)

        # 回测状态
        self.current_capital = initial_capital
        self.positions: Dict[str, Position] = {}
        self.orders: List[Order] = []
        self.trades: List[Dict] = []
        self.equity_curve: List[Dict] = []

        # TICK数据管理器
        self.tick_manager = TickDataManager()

        # 交易时间设置（精确到分钟）
        self.market_open = time(9, 30)
        self.market_close = time(15, 0)
        self.buy_time = time(14, 30)  # 收盘前30分钟
        self.sell_time = time(9, 59)  # 开盘后30分钟（避开集合竞价）

        # 滑点和手续费模型
        self.min_commission = 5.0  # 最低手续费5元
        self.stamp_tax_rate = 0.001  # 印花税率（仅卖出收取）
        
    def run_backtest(self, strategy, start_date: str, end_date: str, 
                    stock_universe: List[str]) -> BacktestResult:
        """
        运行回测
        
        Args:
            strategy: 交易策略
            start_date: 开始日期
            end_date: 结束日期
            stock_universe: 股票池
            
        Returns:
            BacktestResult: 回测结果
        """
        self.logger.info(f"开始TICK回测: {start_date} 至 {end_date}")
        
        # 生成交易日期
        trade_dates = self._generate_trade_dates(start_date, end_date)
        
        # 确保TICK数据完整性
        self._ensure_tick_data(stock_universe, trade_dates)
        
        # 执行回测
        for i, date in enumerate(trade_dates):
            self.logger.info(f"回测日期: {date} ({i+1}/{len(trade_dates)})")
            
            # 生成交易信号
            signals = strategy.generate_signals(date)
            
            if not signals['market_condition']:
                self.logger.info(f"{date}: 市场条件不满足，跳过交易")
                continue
            
            # 处理买入信号
            if signals['buy_signals']:
                self._process_buy_signals(signals['buy_signals'], date)
            
            # 处理卖出信号（第二天）
            if i > 0:  # 从第二天开始处理卖出
                self._process_sell_signals(date)
            
            # 更新持仓市值和权益曲线
            self._update_portfolio_value(date)
        
        # 计算回测结果
        return self._calculate_backtest_result()
    
    def _generate_trade_dates(self, start_date: str, end_date: str) -> List[str]:
        """生成交易日期列表"""
        try:
            # 这里简化处理，实际应该获取真实交易日历
            start = datetime.strptime(start_date, '%Y-%m-%d')
            end = datetime.strptime(end_date, '%Y-%m-%d')
            
            dates = []
            current = start
            while current <= end:
                # 排除周末
                if current.weekday() < 5:
                    dates.append(current.strftime('%Y-%m-%d'))
                current += timedelta(days=1)
            
            return dates
            
        except Exception as e:
            self.logger.error(f"生成交易日期失败: {e}")
            return []
    
    def _ensure_tick_data(self, stock_codes: List[str], dates: List[str]):
        """确保TICK数据完整性"""
        self.logger.info("检查TICK数据完整性...")
        
        missing_data = []
        for stock_code in stock_codes:
            for date in dates:
                if not self.tick_manager._is_data_exists(stock_code, date):
                    missing_data.append((stock_code, date))
        
        if missing_data:
            self.logger.info(f"需要下载 {len(missing_data)} 个TICK数据")
            
            # 批量下载缺失数据
            stock_codes_to_download = list(set([item[0] for item in missing_data]))
            dates_to_download = list(set([item[1] for item in missing_data]))
            
            results = self.tick_manager.batch_download_tick_data(
                stock_codes_to_download, dates_to_download
            )
            
            self.logger.info(f"TICK数据下载完成: {results}")
        else:
            self.logger.info("TICK数据完整，无需下载")
    
    def _process_buy_signals(self, buy_signals: List[Dict], date: str):
        """处理买入信号"""
        for signal in buy_signals:
            stock_code = signal['stock_code']
            quantity = signal['quantity']
            
            # 获取TICK数据
            tick_data = self.tick_manager.get_tick_data(stock_code, date)
            if tick_data is None:
                self.logger.warning(f"无TICK数据: {stock_code} {date}")
                continue
            
            # 找到买入时点的价格（收盘前30分钟）
            buy_price = self._get_execution_price(tick_data, self.buy_time, 'buy')
            if buy_price is None:
                continue
            
            # 创建买入订单
            order = self._create_order(stock_code, OrderType.BUY, quantity, buy_price, date)
            
            # 执行订单
            self._execute_order(order, buy_price)
    
    def _process_sell_signals(self, date: str):
        """处理卖出信号（卖出所有持仓）"""
        for stock_code, position in list(self.positions.items()):
            if position.quantity <= 0:
                continue
            
            # 获取TICK数据
            tick_data = self.tick_manager.get_tick_data(stock_code, date)
            if tick_data is None:
                self.logger.warning(f"无TICK数据: {stock_code} {date}")
                continue
            
            # 找到卖出时点的价格（开盘后30分钟）
            sell_price = self._get_execution_price(tick_data, self.sell_time, 'sell')
            if sell_price is None:
                continue
            
            # 创建卖出订单
            order = self._create_order(stock_code, OrderType.SELL, position.quantity, sell_price, date)
            
            # 执行订单
            self._execute_order(order, sell_price)
    
    def _get_execution_price(self, tick_data: pd.DataFrame, target_time: time,
                           order_type: str) -> Optional[float]:
        """
        获取指定时间的执行价格（基于分时数据）

        Args:
            tick_data: 分时数据DataFrame
            target_time: 目标时间
            order_type: 订单类型 ('buy' 或 'sell')

        Returns:
            执行价格
        """
        try:
            if tick_data.empty:
                return None

            # 转换目标时间为字符串格式
            target_time_str = target_time.strftime('%H:%M:%S')

            # 如果数据有time列，直接匹配
            if 'time' in tick_data.columns:
                # 查找最接近目标时间的数据
                time_diffs = []
                for idx, row in tick_data.iterrows():
                    try:
                        data_time = datetime.strptime(row['time'], '%H:%M:%S').time()
                        target_time_obj = datetime.strptime(target_time_str, '%H:%M:%S').time()

                        # 计算时间差（秒）
                        data_seconds = data_time.hour * 3600 + data_time.minute * 60 + data_time.second
                        target_seconds = target_time_obj.hour * 3600 + target_time_obj.minute * 60 + target_time_obj.second

                        time_diffs.append(abs(data_seconds - target_seconds))
                    except:
                        time_diffs.append(float('inf'))

                if time_diffs:
                    closest_idx_pos = int(np.argmin(time_diffs))
                    closest_idx = tick_data.index[closest_idx_pos]
                    base_price = float(tick_data.loc[closest_idx, 'close'])
                else:
                    # 如果没有找到合适的时间，使用最后一个价格
                    base_price = float(tick_data['close'].iloc[-1])
            else:
                # 如果没有time列，使用最后一个价格
                base_price = float(tick_data['close'].iloc[-1])

            # 应用滑点模型
            if order_type == 'buy':
                execution_price = base_price * (1 + self.slippage)
            else:
                execution_price = base_price * (1 - self.slippage)

            return execution_price

        except Exception as e:
            self.logger.error(f"获取执行价格失败: {e}")
            return None

    def _calculate_commission(self, amount: float, order_type: str) -> float:
        """
        计算手续费

        Args:
            amount: 交易金额
            order_type: 订单类型

        Returns:
            手续费金额
        """
        # 佣金
        commission = max(amount * self.commission_rate, self.min_commission)

        # 印花税（仅卖出收取）
        if order_type == 'sell':
            stamp_tax = amount * self.stamp_tax_rate
            commission += stamp_tax

        return commission
    
    def _create_order(self, stock_code: str, order_type: OrderType, quantity: int, 
                     price: float, date: str) -> Order:
        """创建订单"""
        order_id = f"{stock_code}_{order_type.value}_{date}_{len(self.orders)}"
        timestamp = datetime.strptime(date, '%Y-%m-%d')
        
        return Order(
            order_id=order_id,
            stock_code=stock_code,
            order_type=order_type,
            quantity=quantity,
            price=price,
            timestamp=timestamp
        )
    
    def _execute_order(self, order: Order, execution_price: float):
        """执行订单"""
        try:
            # 计算手续费
            commission = order.quantity * execution_price * self.commission_rate
            
            if order.order_type == OrderType.BUY:
                # 检查资金是否充足
                total_cost = order.quantity * execution_price + commission
                if total_cost > self.current_capital:
                    self.logger.warning(f"资金不足，无法执行买入订单: {order.order_id}")
                    order.status = OrderStatus.CANCELLED
                    return
                
                # 扣除资金
                self.current_capital -= total_cost
                
                # 更新持仓
                if order.stock_code in self.positions:
                    pos = self.positions[order.stock_code]
                    total_quantity = pos.quantity + order.quantity
                    total_cost = pos.quantity * pos.avg_cost + order.quantity * execution_price
                    pos.avg_cost = total_cost / total_quantity
                    pos.quantity = total_quantity
                else:
                    self.positions[order.stock_code] = Position(
                        stock_code=order.stock_code,
                        quantity=order.quantity,
                        avg_cost=execution_price,
                        market_value=order.quantity * execution_price,
                        unrealized_pnl=0.0
                    )
                
            else:  # SELL
                if order.stock_code not in self.positions:
                    self.logger.warning(f"无持仓，无法执行卖出订单: {order.order_id}")
                    order.status = OrderStatus.CANCELLED
                    return
                
                pos = self.positions[order.stock_code]
                if pos.quantity < order.quantity:
                    self.logger.warning(f"持仓不足，无法执行卖出订单: {order.order_id}")
                    order.status = OrderStatus.CANCELLED
                    return
                
                # 计算盈亏
                sell_amount = order.quantity * execution_price - commission
                cost_amount = order.quantity * pos.avg_cost
                realized_pnl = sell_amount - cost_amount
                
                # 增加资金
                self.current_capital += sell_amount
                
                # 更新持仓
                pos.quantity -= order.quantity
                pos.realized_pnl += realized_pnl
                
                # 记录交易
                self.trades.append({
                    'stock_code': order.stock_code,
                    'buy_price': pos.avg_cost,
                    'sell_price': execution_price,
                    'quantity': order.quantity,
                    'pnl': realized_pnl,
                    'date': order.timestamp.strftime('%Y-%m-%d')
                })
                
                # 如果持仓为0，删除持仓记录
                if pos.quantity == 0:
                    del self.positions[order.stock_code]
            
            # 更新订单状态
            order.status = OrderStatus.FILLED
            order.filled_quantity = order.quantity
            order.filled_price = execution_price
            order.commission = commission
            
            self.orders.append(order)
            
            self.logger.info(f"订单执行成功: {order.order_id}, 价格: {execution_price:.2f}")
            
        except Exception as e:
            self.logger.error(f"执行订单失败: {order.order_id} - {e}")
            order.status = OrderStatus.CANCELLED
    
    def _update_portfolio_value(self, date: str):
        """更新投资组合价值"""
        try:
            total_market_value = 0.0
            
            # 计算持仓市值
            for stock_code, position in self.positions.items():
                # 获取当日收盘价
                tick_data = self.tick_manager.get_tick_data(stock_code, date)
                if tick_data is not None and not tick_data.empty:
                    current_price = tick_data.iloc[-1]['price']  # 最后一个价格作为收盘价
                    position.market_value = position.quantity * current_price
                    position.unrealized_pnl = position.market_value - position.quantity * position.avg_cost
                    total_market_value += position.market_value
            
            # 计算总资产
            total_equity = self.current_capital + total_market_value
            
            # 记录权益曲线
            self.equity_curve.append({
                'date': date,
                'cash': self.current_capital,
                'market_value': total_market_value,
                'total_equity': total_equity,
                'return': (total_equity - self.initial_capital) / self.initial_capital
            })
            
        except Exception as e:
            self.logger.error(f"更新投资组合价值失败: {date} - {e}")
    
    def _calculate_backtest_result(self) -> BacktestResult:
        """计算回测结果"""
        try:
            if not self.equity_curve:
                return BacktestResult(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, [], [], pd.DataFrame())
            
            # 转换为DataFrame
            equity_df = pd.DataFrame(self.equity_curve)
            equity_df['date'] = pd.to_datetime(equity_df['date'])
            equity_df.set_index('date', inplace=True)
            
            # 计算收益率
            returns = equity_df['return'].pct_change().dropna()
            
            # 总收益率
            total_return = equity_df['return'].iloc[-1]
            
            # 年化收益率
            days = (equity_df.index[-1] - equity_df.index[0]).days
            annual_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0
            
            # 最大回撤
            rolling_max = equity_df['total_equity'].expanding().max()
            drawdown = (equity_df['total_equity'] - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            # 夏普比率
            if len(returns) > 1:
                sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
            else:
                sharpe_ratio = 0
            
            # 交易统计
            profit_trades = len([t for t in self.trades if t['pnl'] > 0])
            loss_trades = len([t for t in self.trades if t['pnl'] < 0])
            total_trades = len(self.trades)
            win_rate = profit_trades / total_trades if total_trades > 0 else 0
            
            # 平均盈亏
            profits = [t['pnl'] for t in self.trades if t['pnl'] > 0]
            losses = [t['pnl'] for t in self.trades if t['pnl'] < 0]
            avg_profit = np.mean(profits) if profits else 0
            avg_loss = np.mean(losses) if losses else 0
            
            return BacktestResult(
                total_return=total_return,
                annual_return=annual_return,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                win_rate=win_rate,
                total_trades=total_trades,
                profit_trades=profit_trades,
                loss_trades=loss_trades,
                avg_profit=avg_profit,
                avg_loss=avg_loss,
                positions=list(self.positions.values()),
                orders=self.orders,
                equity_curve=equity_df
            )
            
        except Exception as e:
            self.logger.error(f"计算回测结果失败: {e}")
            return BacktestResult(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, [], [], pd.DataFrame())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据提供商基类

定义数据提供商的通用功能和接口实现。
"""

import time
import logging
from abc import ABC
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date
import pandas as pd

from quantization.core.interfaces.data_interface import DataInterface
from quantization.utils.logger import get_logger
from quantization.utils.rate_limiter import RateLimiter


class BaseProvider(DataInterface, ABC):
    """
    数据提供商基类
    
    提供数据提供商的通用功能，包括：
    - 频率限制
    - 错误处理
    - 日志记录
    - 缓存机制
    """
    
    def __init__(
        self, 
        name: str,
        rate_limit: Optional[float] = None,
        timeout: float = 30.0,
        max_retries: int = 3
    ):
        """
        初始化数据提供商
        
        Args:
            name: 提供商名称
            rate_limit: 请求频率限制（秒）
            timeout: 请求超时时间
            max_retries: 最大重试次数
        """
        self.name = name
        self.timeout = timeout
        self.max_retries = max_retries
        self.logger = get_logger(f"{self.__class__.__name__}")
        
        # 初始化频率限制器
        if rate_limit:
            self.rate_limiter = RateLimiter(rate_limit)
        else:
            self.rate_limiter = None
        
        # 统计信息
        self.stats = {
            'requests_count': 0,
            'success_count': 0,
            'error_count': 0,
            'last_request_time': None,
            'total_request_time': 0.0
        }
        
        self.logger.info(f"初始化数据提供商: {self.name}")
    
    def _make_request(self, func, *args, **kwargs) -> Any:
        """
        执行请求的通用方法
        
        Args:
            func: 要执行的函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            函数执行结果
        """
        # 频率限制
        if self.rate_limiter:
            self.rate_limiter.wait()
        
        start_time = time.time()
        self.stats['requests_count'] += 1
        
        for attempt in range(self.max_retries):
            try:
                result = func(*args, **kwargs)
                
                # 更新统计信息
                end_time = time.time()
                request_time = end_time - start_time
                self.stats['success_count'] += 1
                self.stats['last_request_time'] = datetime.now()
                self.stats['total_request_time'] += request_time
                
                self.logger.debug(
                    f"请求成功: {func.__name__}, "
                    f"耗时: {request_time:.2f}s, "
                    f"尝试次数: {attempt + 1}"
                )
                
                return result
                
            except Exception as e:
                self.stats['error_count'] += 1
                self.logger.warning(
                    f"请求失败: {func.__name__}, "
                    f"尝试次数: {attempt + 1}/{self.max_retries}, "
                    f"错误: {str(e)}"
                )
                
                if attempt == self.max_retries - 1:
                    self.logger.error(
                        f"请求最终失败: {func.__name__}, "
                        f"已达到最大重试次数: {self.max_retries}"
                    )
                    raise
                
                # 重试前等待
                time.sleep(2 ** attempt)  # 指数退避
        
        return None
    
    def _validate_stock_code(self, stock_code: str) -> str:
        """
        验证和标准化股票代码
        
        Args:
            stock_code: 股票代码
            
        Returns:
            标准化的股票代码
            
        Raises:
            ValueError: 股票代码格式错误
        """
        if not stock_code or not isinstance(stock_code, str):
            raise ValueError("股票代码不能为空且必须为字符串")
        
        # 移除空格和转换为大写
        stock_code = stock_code.strip().upper()
        
        # 基本格式验证
        if len(stock_code) < 6:
            raise ValueError(f"股票代码格式错误: {stock_code}")
        
        return stock_code
    
    def _validate_date_range(
        self, 
        start_date: Optional[Union[str, date, datetime]], 
        end_date: Optional[Union[str, date, datetime]]
    ) -> tuple:
        """
        验证日期范围
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            验证后的日期元组
        """
        if start_date and end_date:
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date).date()
            elif isinstance(start_date, datetime):
                start_date = start_date.date()
                
            if isinstance(end_date, str):
                end_date = pd.to_datetime(end_date).date()
            elif isinstance(end_date, datetime):
                end_date = end_date.date()
            
            if start_date > end_date:
                raise ValueError("开始日期不能晚于结束日期")
        
        return start_date, end_date
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        stats = self.stats.copy()
        
        # 计算成功率
        if stats['requests_count'] > 0:
            stats['success_rate'] = stats['success_count'] / stats['requests_count']
            stats['error_rate'] = stats['error_count'] / stats['requests_count']
            stats['avg_request_time'] = stats['total_request_time'] / stats['requests_count']
        else:
            stats['success_rate'] = 0.0
            stats['error_rate'] = 0.0
            stats['avg_request_time'] = 0.0
        
        return stats
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.stats = {
            'requests_count': 0,
            'success_count': 0,
            'error_count': 0,
            'last_request_time': None,
            'total_request_time': 0.0
        }
        self.logger.info(f"已重置 {self.name} 的统计信息")
    
    def get_provider_info(self) -> Dict[str, Any]:
        """
        获取提供商信息
        
        Returns:
            提供商信息字典
        """
        return {
            'name': self.name,
            'class': self.__class__.__name__,
            'timeout': self.timeout,
            'max_retries': self.max_retries,
            'rate_limit': self.rate_limiter.interval if self.rate_limiter else None,
            'stats': self.get_stats()
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}(name='{self.name}')"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (
            f"{self.__class__.__name__}("
            f"name='{self.name}', "
            f"timeout={self.timeout}, "
            f"max_retries={self.max_retries}"
            f")"
        )

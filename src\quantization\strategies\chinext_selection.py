#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创业板选股策略

选股条件:
1. 创业板股票 (300开头)
2. 非ST股票
3. 流通市值: 15亿 - 300亿
4. WR值 > -20 (即大于80，因为WR通常为负值)
5. 大单净量 > 0.4
6. 量比 > 2 (倍量)

策略逻辑:
- 每日根据条件筛选股票
- 等权重持有筛选出的股票
- 每日调仓
"""

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Union, Any

from quantization.strategies.base import BaseStrategy
from quantization.core.data_manager import DataManager
from quantization.utils.stock_codes import StockCodeUtils
from quantization.utils.logger import get_logger


class ChiNextSelectionStrategy(BaseStrategy):
    """创业板选股策略"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化策略
        
        Args:
            config: 策略配置参数
        """
        super().__init__("创业板选股策略", config)
        
        # 数据管理器
        self.data_manager = None
        
        # 选股结果缓存
        self.selection_cache = {}
        
        # 策略特定的日志记录器
        self.strategy_logger = get_logger(f"Strategy.ChiNext")
    
    def _get_default_parameters(self) -> Dict[str, Any]:
        """
        获取默认参数
        
        Returns:
            默认参数字典
        """
        return {
            'max_stocks': 10,
            'min_market_cap': 15,      # 亿元
            'max_market_cap': 300,     # 亿元
            'wr_threshold': -20,       # WR > -20 (相当于80)
            'big_order_threshold': 0.4, # 大单净量 > 0.4
            'min_volume_ratio': 2.0,   # 量比 > 2
            'exclude_st': True,        # 排除ST股票
            'min_price': 1.0,          # 最低价格
            'max_price': 1000.0        # 最高价格
        }
    
    def _initialize_strategy(self) -> None:
        """
        策略特定的初始化逻辑
        """
        # 初始化数据管理器
        self.data_manager = DataManager()
        
        # 验证数据管理器连接
        if not self.data_manager.validate_connection():
            raise RuntimeError("数据管理器连接失败")
        
        self.strategy_logger.info("创业板选股策略初始化完成")
    
    def _execute_selection(self, date: Union[str, date, datetime]) -> List[str]:
        """
        执行选股逻辑
        
        Args:
            date: 选股日期
            
        Returns:
            选中的股票代码列表
        """
        # 标准化日期
        if isinstance(date, str):
            date = pd.to_datetime(date).date()
        elif isinstance(date, datetime):
            date = date.date()
        
        # 检查缓存
        cache_key = str(date)
        if cache_key in self.selection_cache:
            self.strategy_logger.debug(f"使用缓存的选股结果: {date}")
            return self.selection_cache[cache_key]
        
        try:
            # 获取创业板股票列表
            chinext_stocks = self._get_chinext_stocks()
            
            if not chinext_stocks:
                self.strategy_logger.warning("未获取到创业板股票列表")
                return []
            
            # 筛选股票
            selected_stocks = []
            
            for stock_code in chinext_stocks:
                try:
                    if self._meets_selection_criteria(stock_code, date):
                        selected_stocks.append(stock_code)
                        
                        # 限制选股数量
                        if len(selected_stocks) >= self.params['max_stocks']:
                            break
                            
                except Exception as e:
                    self.strategy_logger.debug(f"股票 {stock_code} 筛选失败: {str(e)}")
                    continue
            
            # 缓存结果
            self.selection_cache[cache_key] = selected_stocks
            
            self.strategy_logger.info(
                f"选股完成: 日期={date}, "
                f"候选股票={len(chinext_stocks)}, "
                f"选中股票={len(selected_stocks)}"
            )
            
            return selected_stocks
            
        except Exception as e:
            self.strategy_logger.error(f"选股执行失败: {str(e)}")
            raise
    
    def _get_chinext_stocks(self) -> List[str]:
        """
        获取创业板股票列表
        
        Returns:
            创业板股票代码列表
        """
        try:
            # 从数据管理器获取创业板股票
            stocks = self.data_manager.get_chinext_stocks()
            
            # 标准化股票代码
            normalized_stocks = []
            for stock in stocks:
                normalized = StockCodeUtils.normalize_code(stock)
                if normalized and StockCodeUtils.is_chinext(normalized):
                    normalized_stocks.append(normalized)
            
            return normalized_stocks
            
        except Exception as e:
            self.strategy_logger.error(f"获取创业板股票列表失败: {str(e)}")
            return []
    
    def _meets_selection_criteria(self, stock_code: str, date: date) -> bool:
        """
        检查股票是否满足选股条件
        
        Args:
            stock_code: 股票代码
            date: 检查日期
            
        Returns:
            是否满足条件
        """
        try:
            # 1. 检查是否为ST股票
            if self.params['exclude_st'] and self.data_manager.is_st_stock(stock_code):
                return False
            
            # 2. 检查市值
            market_cap = self.data_manager.get_market_cap(stock_code)
            if market_cap is None:
                return False
            
            market_cap_yi = market_cap  # 假设返回的已经是亿元单位
            if (market_cap_yi < self.params['min_market_cap'] or 
                market_cap_yi > self.params['max_market_cap']):
                return False
            
            # 3. 检查威廉指标
            wr_value = self.data_manager.get_williams_r(stock_code)
            if wr_value is None or wr_value <= self.params['wr_threshold']:
                return False
            
            # 4. 检查大单净量
            big_order_ratio = self.data_manager.get_big_order_net_ratio(stock_code)
            if big_order_ratio is None or big_order_ratio <= self.params['big_order_threshold']:
                return False
            
            # 5. 检查量比
            volume_ratio = self.data_manager.get_volume_ratio(stock_code)
            if volume_ratio is None or volume_ratio <= self.params['min_volume_ratio']:
                return False
            
            # 6. 检查价格范围（可选）
            stock_data = self.data_manager.get_stock_data(stock_code, date, date)
            if stock_data.empty:
                return False
            
            current_price = stock_data.iloc[-1]['close']
            if (current_price < self.params['min_price'] or 
                current_price > self.params['max_price']):
                return False
            
            return True
            
        except Exception as e:
            self.strategy_logger.debug(f"检查股票 {stock_code} 条件时出错: {str(e)}")
            return False
    
    def get_selection_summary(self, date: Union[str, date, datetime]) -> Dict[str, Any]:
        """
        获取选股摘要信息
        
        Args:
            date: 选股日期
            
        Returns:
            选股摘要字典
        """
        selected_stocks = self.select_stocks(date)
        
        summary = {
            'date': str(date),
            'selected_count': len(selected_stocks),
            'selected_stocks': selected_stocks,
            'criteria': self.params.copy(),
            'timestamp': datetime.now()
        }
        
        # 获取选中股票的详细信息
        if selected_stocks:
            stock_details = []
            for stock_code in selected_stocks:
                try:
                    details = {
                        'code': stock_code,
                        'market_cap': self.data_manager.get_market_cap(stock_code),
                        'williams_r': self.data_manager.get_williams_r(stock_code),
                        'big_order_ratio': self.data_manager.get_big_order_net_ratio(stock_code),
                        'volume_ratio': self.data_manager.get_volume_ratio(stock_code)
                    }
                    stock_details.append(details)
                except Exception as e:
                    self.strategy_logger.debug(f"获取股票 {stock_code} 详情失败: {str(e)}")
            
            summary['stock_details'] = stock_details
        
        return summary
    
    def clear_cache(self) -> None:
        """清空选股缓存"""
        self.selection_cache.clear()
        self.strategy_logger.info("已清空选股缓存")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息
        
        Returns:
            缓存信息字典
        """
        return {
            'cache_size': len(self.selection_cache),
            'cached_dates': list(self.selection_cache.keys()),
            'memory_usage': sum(len(stocks) for stocks in self.selection_cache.values())
        }

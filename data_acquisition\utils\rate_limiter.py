"""
Rate limiting utilities for API calls and web scraping
"""

import time
import asyncio
from typing import Dict, Optional, Callable, Any
from threading import Lock
from collections import defaultdict, deque
from functools import wraps
from ..config.settings import Config

class RateLimiter:
    """Thread-safe rate limiter for API calls"""
    
    def __init__(self, calls_per_second: float = 1.0, burst_size: int = 1):
        """
        Initialize rate limiter
        
        Args:
            calls_per_second: Maximum calls per second
            burst_size: Maximum burst size
        """
        self.calls_per_second = calls_per_second
        self.burst_size = burst_size
        self.min_interval = 1.0 / calls_per_second if calls_per_second > 0 else 0
        self.last_call_time = 0.0
        self.call_times = deque()
        self.lock = Lock()
    
    def wait_if_needed(self):
        """Wait if rate limit would be exceeded"""
        with self.lock:
            current_time = time.time()
            
            # Remove old call times outside the window
            window_start = current_time - 1.0
            while self.call_times and self.call_times[0] < window_start:
                self.call_times.popleft()
            
            # Check if we need to wait
            if len(self.call_times) >= self.burst_size:
                # Wait until we can make another call
                sleep_time = self.call_times[0] + 1.0 - current_time
                if sleep_time > 0:
                    time.sleep(sleep_time)
                    current_time = time.time()
            
            # Ensure minimum interval between calls
            time_since_last = current_time - self.last_call_time
            if time_since_last < self.min_interval:
                sleep_time = self.min_interval - time_since_last
                time.sleep(sleep_time)
                current_time = time.time()
            
            # Record this call
            self.call_times.append(current_time)
            self.last_call_time = current_time

class AsyncRateLimiter:
    """Async version of rate limiter"""
    
    def __init__(self, calls_per_second: float = 1.0, burst_size: int = 1):
        self.calls_per_second = calls_per_second
        self.burst_size = burst_size
        self.min_interval = 1.0 / calls_per_second if calls_per_second > 0 else 0
        self.last_call_time = 0.0
        self.call_times = deque()
        self.lock = asyncio.Lock()
    
    async def wait_if_needed(self):
        """Async wait if rate limit would be exceeded"""
        async with self.lock:
            current_time = time.time()
            
            # Remove old call times
            window_start = current_time - 1.0
            while self.call_times and self.call_times[0] < window_start:
                self.call_times.popleft()
            
            # Check burst limit
            if len(self.call_times) >= self.burst_size:
                sleep_time = self.call_times[0] + 1.0 - current_time
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                    current_time = time.time()
            
            # Ensure minimum interval
            time_since_last = current_time - self.last_call_time
            if time_since_last < self.min_interval:
                sleep_time = self.min_interval - time_since_last
                await asyncio.sleep(sleep_time)
                current_time = time.time()
            
            # Record call
            self.call_times.append(current_time)
            self.last_call_time = current_time

class MultiSourceRateLimiter:
    """Rate limiter that manages multiple sources with different limits"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize multi-source rate limiter
        
        Args:
            config: Configuration instance
        """
        config = config or Config()
        rate_config = config.get_rate_limit_config()
        
        self.limiters = {
            'akshare': RateLimiter(1.0 / rate_config['akshare_limit']),
            'web_scraper': RateLimiter(1.0 / rate_config['web_scraper_limit']),
            'tencent': RateLimiter(0.5),  # 2 seconds between calls
            'eastmoney': RateLimiter(0.5),  # 2 seconds between calls
        }
    
    def wait_for_source(self, source: str):
        """
        Wait for a specific source if needed
        
        Args:
            source: Source name ('akshare', 'web_scraper', etc.)
        """
        if source in self.limiters:
            self.limiters[source].wait_if_needed()
    
    def add_source(self, source: str, calls_per_second: float, burst_size: int = 1):
        """
        Add a new source with rate limiting
        
        Args:
            source: Source name
            calls_per_second: Rate limit
            burst_size: Burst size
        """
        self.limiters[source] = RateLimiter(calls_per_second, burst_size)

def rate_limited(source: str, limiter: Optional[MultiSourceRateLimiter] = None):
    """
    Decorator for rate limiting function calls
    
    Args:
        source: Source name for rate limiting
        limiter: Rate limiter instance (creates default if None)
        
    Returns:
        Decorated function
    """
    if limiter is None:
        limiter = MultiSourceRateLimiter()
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            limiter.wait_for_source(source)
            return func(*args, **kwargs)
        return wrapper
    return decorator

def async_rate_limited(source: str, calls_per_second: float = 1.0):
    """
    Async decorator for rate limiting
    
    Args:
        source: Source name
        calls_per_second: Rate limit
        
    Returns:
        Decorated async function
    """
    limiter = AsyncRateLimiter(calls_per_second)
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            await limiter.wait_if_needed()
            return await func(*args, **kwargs)
        return wrapper
    return decorator

class RetryWithBackoff:
    """Retry mechanism with exponential backoff"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, backoff_factor: float = 2.0):
        """
        Initialize retry mechanism
        
        Args:
            max_retries: Maximum number of retries
            base_delay: Base delay in seconds
            max_delay: Maximum delay in seconds
            backoff_factor: Backoff multiplier
        """
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
    
    def __call__(self, func: Callable) -> Callable:
        """Make the class callable as a decorator"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(self.max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt == self.max_retries:
                        # Last attempt failed, raise the exception
                        raise last_exception
                    
                    # Calculate delay with exponential backoff
                    delay = min(
                        self.base_delay * (self.backoff_factor ** attempt),
                        self.max_delay
                    )
                    
                    time.sleep(delay)
            
            # This should never be reached, but just in case
            raise last_exception
        
        return wrapper

# Convenience function to create retry decorator with config
def retry_with_config(config: Optional[Config] = None):
    """
    Create retry decorator using configuration
    
    Args:
        config: Configuration instance
        
    Returns:
        RetryWithBackoff: Configured retry decorator
    """
    config = config or Config()
    rate_config = config.get_rate_limit_config()
    
    return RetryWithBackoff(
        max_retries=rate_config['max_retries'],
        base_delay=rate_config['retry_delay']
    )

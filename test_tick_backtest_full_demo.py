#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创业板动态因子策略TICK回测完整演示
使用宽松条件展示完整的策略执行流程
"""

import sys
import os
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.quantization.strategies.chinext_dynamic_factor_strategy import ChiNextDynamicFactorStrategy
from src.quantization.strategies.tick_backtester import TickBacktester
from src.quantization.strategies.chinext_minute_data_downloader import ChinextMinuteDataDownloader

def demo_strategy_with_relaxed_conditions():
    """演示策略执行（使用宽松条件）"""
    print("=" * 60)
    print("创业板动态因子策略完整演示")
    print("=" * 60)
    
    # 使用非常宽松的条件确保能产生交易信号
    strategy_config = {
        'max_stocks': 5,
        'position_size': 100000,
        'market_cap_min': 1,      # 降低到1亿
        'market_cap_max': 5000,   # 提高到5000亿
        'volume_ratio_min': 0.5,  # 降低量比要求
        'dde_net_ratio_min': 0.1, # 降低大单净量要求
        'turnover_rate_min': 0.5, # 降低换手率要求
        'limit_up_days_min': 0,   # 不要求涨停
        'market_volume_threshold': 100000000000.0,  # 降低到1000亿
    }
    
    print("策略配置（宽松条件）:")
    for key, value in strategy_config.items():
        print(f"  {key}: {value}")
    print()
    
    # 创建策略实例
    strategy = ChiNextDynamicFactorStrategy(config=strategy_config)
    
    # 测试多个日期
    test_dates = [
        (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d') 
        for i in range(1, 6)
    ]
    
    print(f"测试日期: {test_dates}")
    print()
    
    # 逐日测试信号生成
    all_signals = []
    for test_date in test_dates:
        print(f"测试日期: {test_date}")
        signals = strategy.generate_signals(test_date)
        
        buy_signals = signals.get('buy_signals', [])
        sell_signals = signals.get('sell_signals', [])
        market_enabled = signals.get('market_trading_enabled', False)
        market_volume = signals.get('market_volume', 0)
        
        print(f"  市场成交额: {market_volume:.2f}万亿")
        print(f"  交易条件: {'满足' if market_enabled else '不满足'}")
        print(f"  买入信号: {len(buy_signals)}只")
        print(f"  卖出信号: {len(sell_signals)}只")
        
        if buy_signals:
            print("  买入标的:")
            for signal in buy_signals[:3]:  # 显示前3只
                print(f"    {signal['stock_code']}: {signal['quantity']}股")
        
        all_signals.append(signals)
        print()
    
    return strategy, all_signals

def demo_tick_backtester_with_mock_data():
    """演示TICK回测（使用模拟数据）"""
    print("=" * 60)
    print("TICK回测引擎演示（模拟数据）")
    print("=" * 60)
    
    # 创建回测引擎
    backtester = TickBacktester(
        initial_capital=1000000,
        commission_rate=0.0003,
        slippage=0.001,
        db_path="data/chinext_minute_data.db"
    )
    
    # 创建策略（宽松条件）
    strategy_config = {
        'max_stocks': 3,
        'position_size': 100000,
        'market_cap_min': 1,
        'market_cap_max': 5000,
        'volume_ratio_min': 0.5,
        'dde_net_ratio_min': 0.1,
        'turnover_rate_min': 0.5,
        'limit_up_days_min': 0,
        'market_volume_threshold': 100000000000.0,
    }
    
    strategy = ChiNextDynamicFactorStrategy(config=strategy_config)
    
    # 回测参数
    end_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
    
    # 使用有数据的股票池
    stock_universe = [
        "300015.SZ", "300059.SZ", "300750.SZ"
    ]
    
    print(f"回测期间: {start_date} 至 {end_date}")
    print(f"股票池: {stock_universe}")
    print(f"初始资金: {backtester.initial_capital:,.0f}元")
    print()
    
    # 运行回测
    print("开始运行TICK回测...")
    start_time = time.time()
    
    try:
        backtest_result = backtester.run_backtest(
            strategy=strategy,
            start_date=start_date,
            end_date=end_date,
            stock_universe=stock_universe
        )
        
        backtest_time = time.time() - start_time
        
        print(f"回测完成，耗时: {backtest_time:.2f}秒")
        
        # 显示详细结果
        print(f"\n📊 回测结果详情:")
        print(f"  总收益率: {backtest_result.total_return:.2%}")
        print(f"  年化收益率: {backtest_result.annual_return:.2%}")
        print(f"  最大回撤: {backtest_result.max_drawdown:.2%}")
        print(f"  夏普比率: {backtest_result.sharpe_ratio:.3f}")
        print(f"  胜率: {backtest_result.win_rate:.1%}")
        print(f"  总交易次数: {backtest_result.total_trades}")
        print(f"  盈利交易: {backtest_result.profit_trades}")
        print(f"  亏损交易: {backtest_result.loss_trades}")
        print(f"  平均盈利: {backtest_result.avg_profit:.2f}元")
        print(f"  平均亏损: {backtest_result.avg_loss:.2f}元")
        
        # 资金曲线分析
        if not backtest_result.equity_curve.empty:
            equity_curve = backtest_result.equity_curve
            final_value = equity_curve['total_value'].iloc[-1]
            max_value = equity_curve['total_value'].max()
            min_value = equity_curve['total_value'].min()
            
            print(f"\n💰 资金曲线分析:")
            print(f"  起始资产: {backtester.initial_capital:,.2f}元")
            print(f"  最终资产: {final_value:,.2f}元")
            print(f"  最高资产: {max_value:,.2f}元")
            print(f"  最低资产: {min_value:,.2f}元")
            print(f"  盈亏金额: {final_value - backtester.initial_capital:,.2f}元")
        
        # 交易分析
        if backtest_result.orders:
            print(f"\n📈 交易分析:")
            buy_orders = [o for o in backtest_result.orders if o.order_type.value == 'buy']
            sell_orders = [o for o in backtest_result.orders if o.order_type.value == 'sell']
            
            print(f"  买入订单: {len(buy_orders)}笔")
            print(f"  卖出订单: {len(sell_orders)}笔")
            
            if buy_orders:
                total_buy_amount = sum(o.filled_quantity * o.filled_price for o in buy_orders)
                print(f"  总买入金额: {total_buy_amount:,.2f}元")
            
            if sell_orders:
                total_sell_amount = sum(o.filled_quantity * o.filled_price for o in sell_orders)
                print(f"  总卖出金额: {total_sell_amount:,.2f}元")
        
        # 持仓分析
        if backtest_result.positions:
            print(f"\n📋 持仓分析:")
            print(f"  持仓股票数: {len(backtest_result.positions)}")
            
            for i, pos in enumerate(backtest_result.positions[:5]):
                pnl_pct = (pos.unrealized_pnl / (pos.quantity * pos.avg_price)) * 100 if pos.avg_price > 0 else 0
                print(f"  {i+1}. {pos.stock_code}: {pos.quantity}股")
                print(f"     平均成本: {pos.avg_price:.2f}元")
                print(f"     未实现盈亏: {pos.unrealized_pnl:.2f}元 ({pnl_pct:.2f}%)")
        
        return backtest_result
        
    except Exception as e:
        print(f"回测过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def demo_performance_analysis():
    """演示性能分析"""
    print("=" * 60)
    print("系统性能分析演示")
    print("=" * 60)
    
    # 创建数据下载器
    downloader = ChinextMinuteDataDownloader()
    
    # 测试性能监控
    test_stocks = ["300015.SZ", "300059.SZ"]
    test_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    
    print(f"性能测试: {test_stocks} @ {test_date}")
    
    # 清空缓存测试无缓存性能
    downloader.clear_cache()
    
    start_time = time.time()
    for stock in test_stocks:
        success, count, msg = downloader.download_stock_minute_data(stock, test_date)
        print(f"  {stock}: {'成功' if success else '失败'} ({count}条记录)")
    
    no_cache_time = time.time() - start_time
    
    # 测试缓存性能
    start_time = time.time()
    for stock in test_stocks:
        success, count, msg = downloader.download_stock_minute_data(stock, test_date)
        print(f"  {stock}: {'成功' if success else '失败'} ({msg})")
    
    cache_time = time.time() - start_time
    
    # 性能对比
    if cache_time > 0:
        speedup = no_cache_time / cache_time
        time_saved = ((no_cache_time - cache_time) / no_cache_time) * 100
        
        print(f"\n⚡ 性能对比:")
        print(f"  无缓存耗时: {no_cache_time:.3f}秒")
        print(f"  缓存耗时: {cache_time:.3f}秒")
        print(f"  性能提升: {speedup:.1f}倍")
        print(f"  时间节省: {time_saved:.1f}%")
    
    # 显示性能统计
    print(f"\n📊 系统性能统计:")
    downloader.print_performance_report()

def main():
    """主演示函数"""
    print("🚀 创业板动态因子策略TICK回测系统完整演示")
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # 1. 策略信号生成演示
        strategy, signals = demo_strategy_with_relaxed_conditions()
        print()
        
        # 2. TICK回测引擎演示
        backtest_result = demo_tick_backtester_with_mock_data()
        print()
        
        # 3. 性能分析演示
        demo_performance_analysis()
        print()
        
        # 总结
        print("=" * 60)
        print("🎯 演示总结")
        print("=" * 60)
        
        total_buy_signals = sum(len(s.get('buy_signals', [])) for s in signals)
        total_sell_signals = sum(len(s.get('sell_signals', [])) for s in signals)
        
        print(f"✅ 策略信号生成: {total_buy_signals} 买入, {total_sell_signals} 卖出")
        print(f"✅ TICK回测执行: {'成功' if backtest_result else '失败'}")
        
        if backtest_result:
            print(f"✅ 回测收益率: {backtest_result.total_return:.2%}")
            print(f"✅ 最大回撤: {backtest_result.max_drawdown:.2%}")
            print(f"✅ 交易次数: {backtest_result.total_trades}")
            print(f"✅ 胜率: {backtest_result.win_rate:.1%}")
        
        print(f"✅ 系统性能: 缓存优化显著提升数据访问速度")
        
        print("\n🎉 创业板动态因子策略TICK回测系统演示完成！")
        print("\n系统特点:")
        print("  📈 多因子选股模型")
        print("  ⚡ 高性能TICK数据处理")
        print("  🎯 精确交易时点控制")
        print("  📊 详细回测分析报告")
        print("  🚀 智能缓存优化")
        print("  🔍 实时性能监控")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户体验和接口优化功能测试

简化版本的功能测试，验证各个模块是否正常工作。
"""

import sys
import os
from pathlib import Path
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def test_basic_imports():
    """测试基本模块导入"""
    print("=" * 50)
    print("测试模块导入")
    print("=" * 50)
    
    # 测试工具模块
    try:
        from quantization.utils.logger import get_logger
        print("✓ 日志模块导入成功")
    except ImportError as e:
        print(f"✗ 日志模块导入失败: {e}")
    
    # 测试配置模块
    try:
        from quantization.config.config_manager import ConfigManager
        print("✓ 配置管理模块导入成功")
    except ImportError as e:
        print(f"✗ 配置管理模块导入失败: {e}")
    
    # 测试可视化模块
    try:
        from quantization.visualization.result_visualizer import ResultVisualizer
        print("✓ 可视化模块导入成功")
    except ImportError as e:
        print(f"✗ 可视化模块导入失败: {e}")
    
    # 测试API模块
    try:
        from quantization.api.api_server import QuantizationAPI
        print("✓ API模块导入成功")
    except ImportError as e:
        print(f"✗ API模块导入失败: {e}")
    
    # 测试UI模块
    try:
        from quantization.ui.dashboard import QuantizationDashboard
        print("✓ UI模块导入成功")
    except ImportError as e:
        print(f"✗ UI模块导入失败: {e}")

def test_config_manager():
    """测试配置管理器"""
    print("\n" + "=" * 50)
    print("测试配置管理器")
    print("=" * 50)
    
    try:
        from quantization.config.config_manager import ConfigManager
        
        # 创建配置管理器实例
        config_manager = ConfigManager()
        
        # 测试基本操作
        config_manager.set("test.value", 42)
        value = config_manager.get("test.value")
        print(f"✓ 配置设置和获取: {value}")
        
        # 测试嵌套配置
        config_manager.set("test.nested.key", "nested_value")
        nested_value = config_manager.get("test.nested.key")
        print(f"✓ 嵌套配置: {nested_value}")
        
        # 测试默认值
        default_value = config_manager.get("test.nonexistent", "default")
        print(f"✓ 默认值处理: {default_value}")
        
        print("✓ 配置管理器测试通过")
        
    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")

def test_logger():
    """测试日志系统"""
    print("\n" + "=" * 50)
    print("测试日志系统")
    print("=" * 50)
    
    try:
        from quantization.utils.logger import get_logger
        
        # 创建日志器
        logger = get_logger("TestLogger")
        
        # 测试不同级别的日志
        logger.info("这是一条信息日志")
        logger.warning("这是一条警告日志")
        logger.error("这是一条错误日志")
        
        print("✓ 日志系统测试通过")
        
    except Exception as e:
        print(f"✗ 日志系统测试失败: {e}")

def test_visualization_config():
    """测试可视化配置"""
    print("\n" + "=" * 50)
    print("测试可视化配置")
    print("=" * 50)
    
    try:
        from quantization.visualization.result_visualizer import VisualizationConfig
        
        # 创建可视化配置
        config = VisualizationConfig(
            style="seaborn",
            figure_size=(12, 8),
            interactive=True
        )
        
        print(f"✓ 可视化配置创建成功: {config.style}, {config.figure_size}")
        
    except Exception as e:
        print(f"✗ 可视化配置测试失败: {e}")

def create_test_config_files():
    """创建测试配置文件"""
    print("\n" + "=" * 50)
    print("创建测试配置文件")
    print("=" * 50)
    
    try:
        # 创建配置目录
        config_dir = Path("config")
        config_dir.mkdir(exist_ok=True)
        
        # 测试配置
        test_config = {
            "system": {
                "name": "量化交易系统",
                "version": "1.0.0",
                "debug": True
            },
            "data": {
                "cache_enabled": True,
                "timeout": 30
            },
            "api": {
                "host": "localhost",
                "port": 8000
            }
        }
        
        # 保存配置文件
        config_file = config_dir / "test_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 测试配置文件已创建: {config_file}")
        
        # 验证文件内容
        with open(config_file, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        print(f"✓ 配置文件验证成功: {loaded_config['system']['name']}")
        
    except Exception as e:
        print(f"✗ 配置文件创建失败: {e}")

def test_output_directories():
    """测试输出目录创建"""
    print("\n" + "=" * 50)
    print("测试输出目录创建")
    print("=" * 50)
    
    try:
        # 创建输出目录
        directories = [
            "output/charts",
            "output/reports", 
            "output/config",
            "output/logs"
        ]
        
        for dir_path in directories:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            print(f"✓ 目录已创建: {dir_path}")
        
        print("✓ 所有输出目录创建成功")
        
    except Exception as e:
        print(f"✗ 输出目录创建失败: {e}")

def test_dependencies():
    """测试可选依赖"""
    print("\n" + "=" * 50)
    print("测试可选依赖")
    print("=" * 50)
    
    dependencies = [
        ("pandas", "数据处理"),
        ("numpy", "数值计算"),
        ("matplotlib", "基础绘图"),
        ("plotly", "交互式图表"),
        ("fastapi", "API框架"),
        ("streamlit", "Web界面"),
        ("pyyaml", "YAML配置"),
        ("toml", "TOML配置")
    ]
    
    for module_name, description in dependencies:
        try:
            __import__(module_name)
            print(f"✓ {description} ({module_name}) 可用")
        except ImportError:
            print(f"✗ {description} ({module_name}) 不可用")

def main():
    """主测试函数"""
    print("用户体验和接口优化功能测试")
    print("=" * 60)
    
    # 基本导入测试
    test_basic_imports()
    
    # 配置管理器测试
    test_config_manager()
    
    # 日志系统测试
    test_logger()
    
    # 可视化配置测试
    test_visualization_config()
    
    # 创建测试文件
    create_test_config_files()
    
    # 输出目录测试
    test_output_directories()
    
    # 依赖测试
    test_dependencies()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n使用说明:")
    print("1. 如果所有模块导入成功，说明基础架构正常")
    print("2. 配置文件已创建在 config/ 目录下")
    print("3. 输出目录已创建在 output/ 目录下")
    print("4. 可以运行完整的示例程序进行进一步测试")
    
    print("\n下一步:")
    print("1. 安装缺失的依赖包")
    print("2. 运行 python examples/ui_optimization_example.py")
    print("3. 启动API服务器: python -c \"from quantization.api import api_server; api_server.run()\"")
    print("4. 启动Web界面: streamlit run examples/ui_optimization_example.py")

if __name__ == "__main__":
    main()

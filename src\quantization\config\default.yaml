# 量化交易系统默认配置文件

# 数据源配置
data_sources:
  # Akshare数据源
  akshare:
    enabled: true
    rate_limit: 0.5  # 请求间隔（秒）
    timeout: 30      # 超时时间（秒）
    max_retries: 3   # 最大重试次数
    
  # 网络爬虫数据源
  web_scraper:
    enabled: true
    rate_limit: 1.0  # 请求间隔（秒）
    timeout: 30      # 超时时间（秒）
    max_retries: 3   # 最大重试次数
    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

# 数据库配置
database:
  # SQLite配置
  sqlite:
    enabled: true
    path: "data/quantization.db"
    
  # MySQL配置（可选）
  mysql:
    enabled: false
    host: "localhost"
    port: 3306
    database: "quantization"
    username: ""
    password: ""
    
# 缓存配置
cache:
  # 内存缓存
  memory:
    enabled: true
    max_size: 1000  # 最大缓存条目数
    ttl: 3600       # 生存时间（秒）
    
  # Redis缓存（可选）
  redis:
    enabled: false
    host: "localhost"
    port: 6379
    db: 0
    password: ""

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 文件日志
  file:
    enabled: true
    path: "logs/quantization.log"
    max_size: "10MB"
    backup_count: 5
    
  # 控制台日志
  console:
    enabled: true
    level: "INFO"

# 策略配置
strategies:
  # 创业板选股策略
  chinext_selection:
    enabled: true
    max_stocks: 10
    min_market_cap: 15      # 亿元
    max_market_cap: 300     # 亿元
    wr_threshold: -20       # 威廉指标阈值
    big_order_threshold: 0.4 # 大单净量阈值
    min_volume_ratio: 2.0   # 最小量比
    exclude_st: true        # 排除ST股票
    min_price: 1.0          # 最低价格
    max_price: 1000.0       # 最高价格

# 回测配置
backtesting:
  # 基本设置
  initial_capital: 1000000  # 初始资金（元）
  commission_rate: 0.0003   # 手续费率
  slippage_rate: 0.001      # 滑点率
  
  # 风险控制
  max_position_size: 0.1    # 单只股票最大仓位
  stop_loss: -0.1           # 止损比例
  take_profit: 0.2          # 止盈比例
  
  # 性能设置
  parallel_workers: 4       # 并行工作进程数
  batch_size: 100          # 批处理大小
  
# 监控配置
monitoring:
  # 性能监控
  performance:
    enabled: true
    metrics_interval: 60    # 指标收集间隔（秒）
    
  # 错误监控
  error:
    enabled: true
    alert_threshold: 10     # 错误告警阈值
    
# 系统配置
system:
  # 多进程设置
  multiprocessing:
    enabled: true
    max_workers: 4
    
  # 内存管理
  memory:
    max_usage: "2GB"        # 最大内存使用
    gc_threshold: 0.8       # 垃圾回收阈值
    
  # 网络设置
  network:
    timeout: 30             # 网络超时
    max_connections: 100    # 最大连接数

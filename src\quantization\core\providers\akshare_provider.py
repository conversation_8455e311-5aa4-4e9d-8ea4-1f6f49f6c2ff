#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Akshare数据提供商

基于akshare库的数据获取实现。
"""

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Union, Any

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False

from quantization.core.providers.base import BaseProvider
from quantization.utils.stock_codes import StockCodeUtils


class AkshareProvider(BaseProvider):
    """
    Akshare数据提供商
    
    使用akshare库获取股票数据。
    """
    
    def __init__(
        self, 
        rate_limit: Optional[float] = 0.5,
        timeout: float = 30.0,
        max_retries: int = 3
    ):
        """
        初始化Akshare提供商
        
        Args:
            rate_limit: 请求频率限制（秒）
            timeout: 请求超时时间
            max_retries: 最大重试次数
        """
        if not AKSHARE_AVAILABLE:
            raise ImportError("akshare库未安装，请运行: pip install akshare")
        
        super().__init__("Akshare", rate_limit, timeout, max_retries)
        
        # 缓存
        self._stock_info_cache = {}
        self._chinext_stocks_cache = None
        self._cache_expire_time = {}
    
    def get_stock_data(
        self, 
        stock_code: str, 
        start_date: Optional[Union[str, date, datetime]] = None,
        end_date: Optional[Union[str, date, datetime]] = None,
        fields: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        获取股票基础数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            fields: 需要的字段列表
            
        Returns:
            股票数据DataFrame
        """
        stock_code = self._validate_stock_code(stock_code)
        start_date, end_date = self._validate_date_range(start_date, end_date)
        
        def _fetch_data():
            # 使用akshare获取股票数据
            symbol = StockCodeUtils.add_suffix(stock_code)
            
            # 获取历史数据
            df = ak.stock_zh_a_hist(
                symbol=symbol,
                period="daily",
                start_date=start_date.strftime('%Y%m%d') if start_date else None,
                end_date=end_date.strftime('%Y%m%d') if end_date else None,
                adjust="qfq"  # 前复权
            )
            
            if df.empty:
                return pd.DataFrame()
            
            # 标准化列名
            column_mapping = {
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '涨跌幅': 'pct_change',
                '涨跌额': 'change',
                '换手率': 'turnover'
            }
            
            df = df.rename(columns=column_mapping)
            
            # 确保日期列为datetime类型
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
                df = df.set_index('date')
            
            # 筛选字段
            if fields:
                available_fields = [f for f in fields if f in df.columns]
                df = df[available_fields]
            
            return df
        
        return self._make_request(_fetch_data)
    
    def get_stock_info(self, stock_code: str) -> Dict[str, Any]:
        """
        获取股票基本信息
        
        Args:
            stock_code: 股票代码
            
        Returns:
            股票基本信息字典
        """
        stock_code = self._validate_stock_code(stock_code)
        
        # 检查缓存
        if stock_code in self._stock_info_cache:
            cache_time = self._cache_expire_time.get(stock_code, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=1):
                return self._stock_info_cache[stock_code]
        
        def _fetch_info():
            try:
                # 获取股票基本信息
                symbol = StockCodeUtils.add_suffix(stock_code)
                
                # 获取实时数据
                df = ak.stock_zh_a_spot_em()
                stock_info = df[df['代码'] == symbol]
                
                if stock_info.empty:
                    return {}
                
                info = stock_info.iloc[0].to_dict()
                
                # 标准化字段名
                standardized_info = {
                    'code': stock_code,
                    'name': info.get('名称', ''),
                    'price': info.get('最新价', 0),
                    'change': info.get('涨跌额', 0),
                    'pct_change': info.get('涨跌幅', 0),
                    'volume': info.get('成交量', 0),
                    'amount': info.get('成交额', 0),
                    'market_cap': info.get('总市值', 0),
                    'circulation_market_cap': info.get('流通市值', 0),
                    'pe_ratio': info.get('市盈率-动态', 0),
                    'pb_ratio': info.get('市净率', 0)
                }
                
                # 缓存结果
                self._stock_info_cache[stock_code] = standardized_info
                self._cache_expire_time[stock_code] = datetime.now()
                
                return standardized_info
                
            except Exception as e:
                self.logger.warning(f"获取股票 {stock_code} 基本信息失败: {str(e)}")
                return {}
        
        return self._make_request(_fetch_info)
    
    def get_market_cap(self, stock_code: str) -> Optional[float]:
        """
        获取股票市值
        
        Args:
            stock_code: 股票代码
            
        Returns:
            市值（亿元）
        """
        try:
            stock_info = self.get_stock_info(stock_code)
            
            # 优先使用流通市值
            market_cap = stock_info.get('circulation_market_cap', 0)
            if not market_cap:
                market_cap = stock_info.get('market_cap', 0)
            
            if market_cap and market_cap > 0:
                # 转换为亿元
                return market_cap / 1e8
            
            return None
            
        except Exception as e:
            self.logger.debug(f"获取股票 {stock_code} 市值失败: {str(e)}")
            return None
    
    def get_big_order_net_ratio(self, stock_code: str) -> Optional[float]:
        """
        获取大单净量比
        
        Args:
            stock_code: 股票代码
            
        Returns:
            大单净量比
        """
        def _calculate_big_order_ratio():
            try:
                # 获取资金流向数据
                symbol = StockCodeUtils.add_suffix(stock_code)
                
                # 获取个股资金流向
                df = ak.stock_individual_fund_flow(stock=symbol, market="沪深A股")
                
                if df.empty:
                    return None
                
                # 获取最新数据
                latest_data = df.iloc[-1]
                
                # 计算大单净量比
                big_order_in = latest_data.get('大单净流入-净额', 0)
                big_order_out = latest_data.get('大单净流出-净额', 0)
                total_amount = latest_data.get('成交额', 1)  # 避免除零
                
                if total_amount > 0:
                    net_ratio = (big_order_in - big_order_out) / total_amount
                    return net_ratio
                
                return None
                
            except Exception as e:
                # 使用技术分析方法估算
                return self._estimate_big_order_ratio_technical(stock_code)
        
        return self._make_request(_calculate_big_order_ratio)
    
    def _estimate_big_order_ratio_technical(self, stock_code: str) -> Optional[float]:
        """
        使用技术分析方法估算大单净量比
        
        Args:
            stock_code: 股票代码
            
        Returns:
            估算的大单净量比
        """
        try:
            # 获取最近几天的数据
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=5)
            
            df = self.get_stock_data(stock_code, start_date, end_date)
            
            if df.empty or len(df) < 2:
                return None
            
            # 基于价量关系估算
            latest = df.iloc[-1]
            previous = df.iloc[-2]
            
            price_change = (latest['close'] - previous['close']) / previous['close']
            volume_ratio = latest['volume'] / previous['volume'] if previous['volume'] > 0 else 1
            
            # 简单的估算公式
            if price_change > 0 and volume_ratio > 1.5:
                # 价涨量增，可能有大单流入
                estimated_ratio = min(0.8, price_change * volume_ratio * 0.3)
            elif price_change < 0 and volume_ratio > 1.5:
                # 价跌量增，可能有大单流出
                estimated_ratio = max(-0.8, price_change * volume_ratio * 0.3)
            else:
                # 其他情况
                estimated_ratio = price_change * 0.1
            
            return estimated_ratio
            
        except Exception as e:
            self.logger.debug(f"技术分析估算大单净量比失败: {str(e)}")
            return None
    
    def get_williams_r(self, stock_code: str, period: int = 14) -> Optional[float]:
        """
        获取威廉指标
        
        Args:
            stock_code: 股票代码
            period: 计算周期
            
        Returns:
            威廉指标值
        """
        def _calculate_williams_r():
            try:
                # 获取足够的历史数据
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=period * 2)
                
                df = self.get_stock_data(stock_code, start_date, end_date)
                
                if df.empty or len(df) < period:
                    return None
                
                # 计算威廉指标
                high_max = df['high'].rolling(window=period).max()
                low_min = df['low'].rolling(window=period).min()
                close = df['close']
                
                wr = -100 * (high_max - close) / (high_max - low_min)
                
                return wr.iloc[-1] if not pd.isna(wr.iloc[-1]) else None
                
            except Exception as e:
                self.logger.debug(f"计算威廉指标失败: {str(e)}")
                return None
        
        return self._make_request(_calculate_williams_r)
    
    def get_volume_ratio(self, stock_code: str) -> Optional[float]:
        """
        获取量比
        
        Args:
            stock_code: 股票代码
            
        Returns:
            量比值
        """
        def _calculate_volume_ratio():
            try:
                # 获取最近的数据
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=10)
                
                df = self.get_stock_data(stock_code, start_date, end_date)
                
                if df.empty or len(df) < 5:
                    return None
                
                # 计算量比（当日成交量/过去5日平均成交量）
                current_volume = df['volume'].iloc[-1]
                avg_volume = df['volume'].iloc[-6:-1].mean()  # 过去5日平均
                
                if avg_volume > 0:
                    volume_ratio = current_volume / avg_volume
                    return volume_ratio
                
                return None
                
            except Exception as e:
                self.logger.debug(f"计算量比失败: {str(e)}")
                return None
        
        return self._make_request(_calculate_volume_ratio)

    def get_chinext_stocks(self) -> List[str]:
        """
        获取创业板股票列表

        Returns:
            创业板股票代码列表
        """
        # 检查缓存
        if (self._chinext_stocks_cache is not None and
            'chinext_stocks' in self._cache_expire_time):
            cache_time = self._cache_expire_time['chinext_stocks']
            if datetime.now() - cache_time < timedelta(hours=24):
                return self._chinext_stocks_cache

        def _fetch_chinext_stocks():
            try:
                # 获取创业板股票列表
                df = ak.stock_zh_a_spot_em()

                if df.empty:
                    return []

                # 筛选创业板股票（代码以300开头）
                chinext_stocks = []
                for _, row in df.iterrows():
                    code = row.get('代码', '')
                    if code.startswith('300'):
                        # 移除后缀，只保留6位数字代码
                        clean_code = code.split('.')[0]
                        if len(clean_code) == 6 and clean_code.isdigit():
                            chinext_stocks.append(clean_code)

                # 缓存结果
                self._chinext_stocks_cache = chinext_stocks
                self._cache_expire_time['chinext_stocks'] = datetime.now()

                return chinext_stocks

            except Exception as e:
                self.logger.warning(f"获取创业板股票列表失败: {str(e)}")
                return []

        return self._make_request(_fetch_chinext_stocks)

    def is_st_stock(self, stock_code: str) -> bool:
        """
        判断是否为ST股票

        Args:
            stock_code: 股票代码

        Returns:
            是否为ST股票
        """
        try:
            stock_info = self.get_stock_info(stock_code)
            stock_name = stock_info.get('name', '').upper()

            # 检查股票名称中是否包含ST标识
            st_keywords = ['ST', '*ST', 'S*ST', 'SST', 'S']

            for keyword in st_keywords:
                if keyword in stock_name:
                    return True

            return False

        except Exception as e:
            self.logger.debug(f"判断ST股票失败: {str(e)}")
            return False

    def validate_connection(self) -> bool:
        """
        验证数据源连接

        Returns:
            连接是否正常
        """
        try:
            # 尝试获取一个简单的数据来测试连接
            df = ak.tool_trade_date_hist_sina()
            return not df.empty

        except Exception as e:
            self.logger.error(f"Akshare连接验证失败: {str(e)}")
            return False

    def get_last_update_time(self) -> Optional[datetime]:
        """
        获取最后更新时间

        Returns:
            最后更新时间
        """
        return self.stats.get('last_request_time')

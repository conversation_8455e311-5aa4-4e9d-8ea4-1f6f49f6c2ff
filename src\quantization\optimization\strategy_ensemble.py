#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略组合管理器

实现多策略组合、权重分配、动态调整和风险管理。
支持多种组合方法：等权重、风险平价、最优化权重等。
"""

import time
import warnings
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
import pandas as pd
from collections import defaultdict, deque
import threading
import json
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import multiprocessing as mp

from quantization.utils.logger import get_logger

# 可选依赖
try:
    from scipy import optimize
    from scipy.stats import pearsonr
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False


class EnsembleMethod(Enum):
    """组合方法"""
    EQUAL_WEIGHT = "equal_weight"           # 等权重
    RISK_PARITY = "risk_parity"            # 风险平价
    MEAN_VARIANCE = "mean_variance"         # 均值方差优化
    SHARPE_WEIGHTED = "sharpe_weighted"     # 夏普比率加权
    KELLY_CRITERION = "kelly_criterion"     # 凯利公式
    ADAPTIVE = "adaptive"                   # 自适应权重


class RebalanceFrequency(Enum):
    """再平衡频率"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    ADAPTIVE = "adaptive"


@dataclass
class StrategyPerformance:
    """策略表现数据"""
    strategy_id: str
    returns: List[float]
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    correlation_matrix: Optional[Dict[str, float]] = None
    
    def __post_init__(self):
        if not self.returns:
            self.returns = []
        
        # 计算基本统计指标
        if self.returns:
            returns_array = np.array(self.returns)
            self.volatility = float(np.std(returns_array))
            mean_return = np.mean(returns_array)
            self.sharpe_ratio = float(mean_return / max(self.volatility, 1e-8))
            
            # 计算最大回撤
            cumulative = np.cumprod(1 + returns_array)
            running_max = np.maximum.accumulate(cumulative)
            drawdown = (cumulative - running_max) / running_max
            self.max_drawdown = float(np.min(drawdown))
            
            # 计算胜率
            self.win_rate = float(np.mean(returns_array > 0))


@dataclass
class EnsembleConfig:
    """组合配置"""
    method: EnsembleMethod = EnsembleMethod.EQUAL_WEIGHT
    rebalance_frequency: RebalanceFrequency = RebalanceFrequency.MONTHLY
    min_weight: float = 0.0
    max_weight: float = 1.0
    target_volatility: Optional[float] = None
    risk_free_rate: float = 0.02
    lookback_period: int = 252  # 回看期（交易日）
    correlation_threshold: float = 0.8  # 相关性阈值
    enable_risk_management: bool = True


class StrategyEnsembleManager:
    """
    策略组合管理器
    
    管理多个策略的组合，动态调整权重，优化整体表现。
    """
    
    def __init__(self, config: Optional[EnsembleConfig] = None):
        self.logger = get_logger(self.__class__.__name__)
        self.config = config or EnsembleConfig()
        
        # 策略管理
        self.strategies: Dict[str, Any] = {}
        self.strategy_performances: Dict[str, StrategyPerformance] = {}
        self.current_weights: Dict[str, float] = {}
        
        # 历史数据
        self.weight_history: List[Tuple[pd.Timestamp, Dict[str, float]]] = []
        self.performance_history: List[Tuple[pd.Timestamp, Dict[str, Any]]] = []
        
        # 风险管理
        self.risk_metrics: Dict[str, float] = {}
        self.correlation_matrix: Optional[pd.DataFrame] = None
        
        # 线程安全
        self._lock = threading.RLock()
        
        # 最后再平衡时间
        self.last_rebalance: Optional[pd.Timestamp] = None
    
    def add_strategy(self, strategy_id: str, strategy: Any, 
                    initial_weight: Optional[float] = None):
        """
        添加策略到组合
        
        Args:
            strategy_id: 策略标识
            strategy: 策略对象
            initial_weight: 初始权重
        """
        with self._lock:
            self.strategies[strategy_id] = strategy
            
            if initial_weight is not None:
                self.current_weights[strategy_id] = initial_weight
            else:
                # 等权重分配
                n_strategies = len(self.strategies)
                equal_weight = 1.0 / n_strategies
                
                # 重新分配所有权重
                for sid in self.strategies.keys():
                    self.current_weights[sid] = equal_weight
            
            self.logger.info(f"添加策略 {strategy_id}，当前权重: {self.current_weights.get(strategy_id, 0):.4f}")
    
    def remove_strategy(self, strategy_id: str):
        """移除策略"""
        with self._lock:
            if strategy_id in self.strategies:
                del self.strategies[strategy_id]
                
                if strategy_id in self.current_weights:
                    del self.current_weights[strategy_id]
                
                if strategy_id in self.strategy_performances:
                    del self.strategy_performances[strategy_id]
                
                # 重新标准化权重
                self._normalize_weights()
                
                self.logger.info(f"移除策略 {strategy_id}")
    
    def update_strategy_performance(self, strategy_id: str, 
                                  performance: StrategyPerformance):
        """更新策略表现数据"""
        with self._lock:
            self.strategy_performances[strategy_id] = performance
            self.logger.debug(f"更新策略 {strategy_id} 表现数据")
    
    def calculate_ensemble_weights(self, 
                                 method: Optional[EnsembleMethod] = None) -> Dict[str, float]:
        """
        计算组合权重
        
        Args:
            method: 组合方法
            
        Returns:
            策略权重字典
        """
        method = method or self.config.method
        
        if not self.strategy_performances:
            # 如果没有表现数据，使用等权重
            return self._calculate_equal_weights()
        
        if method == EnsembleMethod.EQUAL_WEIGHT:
            return self._calculate_equal_weights()
        elif method == EnsembleMethod.RISK_PARITY:
            return self._calculate_risk_parity_weights()
        elif method == EnsembleMethod.MEAN_VARIANCE:
            return self._calculate_mean_variance_weights()
        elif method == EnsembleMethod.SHARPE_WEIGHTED:
            return self._calculate_sharpe_weighted()
        elif method == EnsembleMethod.KELLY_CRITERION:
            return self._calculate_kelly_weights()
        elif method == EnsembleMethod.ADAPTIVE:
            return self._calculate_adaptive_weights()
        else:
            self.logger.warning(f"不支持的组合方法 {method.value}，使用等权重")
            return self._calculate_equal_weights()
    
    def _calculate_equal_weights(self) -> Dict[str, float]:
        """计算等权重"""
        if not self.strategies:
            return {}
        
        equal_weight = 1.0 / len(self.strategies)
        return {strategy_id: equal_weight for strategy_id in self.strategies.keys()}
    
    def _calculate_risk_parity_weights(self) -> Dict[str, float]:
        """计算风险平价权重"""
        if not self.strategy_performances:
            return self._calculate_equal_weights()
        
        # 获取波动率
        volatilities = {}
        for strategy_id, perf in self.strategy_performances.items():
            if strategy_id in self.strategies:
                volatilities[strategy_id] = max(perf.volatility, 1e-8)
        
        if not volatilities:
            return self._calculate_equal_weights()
        
        # 风险平价：权重与波动率成反比
        inv_vol = {sid: 1.0 / vol for sid, vol in volatilities.items()}
        total_inv_vol = sum(inv_vol.values())
        
        weights = {sid: inv_vol[sid] / total_inv_vol for sid in inv_vol.keys()}
        
        return self._apply_weight_constraints(weights)
    
    def _calculate_mean_variance_weights(self) -> Dict[str, float]:
        """计算均值方差优化权重"""
        if not SCIPY_AVAILABLE:
            self.logger.warning("均值方差优化需要scipy，回退到风险平价")
            return self._calculate_risk_parity_weights()
        
        if len(self.strategy_performances) < 2:
            return self._calculate_equal_weights()
        
        try:
            # 构建收益率矩阵
            returns_data = {}
            for strategy_id, perf in self.strategy_performances.items():
                if strategy_id in self.strategies and perf.returns:
                    returns_data[strategy_id] = perf.returns[-self.config.lookback_period:]
            
            if len(returns_data) < 2:
                return self._calculate_equal_weights()
            
            # 对齐数据长度
            min_length = min(len(returns) for returns in returns_data.values())
            if min_length < 10:  # 数据太少
                return self._calculate_equal_weights()
            
            aligned_returns = {}
            for strategy_id, returns in returns_data.items():
                aligned_returns[strategy_id] = returns[-min_length:]
            
            # 计算协方差矩阵
            strategy_ids = list(aligned_returns.keys())
            returns_matrix = np.array([aligned_returns[sid] for sid in strategy_ids])
            
            mean_returns = np.mean(returns_matrix, axis=1)
            cov_matrix = np.cov(returns_matrix)
            
            # 添加正则化避免奇异矩阵
            cov_matrix += np.eye(len(strategy_ids)) * 1e-8
            
            # 最大化夏普比率
            def negative_sharpe(weights):
                portfolio_return = np.dot(weights, mean_returns)
                portfolio_vol = np.sqrt(np.dot(weights, np.dot(cov_matrix, weights)))
                return -(portfolio_return - self.config.risk_free_rate / 252) / max(portfolio_vol, 1e-8)
            
            # 约束条件
            constraints = [{'type': 'eq', 'fun': lambda x: np.sum(x) - 1}]
            bounds = [(self.config.min_weight, self.config.max_weight) for _ in strategy_ids]
            
            # 初始权重
            x0 = np.array([1.0 / len(strategy_ids)] * len(strategy_ids))
            
            # 优化
            result = optimize.minimize(
                negative_sharpe, x0, method='SLSQP',
                bounds=bounds, constraints=constraints
            )
            
            if result.success:
                weights = dict(zip(strategy_ids, result.x))
                return self._apply_weight_constraints(weights)
            else:
                self.logger.warning("均值方差优化失败，使用风险平价")
                return self._calculate_risk_parity_weights()
                
        except Exception as e:
            self.logger.error(f"均值方差优化出错: {e}")
            return self._calculate_risk_parity_weights()
    
    def _calculate_sharpe_weighted(self) -> Dict[str, float]:
        """计算夏普比率加权"""
        if not self.strategy_performances:
            return self._calculate_equal_weights()
        
        # 获取夏普比率
        sharpe_ratios = {}
        for strategy_id, perf in self.strategy_performances.items():
            if strategy_id in self.strategies:
                # 只考虑正的夏普比率
                sharpe_ratios[strategy_id] = max(perf.sharpe_ratio, 0.0)
        
        if not sharpe_ratios or sum(sharpe_ratios.values()) == 0:
            return self._calculate_equal_weights()
        
        # 按夏普比率加权
        total_sharpe = sum(sharpe_ratios.values())
        weights = {sid: sharpe / total_sharpe for sid, sharpe in sharpe_ratios.items()}
        
        return self._apply_weight_constraints(weights)
    
    def _calculate_kelly_weights(self) -> Dict[str, float]:
        """计算凯利公式权重"""
        if not self.strategy_performances:
            return self._calculate_equal_weights()
        
        kelly_weights = {}
        
        for strategy_id, perf in self.strategy_performances.items():
            if strategy_id in self.strategies and perf.returns:
                returns = np.array(perf.returns[-self.config.lookback_period:])
                
                if len(returns) < 10:
                    kelly_weights[strategy_id] = 0.0
                    continue
                
                # 凯利公式: f = (bp - q) / b
                # 其中 b = 赔率, p = 胜率, q = 败率
                win_rate = perf.win_rate
                
                if win_rate <= 0.5:
                    kelly_weights[strategy_id] = 0.0
                    continue
                
                # 计算平均盈亏比
                winning_returns = returns[returns > 0]
                losing_returns = returns[returns < 0]
                
                if len(winning_returns) == 0 or len(losing_returns) == 0:
                    kelly_weights[strategy_id] = 0.0
                    continue
                
                avg_win = np.mean(winning_returns)
                avg_loss = abs(np.mean(losing_returns))
                
                if avg_loss == 0:
                    kelly_weights[strategy_id] = 0.0
                    continue
                
                odds_ratio = avg_win / avg_loss
                kelly_fraction = (odds_ratio * win_rate - (1 - win_rate)) / odds_ratio
                
                # 限制凯利比例，避免过度杠杆
                kelly_weights[strategy_id] = max(0.0, min(kelly_fraction, 0.25))
        
        if not kelly_weights or sum(kelly_weights.values()) == 0:
            return self._calculate_equal_weights()
        
        # 标准化权重
        total_weight = sum(kelly_weights.values())
        normalized_weights = {sid: weight / total_weight 
                            for sid, weight in kelly_weights.items()}
        
        return self._apply_weight_constraints(normalized_weights)
    
    def _calculate_adaptive_weights(self) -> Dict[str, float]:
        """计算自适应权重"""
        # 自适应权重结合多种方法
        methods = [
            EnsembleMethod.RISK_PARITY,
            EnsembleMethod.SHARPE_WEIGHTED,
            EnsembleMethod.KELLY_CRITERION
        ]
        
        method_weights = []
        for method in methods:
            weights = self.calculate_ensemble_weights(method)
            method_weights.append(weights)
        
        if not method_weights:
            return self._calculate_equal_weights()
        
        # 计算各方法的平均权重
        strategy_ids = list(self.strategies.keys())
        adaptive_weights = {}
        
        for strategy_id in strategy_ids:
            weight_sum = sum(weights.get(strategy_id, 0.0) for weights in method_weights)
            adaptive_weights[strategy_id] = weight_sum / len(method_weights)
        
        return self._apply_weight_constraints(adaptive_weights)
    
    def _apply_weight_constraints(self, weights: Dict[str, float]) -> Dict[str, float]:
        """应用权重约束"""
        # 应用最小最大权重约束
        constrained_weights = {}
        for strategy_id, weight in weights.items():
            constrained_weights[strategy_id] = np.clip(
                weight, self.config.min_weight, self.config.max_weight
            )
        
        # 重新标准化
        total_weight = sum(constrained_weights.values())
        if total_weight > 0:
            constrained_weights = {
                sid: weight / total_weight 
                for sid, weight in constrained_weights.items()
            }
        
        return constrained_weights
    
    def _normalize_weights(self):
        """标准化权重"""
        if not self.current_weights:
            return
        
        total_weight = sum(self.current_weights.values())
        if total_weight > 0:
            for strategy_id in self.current_weights:
                self.current_weights[strategy_id] /= total_weight
    
    def should_rebalance(self, current_time: pd.Timestamp) -> bool:
        """判断是否需要再平衡"""
        if self.last_rebalance is None:
            return True
        
        time_diff = current_time - self.last_rebalance
        
        if self.config.rebalance_frequency == RebalanceFrequency.DAILY:
            return time_diff.days >= 1
        elif self.config.rebalance_frequency == RebalanceFrequency.WEEKLY:
            return time_diff.days >= 7
        elif self.config.rebalance_frequency == RebalanceFrequency.MONTHLY:
            return time_diff.days >= 30
        elif self.config.rebalance_frequency == RebalanceFrequency.QUARTERLY:
            return time_diff.days >= 90
        elif self.config.rebalance_frequency == RebalanceFrequency.ADAPTIVE:
            # 自适应再平衡：基于权重偏离度
            return self._check_adaptive_rebalance()
        
        return False
    
    def _check_adaptive_rebalance(self) -> bool:
        """检查自适应再平衡条件"""
        if not self.current_weights or not self.strategy_performances:
            return False
        
        # 计算理想权重
        ideal_weights = self.calculate_ensemble_weights()
        
        # 计算权重偏离度
        total_deviation = 0.0
        for strategy_id in self.strategies.keys():
            current_weight = self.current_weights.get(strategy_id, 0.0)
            ideal_weight = ideal_weights.get(strategy_id, 0.0)
            total_deviation += abs(current_weight - ideal_weight)
        
        # 如果总偏离度超过阈值，则再平衡
        return total_deviation > 0.1  # 10%阈值
    
    def rebalance(self, current_time: pd.Timestamp) -> Dict[str, float]:
        """
        执行再平衡
        
        Args:
            current_time: 当前时间
            
        Returns:
            新的权重分配
        """
        with self._lock:
            # 计算新权重
            new_weights = self.calculate_ensemble_weights()
            
            # 更新当前权重
            self.current_weights = new_weights.copy()
            self.last_rebalance = current_time
            
            # 记录历史
            self.weight_history.append((current_time, new_weights.copy()))
            
            self.logger.info(f"执行再平衡，新权重: {new_weights}")
            
            return new_weights
    
    def get_current_weights(self) -> Dict[str, float]:
        """获取当前权重"""
        with self._lock:
            return self.current_weights.copy()
    
    def get_ensemble_performance(self) -> Dict[str, Any]:
        """获取组合整体表现"""
        if not self.strategy_performances or not self.current_weights:
            return {}
        
        # 计算组合收益率
        ensemble_returns = []
        min_length = float('inf')
        
        # 找到最短的收益率序列长度
        for strategy_id, perf in self.strategy_performances.items():
            if strategy_id in self.current_weights and perf.returns:
                min_length = min(min_length, len(perf.returns))
        
        if min_length == float('inf') or min_length == 0:
            return {}
        
        # 计算组合收益率
        for i in range(int(min_length)):
            weighted_return = 0.0
            for strategy_id, weight in self.current_weights.items():
                if strategy_id in self.strategy_performances:
                    perf = self.strategy_performances[strategy_id]
                    if i < len(perf.returns):
                        weighted_return += weight * perf.returns[i]
            ensemble_returns.append(weighted_return)
        
        if not ensemble_returns:
            return {}
        
        # 计算组合统计指标
        returns_array = np.array(ensemble_returns)
        
        return {
            'total_return': float(np.prod(1 + returns_array) - 1),
            'annualized_return': float(np.mean(returns_array) * 252),
            'volatility': float(np.std(returns_array) * np.sqrt(252)),
            'sharpe_ratio': float(np.mean(returns_array) / max(np.std(returns_array), 1e-8) * np.sqrt(252)),
            'max_drawdown': float(self._calculate_max_drawdown(returns_array)),
            'win_rate': float(np.mean(returns_array > 0)),
            'current_weights': self.current_weights.copy(),
            'n_strategies': len(self.strategies)
        }
    
    def _calculate_max_drawdown(self, returns: np.ndarray) -> float:
        """计算最大回撤"""
        cumulative = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        return np.min(drawdown)

{"data": {"akshare": {"enabled": true, "timeout": 30}, "cache": {"enabled": true, "ttl": 3600}}, "database": {"host": "localhost", "port": 3306, "name": "quantization", "pool_size": 10}, "system": {"log_level": "DEBUG", "max_workers": 4}, "api": {"debug": true, "host": "127.0.0.1", "port": 8000}, "demo": {"test_value": 42, "test_string": "Hello World", "nested": {"value": {"key": "value"}}, "validated_number": 50, "monitored_value": "changed"}}
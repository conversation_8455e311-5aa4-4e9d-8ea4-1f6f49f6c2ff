#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股量化交易框架

专为A股市场设计的综合性量化交易框架，提供数据采集、策略开发、回测分析等完整功能。

主要功能模块：
- core: 核心数据管理和接口
- storage: 数据存储和缓存
- strategies: 交易策略实现
- backtesting: 回测引擎和分析
- utils: 工具函数和辅助模块
- config: 配置管理

使用示例：
    >>> from quantization import DataManager, ChiNextStrategy
    >>> dm = DataManager()
    >>> strategy = ChiNextStrategy()
    >>> data = dm.get_stock_data('300001')
"""

__version__ = "1.0.0"
__author__ = "A股量化交易框架团队"
__email__ = "<EMAIL>"
__license__ = "MIT"

# 核心模块导入
try:
    from .core.data_manager import DataManager
    from .core.interfaces.data_interface import DataInterface

    # 策略模块导入
    from .strategies.base import BaseStrategy
    from .strategies.chinext_selection import ChiNextSelectionStrategy
except ImportError as e:
    # 如果相对导入失败，尝试绝对导入
    import sys
    from pathlib import Path

    # 添加src目录到路径
    src_path = Path(__file__).parent.parent
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))

    try:
        from quantization.core.data_manager import DataManager
        from quantization.core.interfaces.data_interface import DataInterface
        from quantization.strategies.base import BaseStrategy
        from quantization.strategies.chinext_selection import ChiNextSelectionStrategy
    except ImportError:
        # 如果仍然失败，定义空的占位符
        DataManager = None
        DataInterface = None
        BaseStrategy = None
        ChiNextSelectionStrategy = None

# 回测模块导入（暂时注释，待实现）
# from quantization.backtesting.engine import BacktestEngine
# from quantization.backtesting.metrics import PerformanceMetrics

# 工具模块导入
from quantization.utils.logger import get_logger
from quantization.utils.validators import validate_stock_code

# 配置模块导入
from quantization.config.settings import Config

# 公开API
__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    "__email__",
    "__license__",
    
    # 核心类
    "DataManager",
    "DataInterface",
    
    # 策略类
    "BaseStrategy",
    "ChiNextSelectionStrategy",

    # 回测类（暂时注释，待实现）
    # "BacktestEngine",
    # "PerformanceMetrics",
    
    # 工具函数
    "get_logger",
    "validate_stock_code",
    
    # 配置类
    "Config",
]

# 模块级别配置
import logging
logging.getLogger(__name__).addHandler(logging.NullHandler())

# 框架初始化检查
def _check_dependencies():
    """检查必要的依赖包是否已安装"""
    required_packages = [
        'pandas', 'numpy', 'akshare', 'requests', 
        'beautifulsoup4', 'lxml', 'sqlalchemy'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        import warnings
        warnings.warn(
            f"缺少必要的依赖包: {', '.join(missing_packages)}. "
            f"请运行 'pip install {' '.join(missing_packages)}' 进行安装。",
            ImportWarning
        )

# 执行依赖检查
_check_dependencies()

# 框架信息
def get_info():
    """获取框架信息"""
    return {
        "name": "A股量化交易框架",
        "version": __version__,
        "author": __author__,
        "license": __license__,
        "description": "专为A股市场设计的综合性量化交易框架",
        "modules": [
            "core - 核心数据管理",
            "storage - 数据存储",
            "strategies - 交易策略",
            "backtesting - 回测分析",
            "utils - 工具函数",
            "config - 配置管理"
        ]
    }

def print_info():
    """打印框架信息"""
    info = get_info()
    print(f"\n{info['name']} v{info['version']}")
    print(f"作者: {info['author']}")
    print(f"许可证: {info['license']}")
    print(f"描述: {info['description']}")
    print("\n主要模块:")
    for module in info['modules']:
        print(f"  - {module}")
    print()

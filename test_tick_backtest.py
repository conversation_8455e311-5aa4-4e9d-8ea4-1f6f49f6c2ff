#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创业板动态因子策略TICK数据回测测试
"""

import sys
from pathlib import Path
import logging

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.quantization.strategies.chinext_dynamic_factor_strategy import ChiNextDynamicFactorStrategy
from src.quantization.strategies.tick_backtester import TickBacktester
from src.quantization.strategies.tick_data_manager import TickDataManager


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('tick_backtest.log', encoding='utf-8')
        ]
    )


def main():
    """主测试函数"""
    try:
        setup_logging()
        logger = logging.getLogger(__name__)
        
        print("=" * 80)
        print("创业板动态因子策略TICK数据回测测试")
        print("=" * 80)
        
        # 策略配置
        config = {
            # BaseStrategy需要的基础参数
            'max_stocks': 10,
            'min_market_cap': 15e8,      # 最小市值15亿元
            'max_market_cap': 250e8,     # 最大市值250亿元
            'rebalance_frequency': 'daily',
            'risk_level': 'medium',
            
            # 创业板策略特定参数
            'market_cap_min': 15e8,      # 最小市值15亿元
            'market_cap_max': 250e8,     # 最大市值250亿元
            'volume_ratio_min': 2.0,     # 量比 > 2
            'dde_net_ratio_min': 0.5,    # DDE大单净量 > 0.5%
            'turnover_rate_min': 4.0,    # 换手率 > 4%
            'turnover_growth_min': 0.0,  # 换手率环比增长 > 0
            
            # 价格特征参数
            'limit_up_days': 60,         # 近60个交易日
            'min_limit_up_count': 1,     # 至少1次涨停
            
            # 交易触发参数
            'market_volume_threshold': 1.5e12,  # 1.5万亿元成交额阈值
            
            # 资金管理参数
            'initial_capital': 1000000,  # 初始资金100万元
            'max_positions': 10,         # 最大持仓数量
            'position_size': 100000,     # 单个标的建仓资金10万元
            'max_position_ratio': 0.1,   # 单只标的最大仓位10%
            
            # 交易时点参数
            'buy_time_before_close': 30,  # 收盘前30分钟买入
            'sell_time_after_open': 30,   # 开盘后30分钟卖出
            
            # 数据缓存参数
            'cache_dir': 'strategy_cache',
            'use_cache': True,
        }
        
        # 回测参数
        backtest_config = {
            'start_date': '2024-01-15',
            'end_date': '2024-01-19',  # 短期测试
            'initial_capital': 1000000,
            'commission_rate': 0.0003
        }
        
        print("1. 初始化策略和回测引擎...")
        
        # 创建策略实例
        strategy = ChiNextDynamicFactorStrategy(config)
        strategy.initialize(config)
        
        # 创建回测引擎
        backtester = TickBacktester(
            initial_capital=backtest_config['initial_capital'],
            commission_rate=backtest_config['commission_rate']
        )
        
        print("2. 准备测试股票池...")
        
        # 获取创业板股票池（取前20只进行测试）
        test_stocks = [
            '300001.SZ', '300002.SZ', '300003.SZ', '300004.SZ', '300005.SZ',
            '300006.SZ', '300007.SZ', '300008.SZ', '300009.SZ', '300010.SZ',
            '300011.SZ', '300012.SZ', '300013.SZ', '300014.SZ', '300015.SZ',
            '300016.SZ', '300017.SZ', '300018.SZ', '300019.SZ', '300020.SZ'
        ]
        
        print(f"   测试股票数量: {len(test_stocks)}")
        print(f"   回测期间: {backtest_config['start_date']} 至 {backtest_config['end_date']}")
        
        print("3. 检查TICK数据完整性...")
        
        # 检查并下载TICK数据
        tick_manager = TickDataManager()
        
        # 生成测试日期
        test_dates = ['2024-01-15', '2024-01-16', '2024-01-17', '2024-01-18', '2024-01-19']
        
        # 批量下载TICK数据（如果需要）
        print("   开始下载TICK数据...")
        download_results = tick_manager.batch_download_tick_data(
            test_stocks[:5],  # 只测试前5只股票
            test_dates,
            max_workers=3
        )
        
        print(f"   TICK数据下载结果: {download_results}")
        
        print("4. 运行TICK数据回测...")
        
        # 运行回测
        backtest_result = backtester.run_backtest(
            strategy=strategy,
            start_date=backtest_config['start_date'],
            end_date=backtest_config['end_date'],
            stock_universe=test_stocks[:5]  # 只测试前5只股票
        )
        
        print("5. 回测结果分析:")
        print(f"   总收益率: {backtest_result.total_return:.2%}")
        print(f"   年化收益率: {backtest_result.annual_return:.2%}")
        print(f"   最大回撤: {backtest_result.max_drawdown:.2%}")
        print(f"   夏普比率: {backtest_result.sharpe_ratio:.3f}")
        print(f"   胜率: {backtest_result.win_rate:.2%}")
        print(f"   总交易次数: {backtest_result.total_trades}")
        print(f"   盈利交易: {backtest_result.profit_trades}")
        print(f"   亏损交易: {backtest_result.loss_trades}")
        print(f"   平均盈利: {backtest_result.avg_profit:.2f}元")
        print(f"   平均亏损: {backtest_result.avg_loss:.2f}元")
        
        print("6. 持仓详情:")
        if backtest_result.positions:
            for i, position in enumerate(backtest_result.positions, 1):
                print(f"   {i}. {position.stock_code}: {position.quantity}股, "
                      f"成本: {position.avg_cost:.2f}, "
                      f"市值: {position.market_value:.2f}, "
                      f"盈亏: {position.unrealized_pnl:.2f}")
        else:
            print("   无持仓")
        
        print("7. 订单执行情况:")
        filled_orders = [o for o in backtest_result.orders if o.status.value == 'filled']
        print(f"   成功执行订单: {len(filled_orders)}")
        
        for i, order in enumerate(filled_orders[:10], 1):  # 只显示前10个订单
            print(f"   {i}. {order.stock_code} {order.order_type.value} "
                  f"{order.quantity}股 @{order.filled_price:.2f} "
                  f"手续费: {order.commission:.2f}")
        
        print("8. 权益曲线:")
        if not backtest_result.equity_curve.empty:
            print("   日期\t\t现金\t\t市值\t\t总资产\t\t收益率")
            for idx, row in backtest_result.equity_curve.iterrows():
                print(f"   {idx.strftime('%Y-%m-%d')}\t"
                      f"{row['cash']:.0f}\t\t"
                      f"{row['market_value']:.0f}\t\t"
                      f"{row['total_equity']:.0f}\t\t"
                      f"{row['return']:.2%}")
        
        print("9. TICK数据统计:")
        tick_stats = tick_manager.get_download_statistics()
        print(f"   下载统计: {tick_stats}")
        
        print("10. 清理资源...")
        strategy.cleanup()
        
        print("\n" + "=" * 80)
        print("TICK数据回测完成!")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 回测失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

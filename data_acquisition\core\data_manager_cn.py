"""
A股数据管理器 - 数据采集框架的中央协调器

这是一个专为A股回测设计的数据管理系统，提供：
- 多数据源智能切换
- 本地缓存和数据库存储
- 数据质量验证和清洗
- 并发数据获取
- 完整的错误处理和重试机制
"""

import pandas as pd
from typing import Dict, List, Optional, Union, Tuple
from datetime import datetime, date, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from .akshare_provider import AkshareProvider
from .web_scraper import WebScraper
from ..storage.database import DatabaseManager
from ..storage.cache_manager import CacheManager
from ..utils.data_validator import DataValidator
from ..utils.stock_codes import normalize_stock_code, validate_stock_codes
from ..utils.rate_limiter import MultiSourceRateLimiter
from ..config.settings import Config
from ..utils.logger import get_data_manager_logger


class DataManager:
    """
    A股数据管理器
    
    负责协调所有数据采集操作，包括：
    - 多数据源管理（akshare、网络爬虫）
    - 智能缓存和数据库存储
    - 数据质量控制
    - 并发数据获取
    """
    
    def __init__(self, config: Optional[Config] = None):
        """
        初始化数据管理器
        
        参数:
            config: 配置实例，如果为None则使用默认配置
        """
        self.config = config or Config()
        self.logger = get_data_manager_logger(config)
        
        # 初始化组件
        self.database = DatabaseManager(config)
        self.cache = CacheManager(config)
        self.validator = DataValidator(config)
        self.rate_limiter = MultiSourceRateLimiter(config)
        
        # 初始化数据提供商
        self.providers = {}
        self._init_providers()
        
        # 线程锁，确保线程安全
        self._lock = threading.Lock()
        
        self.logger.info("数据管理器初始化成功")
    
    def _init_providers(self):
        """初始化数据提供商"""
        try:
            # 主要提供商：akshare
            akshare_provider = AkshareProvider(self.config)
            if akshare_provider.is_available():
                self.providers['akshare'] = akshare_provider
                self.logger.info("akshare数据源初始化成功")
            else:
                self.logger.warning("akshare数据源不可用")
            
            # 备用提供商：网络爬虫
            web_scraper = WebScraper(self.config)
            if web_scraper.is_available():
                self.providers['web_scraper'] = web_scraper
                self.logger.info("网络爬虫数据源初始化成功")
            else:
                self.logger.warning("网络爬虫数据源不可用")
            
            if not self.providers:
                self.logger.error("没有可用的数据源！")
                
        except Exception as e:
            self.logger.error(f"初始化数据源失败: {e}")
    
    def get_stock_data(self, 
                      stock_code: str,
                      start_date: Union[str, date, datetime],
                      end_date: Union[str, date, datetime],
                      use_cache: bool = True,
                      force_update: bool = False) -> Optional[pd.DataFrame]:
        """
        获取股票数据，支持智能数据源选择
        
        参数:
            stock_code: 股票代码 (如 '000001.SZ')
            start_date: 开始日期
            end_date: 结束日期
            use_cache: 是否使用缓存数据
            force_update: 强制从数据源更新（忽略缓存）
            
        返回:
            pd.DataFrame: 股票数据，失败时返回None
        """
        try:
            # 标准化股票代码
            normalized_code = normalize_stock_code(stock_code)
            if not normalized_code:
                self.logger.error(f"无效的股票代码: {stock_code}")
                return None
            
            # 转换日期格式
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date).date()
            if isinstance(end_date, str):
                end_date = pd.to_datetime(end_date).date()
            
            # 首先检查缓存（如果启用且不强制更新）
            if use_cache and not force_update:
                cached_data = self.cache.get_cached_data(
                    'stock', normalized_code, 
                    start_date=start_date, end_date=end_date
                )
                if cached_data is not None:
                    self.logger.debug(f"使用缓存数据: {normalized_code}")
                    return cached_data
            
            # 检查数据库中的现有数据
            if not force_update:
                db_data = self.database.get_stock_data(normalized_code, start_date, end_date)
                if db_data is not None and not db_data.empty:
                    # 检查数据覆盖度是否足够
                    coverage = self._check_data_coverage(db_data, start_date, end_date)
                    if coverage['is_sufficient']:
                        self.logger.debug(f"使用数据库数据: {normalized_code}")
                        
                        # 缓存数据
                        if use_cache:
                            self.cache.cache_data(
                                db_data, 'stock', normalized_code,
                                start_date=start_date, end_date=end_date
                            )
                        
                        return db_data
            
            # 从数据源获取数据
            data = self._fetch_from_providers('stock', normalized_code, 
                                            start_date=start_date, end_date=end_date)
            
            if data is not None and not data.empty:
                # 验证数据质量
                is_valid, errors = self.validator.validate_price_data(data, normalized_code)
                if not is_valid:
                    self.logger.warning(f"数据验证失败 {normalized_code}: {errors}")
                    # 清洗数据
                    data = self.validator.clean_data(data, normalized_code)
                
                # 保存到数据库
                self.database.save_stock_data(normalized_code, data, 'data_manager')
                
                # 缓存数据
                if use_cache:
                    self.cache.cache_data(
                        data, 'stock', normalized_code,
                        start_date=start_date, end_date=end_date
                    )
                
                self.logger.info(f"成功获取 {len(data)} 条记录: {normalized_code}")
                return data
            
            self.logger.error(f"获取数据失败: {normalized_code}")
            return None
            
        except Exception as e:
            self.logger.error(f"获取股票数据时出错 {stock_code}: {e}")
            return None
    
    def get_multiple_stocks_data(self,
                               stock_codes: List[str],
                               start_date: Union[str, date, datetime],
                               end_date: Union[str, date, datetime],
                               max_workers: int = 5,
                               use_cache: bool = True) -> Dict[str, pd.DataFrame]:
        """
        并发获取多只股票的数据
        
        参数:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            max_workers: 最大并发工作线程数
            use_cache: 是否使用缓存数据
            
        返回:
            Dict[str, pd.DataFrame]: 股票数据字典
        """
        # 验证股票代码
        valid_codes, invalid_codes = validate_stock_codes(stock_codes)
        
        if invalid_codes:
            self.logger.warning(f"无效的股票代码: {invalid_codes}")
        
        if not valid_codes:
            self.logger.error("没有有效的股票代码")
            return {}
        
        results = {}
        
        # 使用线程池并发获取数据
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_code = {
                executor.submit(
                    self.get_stock_data, code, start_date, end_date, use_cache
                ): code for code in valid_codes
            }
            
            # 收集结果
            for future in as_completed(future_to_code):
                stock_code = future_to_code[future]
                try:
                    data = future.result()
                    if data is not None:
                        results[stock_code] = data
                except Exception as e:
                    self.logger.error(f"获取数据失败 {stock_code}: {e}")
        
        self.logger.info(f"成功获取 {len(results)}/{len(valid_codes)} 只股票的数据")
        return results
    
    def get_stock_info(self, stock_code: str, use_cache: bool = True) -> Optional[Dict]:
        """
        获取股票基本信息
        
        参数:
            stock_code: 股票代码
            use_cache: 是否使用缓存数据
            
        返回:
            Dict: 股票信息，失败时返回None
        """
        try:
            normalized_code = normalize_stock_code(stock_code)
            if not normalized_code:
                return None
            
            # 首先检查数据库
            info = self.database.get_stock_info(normalized_code)
            if info:
                return info
            
            # 从数据源获取
            info = self._fetch_from_providers('info', normalized_code)
            
            if info:
                # 保存到数据库
                self.database.save_stock_info(normalized_code, info)
                return info
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取股票信息时出错 {stock_code}: {e}")
            return None
    
    def _fetch_from_providers(self, data_type: str, identifier: str, **kwargs) -> Optional[Union[pd.DataFrame, Dict]]:
        """
        按优先级从可用的数据源获取数据
        
        参数:
            data_type: 数据类型 ('stock', 'index', 'info', 'financial')
            identifier: 股票代码或标识符
            **kwargs: 额外参数
            
        返回:
            从数据源获取的数据，所有源都失败时返回None
        """
        # 数据源优先级顺序
        provider_order = ['akshare', 'web_scraper']
        
        for provider_name in provider_order:
            if provider_name not in self.providers:
                continue
            
            provider = self.providers[provider_name]
            
            try:
                if data_type == 'stock':
                    data = provider.get_stock_data(identifier, **kwargs)
                elif data_type == 'index':
                    data = provider.get_index_data(identifier, **kwargs)
                elif data_type == 'info':
                    data = provider.get_stock_info(identifier)
                elif data_type == 'financial':
                    data = provider.get_financial_data(identifier, **kwargs)
                else:
                    continue
                
                if data is not None and (isinstance(data, dict) or not data.empty):
                    self.logger.debug(f"成功从 {provider_name} 获取 {data_type} 数据: {identifier}")
                    return data
                    
            except Exception as e:
                self.logger.warning(f"数据源 {provider_name} 失败 {identifier}: {e}")
                continue
        
        self.logger.error(f"所有数据源都失败了 {data_type}:{identifier}")
        return None
    
    def _check_data_coverage(self, data: pd.DataFrame, start_date: date, end_date: date) -> Dict:
        """
        检查现有数据对请求期间的覆盖度是否足够
        
        参数:
            data: 现有数据
            start_date: 请求的开始日期
            end_date: 请求的结束日期
            
        返回:
            Dict: 覆盖度分析结果
        """
        if data.empty:
            return {'is_sufficient': False, 'reason': '无数据'}
        
        data_start = data.index.min().date()
        data_end = data.index.max().date()
        
        # 检查数据是否覆盖请求的期间
        covers_start = data_start <= start_date
        covers_end = data_end >= end_date
        
        # 检查是否有显著的数据缺口
        expected_days = (end_date - start_date).days
        actual_days = len(data)
        coverage_ratio = actual_days / max(expected_days, 1)
        
        is_sufficient = covers_start and covers_end and coverage_ratio > 0.7  # 70%覆盖度阈值
        
        return {
            'is_sufficient': is_sufficient,
            'data_start': data_start,
            'data_end': data_end,
            'covers_start': covers_start,
            'covers_end': covers_end,
            'coverage_ratio': coverage_ratio,
            'actual_days': actual_days,
            'expected_days': expected_days
        }
    
    def get_available_stocks(self, exchange: Optional[str] = None) -> List[str]:
        """
        获取可用股票列表
        
        参数:
            exchange: 交易所过滤器 ('SH', 'SZ', 或 None 表示全部)
            
        返回:
            List[str]: 股票代码列表
        """
        # 首先尝试数据库
        db_stocks = self.database.get_available_stocks(exchange)
        if db_stocks:
            return db_stocks
        
        # 回退到数据源
        for provider_name, provider in self.providers.items():
            try:
                stock_list = provider.get_stock_list(exchange)
                if stock_list is not None and not stock_list.empty:
                    # 提取股票代码（假设存在'code'列）
                    if 'code' in stock_list.columns:
                        codes = stock_list['code'].tolist()
                        # 标准化代码
                        normalized_codes = [normalize_stock_code(code) for code in codes]
                        return [code for code in normalized_codes if code]
            except Exception as e:
                self.logger.warning(f"从 {provider_name} 获取股票列表失败: {e}")
        
        return []
    
    def update_stock_data(self, stock_code: str, days_back: int = 30) -> bool:
        """
        更新股票的最近数据
        
        参数:
            stock_code: 股票代码
            days_back: 回溯天数
            
        返回:
            bool: 更新成功返回True
        """
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=days_back)
            
            data = self.get_stock_data(stock_code, start_date, end_date, 
                                     use_cache=False, force_update=True)
            
            return data is not None and not data.empty
            
        except Exception as e:
            self.logger.error(f"更新数据失败 {stock_code}: {e}")
            return False
    
    def get_data_stats(self) -> Dict:
        """
        获取数据采集统计信息
        
        返回:
            Dict: 统计信息
        """
        try:
            stats = {
                'providers': {name: provider.get_provider_info() 
                            for name, provider in self.providers.items()},
                'cache': self.cache.get_cache_stats(),
                'available_stocks_count': len(self.get_available_stocks())
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取数据统计失败: {e}")
            return {'error': str(e)}
    
    def cleanup(self):
        """清理资源"""
        try:
            # 清理缓存
            self.cache.cleanup_expired_cache()
            
            # 关闭数据库连接
            self.database.close()
            
            # 关闭网络爬虫会话
            if 'web_scraper' in self.providers:
                self.providers['web_scraper'].close()
            
            self.logger.info("数据管理器清理完成")
            
        except Exception as e:
            self.logger.error(f"清理过程中出错: {e}")

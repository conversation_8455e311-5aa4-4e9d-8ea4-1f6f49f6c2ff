#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多数据源数据提供器
支持akshare、网络爬虫等多种数据源的自动切换
"""

import time
import random
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
import json
import re
from urllib.parse import urlencode
import warnings
warnings.filterwarnings('ignore')

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    print("警告: akshare未安装，将仅使用网络爬虫数据源")

class MultiSourceDataProvider:
    """多数据源数据提供器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化多数据源数据提供器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # 数据源优先级配置
        self.data_sources = ['akshare', 'eastmoney', 'sina', 'tencent']
        self.current_source_index = 0
        
        # 网络请求配置
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 请求频率控制
        self.request_delay = self.config.get('request_delay', 0.5)  # 请求间隔
        self.last_request_time = 0
        
        # 重试配置
        self.max_retries = self.config.get('max_retries', 3)
        self.retry_delay = self.config.get('retry_delay', 1.0)
        
        self.logger.info("多数据源数据提供器初始化完成")
    
    def _rate_limit(self):
        """请求频率控制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.request_delay:
            sleep_time = self.request_delay - time_since_last
            time.sleep(sleep_time)
        self.last_request_time = time.time()
    
    def get_minute_data(self, stock_code: str, date: str) -> Optional[pd.DataFrame]:
        """
        获取股票分时数据（多数据源自动切换）
        
        Args:
            stock_code: 股票代码 (如: 300015.SZ)
            date: 日期 (如: 2025-06-27)
            
        Returns:
            DataFrame: 分时数据，包含时间、开高低收量等字段
        """
        for source in self.data_sources:
            try:
                self.logger.info(f"尝试使用数据源: {source}")
                data = self._get_data_from_source(source, stock_code, date)
                if data is not None and not data.empty:
                    self.logger.info(f"成功从 {source} 获取数据: {len(data)} 条记录")
                    return data
                else:
                    self.logger.warning(f"数据源 {source} 返回空数据")
            except Exception as e:
                self.logger.warning(f"数据源 {source} 获取失败: {e}")
                continue
        
        self.logger.error(f"所有数据源都无法获取数据: {stock_code} {date}")
        return None
    
    def _get_data_from_source(self, source: str, stock_code: str, date: str) -> Optional[pd.DataFrame]:
        """从指定数据源获取数据"""
        if source == 'akshare':
            return self._get_akshare_data(stock_code, date)
        elif source == 'eastmoney':
            return self._get_eastmoney_data(stock_code, date)
        elif source == 'sina':
            return self._get_sina_data(stock_code, date)
        elif source == 'tencent':
            return self._get_tencent_data(stock_code, date)
        else:
            raise ValueError(f"不支持的数据源: {source}")
    
    def _get_akshare_data(self, stock_code: str, date: str) -> Optional[pd.DataFrame]:
        """从akshare获取数据"""
        if not AKSHARE_AVAILABLE:
            raise ImportError("akshare不可用")
        
        # 转换股票代码格式
        symbol = stock_code.replace('.SZ', '').replace('.SH', '')
        
        try:
            data = ak.stock_zh_a_hist_min_em(
                symbol=symbol,
                start_date=f"{date} 09:30:00",
                end_date=f"{date} 15:00:00",
                period="1",
                adjust="qfq"
            )
            
            if data.empty:
                return None
            
            # 标准化列名
            data = self._standardize_columns(data, 'akshare')
            return data
            
        except Exception as e:
            self.logger.error(f"akshare获取数据失败: {e}")
            return None
    
    def _get_eastmoney_data(self, stock_code: str, date: str) -> Optional[pd.DataFrame]:
        """从东方财富获取分时数据"""
        self._rate_limit()
        
        # 转换股票代码格式
        if stock_code.endswith('.SZ'):
            market_code = '0'
            symbol = stock_code.replace('.SZ', '')
        elif stock_code.endswith('.SH'):
            market_code = '1'
            symbol = stock_code.replace('.SH', '')
        else:
            return None
        
        # 构建请求URL
        url = "http://push2his.eastmoney.com/api/qt/stock/trends2/get"
        params = {
            'fields1': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13',
            'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58',
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'ndays': '1',
            'iscr': '0',
            'secid': f"{market_code}.{symbol}",
            'cb': 'jQuery'
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            # 解析JSONP响应
            content = response.text
            json_start = content.find('(') + 1
            json_end = content.rfind(')')
            json_str = content[json_start:json_end]
            
            data = json.loads(json_str)
            
            if 'data' not in data or not data['data']:
                return None
            
            trends = data['data'].get('trends', [])
            if not trends:
                return None
            
            # 解析数据
            records = []
            target_date = date.replace('-', '')
            
            for trend in trends:
                parts = trend.split(',')
                if len(parts) >= 5:
                    time_str = parts[0]
                    # 检查日期是否匹配
                    if not time_str.startswith(target_date):
                        continue
                    
                    # 转换时间格式
                    dt = datetime.strptime(time_str, '%Y%m%d%H%M')
                    time_formatted = dt.strftime('%Y-%m-%d %H:%M:%S')
                    
                    records.append({
                        'time': time_formatted,
                        'open': float(parts[1]),
                        'high': float(parts[4]),
                        'low': float(parts[5]),
                        'close': float(parts[2]),
                        'volume': int(parts[6]) if len(parts) > 6 else 0
                    })
            
            if not records:
                return None
            
            df = pd.DataFrame(records)
            return self._standardize_columns(df, 'eastmoney')
            
        except Exception as e:
            self.logger.error(f"东方财富获取数据失败: {e}")
            return None
    
    def _get_sina_data(self, stock_code: str, date: str) -> Optional[pd.DataFrame]:
        """从新浪财经获取分时数据"""
        self._rate_limit()
        
        # 转换股票代码格式
        if stock_code.endswith('.SZ'):
            sina_code = f"sz{stock_code.replace('.SZ', '')}"
        elif stock_code.endswith('.SH'):
            sina_code = f"sh{stock_code.replace('.SH', '')}"
        else:
            return None
        
        # 新浪分时数据API
        url = f"https://quotes.sina.cn/cn/api/json_v2.php/CN_MarketDataService.getKLineData"
        params = {
            'symbol': sina_code,
            'scale': '5',  # 5分钟K线
            'ma': 'no',
            'datalen': '288'  # 一天的5分钟K线数量
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if not data:
                return None
            
            # 转换为DataFrame
            records = []
            target_date = date
            
            for item in data:
                day = item.get('day', '')
                if day.startswith(target_date):
                    records.append({
                        'time': day,
                        'open': float(item.get('open', 0)),
                        'high': float(item.get('high', 0)),
                        'low': float(item.get('low', 0)),
                        'close': float(item.get('close', 0)),
                        'volume': int(item.get('volume', 0))
                    })
            
            if not records:
                return None
            
            df = pd.DataFrame(records)
            return self._standardize_columns(df, 'sina')
            
        except Exception as e:
            self.logger.error(f"新浪财经获取数据失败: {e}")
            return None
    
    def _get_tencent_data(self, stock_code: str, date: str) -> Optional[pd.DataFrame]:
        """从腾讯财经获取分时数据"""
        self._rate_limit()
        
        # 转换股票代码格式
        if stock_code.endswith('.SZ'):
            tencent_code = f"sz{stock_code.replace('.SZ', '')}"
        elif stock_code.endswith('.SH'):
            tencent_code = f"sh{stock_code.replace('.SH', '')}"
        else:
            return None
        
        # 腾讯分时数据API
        url = f"http://data.gtimg.cn/flashdata/hushen/minute/{tencent_code}.js"
        
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            content = response.text
            if not content or 'split' not in content:
                return None
            
            # 解析数据
            lines = content.strip().split('\n')
            records = []
            
            for line in lines:
                if line and not line.startswith('var'):
                    parts = line.split(' ')
                    if len(parts) >= 2:
                        time_str = parts[0]
                        price = float(parts[1])
                        
                        # 构建完整时间
                        hour_min = time_str.zfill(4)
                        hour = hour_min[:2]
                        minute = hour_min[2:]
                        full_time = f"{date} {hour}:{minute}:00"
                        
                        records.append({
                            'time': full_time,
                            'open': price,
                            'high': price,
                            'low': price,
                            'close': price,
                            'volume': 0  # 腾讯分时数据不包含成交量
                        })
            
            if not records:
                return None
            
            df = pd.DataFrame(records)
            return self._standardize_columns(df, 'tencent')
            
        except Exception as e:
            self.logger.error(f"腾讯财经获取数据失败: {e}")
            return None
    
    def _standardize_columns(self, df: pd.DataFrame, source: str) -> pd.DataFrame:
        """标准化数据列名和格式"""
        if df.empty:
            return df
        
        # 根据不同数据源调整列名
        if source == 'akshare':
            # akshare的列名通常是中文
            column_mapping = {
                '时间': 'time',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount'
            }
            df = df.rename(columns=column_mapping)
        
        # 确保必要的列存在
        required_columns = ['time', 'open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                if col == 'volume':
                    df[col] = 0
                elif col in ['open', 'high', 'low']:
                    df[col] = df['close'] if 'close' in df.columns else 0
                else:
                    df[col] = 0
        
        # 数据类型转换
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        
        # 时间格式标准化
        if 'time' in df.columns:
            df['time'] = pd.to_datetime(df['time'], errors='coerce')
            df = df.dropna(subset=['time'])
        
        # 按时间排序
        if not df.empty and 'time' in df.columns:
            df = df.sort_values('time').reset_index(drop=True)
        
        return df[required_columns]
    
    def get_stock_list(self, market: str = 'chinext') -> List[str]:
        """
        获取股票列表
        
        Args:
            market: 市场类型 ('chinext', 'all')
            
        Returns:
            List[str]: 股票代码列表
        """
        try:
            if AKSHARE_AVAILABLE:
                # 使用akshare获取股票列表
                stock_info = ak.stock_zh_a_spot_em()
                
                if market == 'chinext':
                    # 筛选创业板股票（代码以300开头）
                    chinext_stocks = stock_info[stock_info['代码'].str.startswith('300')]
                    stock_codes = [f"{code}.SZ" for code in chinext_stocks['代码'].tolist()]
                else:
                    # 所有股票
                    stock_codes = []
                    for _, row in stock_info.iterrows():
                        code = row['代码']
                        if code.startswith('000') or code.startswith('002') or code.startswith('300'):
                            stock_codes.append(f"{code}.SZ")
                        elif code.startswith('600') or code.startswith('601') or code.startswith('603'):
                            stock_codes.append(f"{code}.SH")
                
                self.logger.info(f"获取到 {len(stock_codes)} 只{market}股票")
                return stock_codes
            else:
                self.logger.warning("akshare不可用，返回默认股票列表")
                # 返回一些常见的创业板股票代码
                return [
                    "300015.SZ", "300059.SZ", "300124.SZ", "300142.SZ", 
                    "300144.SZ", "300251.SZ", "300316.SZ", "300347.SZ"
                ]
                
        except Exception as e:
            self.logger.error(f"获取股票列表失败: {e}")
            return []
    
    def test_data_sources(self, stock_code: str = "300015.SZ", date: str = None) -> Dict[str, Any]:
        """
        测试各数据源的可用性
        
        Args:
            stock_code: 测试用股票代码
            date: 测试日期
            
        Returns:
            Dict: 测试结果
        """
        if date is None:
            date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        results = {}
        
        for source in self.data_sources:
            try:
                start_time = time.time()
                data = self._get_data_from_source(source, stock_code, date)
                elapsed_time = time.time() - start_time
                
                if data is not None and not data.empty:
                    results[source] = {
                        'status': 'success',
                        'records': len(data),
                        'time': elapsed_time,
                        'columns': list(data.columns)
                    }
                else:
                    results[source] = {
                        'status': 'no_data',
                        'records': 0,
                        'time': elapsed_time,
                        'error': '返回空数据'
                    }
            except Exception as e:
                results[source] = {
                    'status': 'error',
                    'records': 0,
                    'time': 0,
                    'error': str(e)
                }
        
        return results

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试演示脚本
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from quantization.visualization.result_visualizer import ResultVisualizer, VisualizationConfig

def debug_visualization():
    """调试可视化功能"""
    print("调试可视化功能...")
    
    try:
        config = VisualizationConfig(
            style="seaborn",
            figure_size=(10, 6),
            interactive=False,
            save_format="png",
            dpi=300
        )
        
        visualizer = ResultVisualizer(config)
        
        # 生成模拟回测数据
        np.random.seed(42)
        n_days = 30
        dates = pd.date_range(start='2023-01-01', periods=n_days, freq='D')
        
        daily_returns = np.random.normal(0.001, 0.02, n_days)
        
        portfolio_history = []
        for i, date in enumerate(dates):
            portfolio_history.append({
                'date': date.strftime('%Y-%m-%d'),
                'daily_return': daily_returns[i],
                'portfolio_value': 1000000 * np.prod(1 + daily_returns[:i+1])
            })
        
        results = {
            'strategy_name': 'Debug Test Strategy',
            'start_date': '2023-01-01',
            'end_date': '2023-01-30',
            'portfolio_history': portfolio_history,
            'performance_metrics': {
                'total_return': 0.03,
                'annualized_return': 0.02,
                'annualized_volatility': 0.12,
                'sharpe_ratio': 0.17,
                'max_drawdown': -0.02
            }
        }
        
        # 测试create_backtest_report
        output_dir = "output/visualization"
        print(f"输出目录: {output_dir}")
        print(f"输出目录绝对路径: {Path(output_dir).absolute()}")
        
        # 确保目录存在
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        print(f"目录是否存在: {Path(output_dir).exists()}")
        print(f"目录是否可写: {os.access(Path(output_dir), os.W_OK)}")
        
        # 列出目录内容（调用前）
        print(f"调用前目录内容: {list(Path(output_dir).iterdir())}")
        
        report_files = visualizer.create_backtest_report(results, output_dir)
        
        print(f"报告文件: {report_files}")
        
        # 列出目录内容（调用后）
        print(f"调用后目录内容: {list(Path(output_dir).iterdir())}")
        
        # 检查文件是否真的存在
        for name, path in report_files.items():
            abs_path = Path(path).absolute()
            if Path(path).exists():
                size = Path(path).stat().st_size
                print(f"✓ {name}: {path} -> {abs_path} (大小: {size} bytes)")
            else:
                print(f"✗ {name}: {path} -> {abs_path} (文件不存在)")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("调试演示脚本")
    print("=" * 50)
    
    success = debug_visualization()
    
    if success:
        print("\n✓ 测试成功")
    else:
        print("\n✗ 测试失败")

if __name__ == "__main__":
    main()

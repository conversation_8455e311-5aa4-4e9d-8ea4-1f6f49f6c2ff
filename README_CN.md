# A股数据采集框架

专为A股回测设计的综合性数据采集框架。该框架提供强大的多数据源采集、智能缓存、数据验证和为回测系统设计的清洁接口。

## 🚀 核心特性

### 📊 **多数据源智能采集**
- **主要数据源**: akshare库API，提供可靠的A股数据
- **备用数据源**: 腾讯财经和东方财富网络爬虫
- **自动故障转移**: 当主数据源失败时无缝切换到备用源

### 💾 **全面的数据类型支持**
- **股票价格数据**: OHLCV（开高低收量）含复权处理
- **市场指数**: 上证综指、深证成指、创业板指、沪深300
- **财务数据**: 资产负债表、利润表、现金流量表
- **股票信息**: 公司详情、行业分类、市值数据

### 🏗️ **稳健的架构设计**
- **模块化设计**: 易于扩展和维护
- **线程安全**: 支持多股票并发数据获取
- **限流控制**: 遵守API限制，防止被封禁
- **错误处理**: 全面的错误处理和重试机制

### 🔧 **智能存储与缓存**
- **本地数据库**: SQLite存储，可选PostgreSQL支持
- **智能缓存**: 基于TTL的可配置缓存系统
- **数据验证**: 自动数据质量检查和清洗
- **去重处理**: 自动处理重复数据

### 📈 **回测集成优化**
- **清洁接口**: 专为回测系统设计的接口
- **数据预处理**: 技术指标、标准化、重采样
- **投资组合支持**: 多股票数据对齐和矩阵操作
- **性能优化**: 高效的数据访问模式

## 📦 安装配置

### 环境要求
```bash
# 安装必需的包
pip install pandas numpy requests beautifulsoup4 akshare
```

### 快速安装
```bash
# 运行自动安装脚本
python setup_framework.py
```

## 🎯 快速开始

### 基础使用

```python
from data_acquisition import DataManager

# 初始化数据管理器
dm = DataManager()

# 获取股票数据
data = dm.get_stock_data('000001.SZ', '2023-01-01', '2023-12-31')
print(f"获取到 {len(data)} 条记录")
print(data.head())

# 获取多只股票
stocks = ['000001.SZ', '000002.SZ', '600000.SH']
all_data = dm.get_multiple_stocks_data(stocks, '2023-01-01', '2023-12-31')

# 清理资源
dm.cleanup()
```

### 回测接口

```python
from data_acquisition import BacktestingDataInterface

# 初始化回测接口
bt = BacktestingDataInterface()

# 获取价格矩阵（用于投资组合分析）
stocks = ['000001.SZ', '000002.SZ', '600000.SH']
price_matrix = bt.get_price_matrix(stocks, '2023-01-01', '2023-12-31')

# 获取收益率矩阵
returns_matrix = bt.get_returns_matrix(stocks, '2023-01-01', '2023-12-31')

# 获取市场数据
market_data = bt.get_market_data('2023-01-01', '2023-12-31')

bt.cleanup()
```

## ⚙️ 配置说明

### 环境变量配置
```bash
# 数据库设置
export DATABASE_URL="sqlite:///data/ashare_data.db"
export CACHE_ENABLED="true"
export CACHE_TTL_DAYS="7"

# 限流设置
export AKSHARE_RATE_LIMIT="0.5"
export WEB_SCRAPER_RATE_LIMIT="1.0"
export MAX_RETRIES="3"

# 日志设置
export LOG_LEVEL="INFO"
```

### 自定义配置
```python
from data_acquisition.config import Config

# 创建自定义配置
config = Config()
config.CACHE_TTL_DAYS = 1
config.AKSHARE_RATE_LIMIT = 0.1
config.LOG_LEVEL = 'DEBUG'

# 使用自定义配置
dm = DataManager(config)
```

## 📋 股票代码格式

框架支持标准的中国股票代码格式：

- **上海证券交易所**: `600000.SH`（主板）、`688000.SH`（科创板）
- **深圳证券交易所**: `000001.SZ`（主板）、`300001.SZ`（创业板）

```python
from data_acquisition.utils import normalize_stock_code, validate_stock_codes

# 标准化代码
code = normalize_stock_code('000001')  # 返回 '000001.SZ'

# 验证多个代码
valid, invalid = validate_stock_codes(['000001', '600000.SH', 'INVALID'])
```

## 🔍 数据验证与质量控制

框架包含全面的数据验证功能：

```python
from data_acquisition.utils import DataValidator

validator = DataValidator()

# 验证价格数据
is_valid, errors = validator.validate_price_data(data, '000001.SZ')

# 清洗数据
cleaned_data = validator.clean_data(data, '000001.SZ')
```

## 📚 使用示例

### 基础功能示例
```python
# 查看基础使用示例
python examples/basic_usage_cn.py
```

### 量化策略回测
```python
# 查看策略回测示例
python examples/strategy_backtest_cn.py
```

## 🏗️ 架构说明

```
data_acquisition/
├── core/                   # 核心数据采集模块
│   ├── base_provider.py   # 抽象数据源基类
│   ├── akshare_provider.py # akshare数据源
│   ├── web_scraper.py     # 网络爬虫数据源
│   └── data_manager.py    # 中央协调器
├── storage/               # 数据存储模块
│   ├── database.py        # 数据库操作
│   └── cache_manager.py   # 缓存系统
├── utils/                 # 工具模块
│   ├── stock_codes.py     # 股票代码处理
│   ├── rate_limiter.py    # 限流控制
│   ├── data_validator.py  # 数据验证
│   └── logger.py          # 日志系统
├── config/                # 配置模块
│   └── settings.py        # 设置管理
└── backtesting_interface/ # 回测接口
    ├── data_interface.py  # 清洁数据接口
    └── data_preprocessor.py # 数据预处理
```

## 📊 性能特性

### 缓存策略
- **缓存命中率**: 重复查询通常达到80%+
- **存储效率**: 压缩pickle格式
- **TTL管理**: 可配置的过期时间

### 限流控制
- **akshare**: 请求间隔0.5秒（可配置）
- **网络爬虫**: 请求间隔1.0秒
- **并发获取**: 最多5个并行请求

### 内存使用
- **流式处理**: 大数据集分块处理
- **自动清理**: 自动资源清理
- **优化操作**: 高效的pandas操作

## 🛠️ 错误处理

框架提供强大的错误处理机制：

```python
try:
    data = dm.get_stock_data('000001.SZ', '2023-01-01', '2023-12-31')
except Exception as e:
    print(f"错误: {e}")
    # 框架会记录详细的错误信息
```

## 📝 日志系统

完善的日志系统：

```python
from data_acquisition.utils import get_logger

logger = get_logger('my_module')
logger.info("自定义日志消息")
```

日志文件存储在 `logs/` 目录，支持自动轮转。

## 🎨 使用技巧

### 1. 批量数据获取
```python
# 获取银行板块股票
bank_stocks = ['000001.SZ', '600000.SH', '600036.SH']
bank_data = dm.get_multiple_stocks_data(bank_stocks, '2023-01-01', '2023-12-31')
```

### 2. 缓存优化
```python
# 首次获取会缓存数据
data1 = dm.get_stock_data('000001.SZ', '2023-01-01', '2023-12-31', use_cache=True)

# 第二次获取使用缓存，速度更快
data2 = dm.get_stock_data('000001.SZ', '2023-01-01', '2023-12-31', use_cache=True)
```

### 3. 数据质量检查
```python
# 获取数据覆盖度信息
coverage = dm.database.get_data_coverage('000001.SZ')
print(f"数据范围: {coverage['start_date']} 到 {coverage['end_date']}")
```

## 🔧 扩展开发

### 添加新数据源
1. 继承 `BaseDataProvider` 类
2. 实现必需的抽象方法
3. 在 `DataManager` 中注册新数据源

### 自定义验证规则
1. 扩展 `DataValidator` 类
2. 添加特定的验证逻辑
3. 配置验证参数

## 📈 应用场景

### 量化研究
- 因子分析和挖掘
- 策略回测和优化
- 风险模型构建

### 投资分析
- 基本面分析
- 技术分析
- 投资组合管理

### 学术研究
- 市场微观结构研究
- 行为金融学研究
- 风险管理研究

## ⚠️ 重要提醒

- **遵守数据源使用条款**：请确保符合数据提供商的使用协议
- **合理使用限流**：避免过于频繁的请求导致IP被封
- **数据质量检查**：使用前请验证数据的准确性和完整性
- **备份重要数据**：定期备份本地数据库

## 🚀 快速测试

```bash
# 测试框架功能
python test_framework.py

# 运行基础示例
python examples/basic_usage_cn.py

# 运行策略回测示例
python examples/strategy_backtest_cn.py
```

## 📞 技术支持

如遇问题请：
1. 查看 `examples/` 目录中的示例
2. 检查 `config/settings.py` 中的配置
3. 启用调试日志获取详细信息

## 🗺️ 发展路线

- [ ] 支持更多数据源
- [ ] 实时数据流
- [ ] 高级技术指标
- [ ] 机器学习特征
- [ ] 云存储后端
- [ ] REST API接口

---

**A股数据采集框架** - 让量化投资更简单、更可靠！

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API服务器

提供RESTful API接口，支持策略管理、回测执行、数据查询等功能。
包含认证、限流、监控、文档生成等特性。
"""

import time
import json
import asyncio
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
import threading
from collections import defaultdict, deque

from quantization.utils.logger import get_logger
from quantization.config.config_manager import config_manager

# 可选依赖
try:
    from fastapi import FastAPI, HTTPException, Depends, Request, Response
    from fastapi.security import HTTP<PERSON>earer, HTTPAuthorizationCredentials
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import JSONResponse
    from pydantic import BaseModel, Field
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False


@dataclass
class APIResponse:
    """API响应格式"""
    success: bool
    data: Any = None
    message: str = ""
    error_code: Optional[str] = None
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


class RateLimiter:
    """速率限制器"""
    
    def __init__(self, max_requests: int = 1000, window_seconds: int = 3600):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests: Dict[str, deque] = defaultdict(deque)
        self.lock = threading.Lock()
        self.logger = get_logger(self.__class__.__name__)
    
    def is_allowed(self, client_id: str) -> bool:
        """检查是否允许请求"""
        with self.lock:
            now = time.time()
            window_start = now - self.window_seconds
            
            # 清理过期请求
            client_requests = self.requests[client_id]
            while client_requests and client_requests[0] < window_start:
                client_requests.popleft()
            
            # 检查是否超过限制
            if len(client_requests) >= self.max_requests:
                return False
            
            # 记录新请求
            client_requests.append(now)
            return True
    
    def get_remaining(self, client_id: str) -> int:
        """获取剩余请求数"""
        with self.lock:
            now = time.time()
            window_start = now - self.window_seconds
            
            client_requests = self.requests[client_id]
            # 清理过期请求
            while client_requests and client_requests[0] < window_start:
                client_requests.popleft()
            
            return max(0, self.max_requests - len(client_requests))


class APIMetrics:
    """API指标收集器"""
    
    def __init__(self):
        self.request_count = defaultdict(int)
        self.response_times = defaultdict(list)
        self.error_count = defaultdict(int)
        self.lock = threading.Lock()
        self.logger = get_logger(self.__class__.__name__)
    
    def record_request(self, endpoint: str, response_time: float, 
                      status_code: int):
        """记录请求指标"""
        with self.lock:
            self.request_count[endpoint] += 1
            self.response_times[endpoint].append(response_time)
            
            if status_code >= 400:
                self.error_count[endpoint] += 1
            
            # 保持最近1000个响应时间
            if len(self.response_times[endpoint]) > 1000:
                self.response_times[endpoint] = self.response_times[endpoint][-1000:]
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取指标统计"""
        with self.lock:
            metrics = {}
            
            for endpoint in self.request_count:
                response_times = self.response_times[endpoint]
                
                metrics[endpoint] = {
                    'request_count': self.request_count[endpoint],
                    'error_count': self.error_count[endpoint],
                    'error_rate': self.error_count[endpoint] / max(self.request_count[endpoint], 1),
                    'avg_response_time': sum(response_times) / max(len(response_times), 1),
                    'min_response_time': min(response_times) if response_times else 0,
                    'max_response_time': max(response_times) if response_times else 0
                }
            
            return metrics


if FASTAPI_AVAILABLE:
    # Pydantic模型定义
    class BacktestRequest(BaseModel):
        """回测请求模型"""
        strategy_name: str = Field(..., description="策略名称")
        start_date: str = Field(..., description="开始日期 (YYYY-MM-DD)")
        end_date: str = Field(..., description="结束日期 (YYYY-MM-DD)")
        initial_capital: float = Field(1000000.0, description="初始资金")
        parameters: Dict[str, Any] = Field(default_factory=dict, description="策略参数")
        benchmark: Optional[str] = Field(None, description="基准指数")
    
    class StrategyConfig(BaseModel):
        """策略配置模型"""
        name: str = Field(..., description="策略名称")
        description: str = Field("", description="策略描述")
        parameters: Dict[str, Any] = Field(default_factory=dict, description="策略参数")
        enabled: bool = Field(True, description="是否启用")
    
    class DataQuery(BaseModel):
        """数据查询模型"""
        symbol: str = Field(..., description="股票代码")
        start_date: str = Field(..., description="开始日期")
        end_date: str = Field(..., description="结束日期")
        fields: Optional[List[str]] = Field(None, description="字段列表")


class QuantizationAPI:
    """
    量化交易API服务器
    
    提供策略管理、回测执行、数据查询等RESTful API接口。
    """
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        
        if not FASTAPI_AVAILABLE:
            raise ImportError("FastAPI未安装，请运行: pip install fastapi uvicorn")
        
        # 创建FastAPI应用
        self.app = FastAPI(
            title="量化交易系统API",
            description="提供策略管理、回测执行、数据查询等功能的RESTful API",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        # 中间件
        self._setup_middleware()
        
        # 组件
        self.rate_limiter = RateLimiter(
            max_requests=config_manager.get("api.rate_limit", 1000),
            window_seconds=3600
        )
        self.metrics = APIMetrics()
        self.security = HTTPBearer()
        
        # 路由
        self._setup_routes()
        
        # 策略管理器（需要注入）
        self.strategy_manager = None
        self.backtest_engine = None
        self.data_manager = None
    
    def _setup_middleware(self):
        """设置中间件"""
        # CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 请求日志和指标
        @self.app.middleware("http")
        async def log_requests(request: Request, call_next):
            start_time = time.time()
            client_ip = request.client.host
            
            # 速率限制
            if not self.rate_limiter.is_allowed(client_ip):
                return JSONResponse(
                    status_code=429,
                    content=asdict(APIResponse(
                        success=False,
                        message="请求频率过高，请稍后再试",
                        error_code="RATE_LIMIT_EXCEEDED"
                    ))
                )
            
            # 处理请求
            response = await call_next(request)
            
            # 记录指标
            process_time = time.time() - start_time
            self.metrics.record_request(
                str(request.url.path),
                process_time,
                response.status_code
            )
            
            # 添加响应头
            response.headers["X-Process-Time"] = str(process_time)
            response.headers["X-Rate-Limit-Remaining"] = str(
                self.rate_limiter.get_remaining(client_ip)
            )
            
            return response
    
    def _setup_routes(self):
        """设置路由"""
        
        @self.app.get("/", response_model=dict)
        async def root():
            """根路径"""
            return asdict(APIResponse(
                success=True,
                data={
                    "name": "量化交易系统API",
                    "version": "1.0.0",
                    "status": "running",
                    "timestamp": time.time()
                },
                message="API服务正常运行"
            ))
        
        @self.app.get("/health", response_model=dict)
        async def health_check():
            """健康检查"""
            return asdict(APIResponse(
                success=True,
                data={
                    "status": "healthy",
                    "uptime": time.time(),
                    "components": {
                        "database": "ok",
                        "cache": "ok",
                        "data_source": "ok"
                    }
                }
            ))
        
        @self.app.get("/metrics", response_model=dict)
        async def get_metrics():
            """获取API指标"""
            return asdict(APIResponse(
                success=True,
                data=self.metrics.get_metrics()
            ))
        
        # 策略管理
        @self.app.get("/strategies", response_model=dict)
        async def list_strategies():
            """获取策略列表"""
            try:
                if not self.strategy_manager:
                    raise HTTPException(status_code=503, detail="策略管理器未初始化")
                
                strategies = self.strategy_manager.list_strategies()
                return asdict(APIResponse(
                    success=True,
                    data=strategies
                ))
            except Exception as e:
                self.logger.error(f"获取策略列表失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/strategies", response_model=dict)
        async def create_strategy(strategy: StrategyConfig):
            """创建策略"""
            try:
                if not self.strategy_manager:
                    raise HTTPException(status_code=503, detail="策略管理器未初始化")
                
                result = self.strategy_manager.create_strategy(
                    strategy.name,
                    strategy.description,
                    strategy.parameters
                )
                
                return asdict(APIResponse(
                    success=True,
                    data=result,
                    message=f"策略 {strategy.name} 创建成功"
                ))
            except Exception as e:
                self.logger.error(f"创建策略失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/strategies/{strategy_name}", response_model=dict)
        async def get_strategy(strategy_name: str):
            """获取策略详情"""
            try:
                if not self.strategy_manager:
                    raise HTTPException(status_code=503, detail="策略管理器未初始化")
                
                strategy = self.strategy_manager.get_strategy(strategy_name)
                if not strategy:
                    raise HTTPException(status_code=404, detail="策略不存在")
                
                return asdict(APIResponse(
                    success=True,
                    data=strategy
                ))
            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"获取策略详情失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.put("/strategies/{strategy_name}", response_model=dict)
        async def update_strategy(strategy_name: str, strategy: StrategyConfig):
            """更新策略"""
            try:
                if not self.strategy_manager:
                    raise HTTPException(status_code=503, detail="策略管理器未初始化")
                
                result = self.strategy_manager.update_strategy(
                    strategy_name,
                    strategy.description,
                    strategy.parameters
                )
                
                return asdict(APIResponse(
                    success=True,
                    data=result,
                    message=f"策略 {strategy_name} 更新成功"
                ))
            except Exception as e:
                self.logger.error(f"更新策略失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.delete("/strategies/{strategy_name}", response_model=dict)
        async def delete_strategy(strategy_name: str):
            """删除策略"""
            try:
                if not self.strategy_manager:
                    raise HTTPException(status_code=503, detail="策略管理器未初始化")
                
                result = self.strategy_manager.delete_strategy(strategy_name)
                
                return asdict(APIResponse(
                    success=True,
                    data=result,
                    message=f"策略 {strategy_name} 删除成功"
                ))
            except Exception as e:
                self.logger.error(f"删除策略失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # 回测管理
        @self.app.post("/backtest", response_model=dict)
        async def run_backtest(request: BacktestRequest):
            """执行回测"""
            try:
                if not self.backtest_engine:
                    raise HTTPException(status_code=503, detail="回测引擎未初始化")
                
                # 异步执行回测
                task_id = await self._run_backtest_async(request)
                
                return asdict(APIResponse(
                    success=True,
                    data={"task_id": task_id},
                    message="回测任务已提交"
                ))
            except Exception as e:
                self.logger.error(f"执行回测失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/backtest/{task_id}", response_model=dict)
        async def get_backtest_result(task_id: str):
            """获取回测结果"""
            try:
                if not self.backtest_engine:
                    raise HTTPException(status_code=503, detail="回测引擎未初始化")
                
                result = self.backtest_engine.get_result(task_id)
                if not result:
                    raise HTTPException(status_code=404, detail="回测任务不存在")
                
                return asdict(APIResponse(
                    success=True,
                    data=result
                ))
            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"获取回测结果失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # 数据查询
        @self.app.post("/data/query", response_model=dict)
        async def query_data(query: DataQuery):
            """查询数据"""
            try:
                if not self.data_manager:
                    raise HTTPException(status_code=503, detail="数据管理器未初始化")
                
                data = self.data_manager.get_stock_data(
                    query.symbol,
                    query.start_date,
                    query.end_date,
                    query.fields
                )
                
                return asdict(APIResponse(
                    success=True,
                    data=data.to_dict('records') if hasattr(data, 'to_dict') else data
                ))
            except Exception as e:
                self.logger.error(f"查询数据失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # 配置管理
        @self.app.get("/config", response_model=dict)
        async def get_config():
            """获取配置"""
            try:
                config = config_manager.get_all_configs()
                return asdict(APIResponse(
                    success=True,
                    data=config
                ))
            except Exception as e:
                self.logger.error(f"获取配置失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.put("/config/{key}", response_model=dict)
        async def update_config(key: str, value: dict):
            """更新配置"""
            try:
                success = config_manager.set(key, value.get('value'))
                
                return asdict(APIResponse(
                    success=success,
                    message=f"配置 {key} 更新{'成功' if success else '失败'}"
                ))
            except Exception as e:
                self.logger.error(f"更新配置失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
    
    async def _run_backtest_async(self, request: BacktestRequest) -> str:
        """异步执行回测"""
        # 这里应该实现异步回测逻辑
        # 返回任务ID
        import uuid
        task_id = str(uuid.uuid4())
        
        # 实际实现中应该将任务提交到队列或线程池
        self.logger.info(f"提交回测任务 {task_id}: {request.strategy_name}")
        
        return task_id
    
    def set_strategy_manager(self, strategy_manager):
        """设置策略管理器"""
        self.strategy_manager = strategy_manager
    
    def set_backtest_engine(self, backtest_engine):
        """设置回测引擎"""
        self.backtest_engine = backtest_engine
    
    def set_data_manager(self, data_manager):
        """设置数据管理器"""
        self.data_manager = data_manager
    
    def run(self, host: str = None, port: int = None, debug: bool = None):
        """运行API服务器"""
        host = host or config_manager.get("api.host", "0.0.0.0")
        port = port or config_manager.get("api.port", 8000)
        debug = debug or config_manager.get("api.debug", False)
        
        self.logger.info(f"启动API服务器: http://{host}:{port}")
        
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            debug=debug,
            access_log=True
        )


# 全局API实例
try:
    api_server = QuantizationAPI() if FASTAPI_AVAILABLE else None
except Exception as e:
    api_server = None
    print(f"API服务器初始化失败: {e}")

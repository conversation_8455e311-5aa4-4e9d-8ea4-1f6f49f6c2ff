#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创业板动态因子策略测试脚本
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.quantization.strategies.chinext_dynamic_factor_strategy import ChiNextDynamicFactorStrategy


def main():
    """主测试函数"""
    try:
        print("=" * 60)
        print("创业板动态因子量化交易策略测试")
        print("=" * 60)
        
        # 策略配置
        config = {
            # BaseStrategy需要的基础参数
            'max_stocks': 10,
            'min_market_cap': 15e8,      # 最小市值15亿元
            'max_market_cap': 250e8,     # 最大市值250亿元
            'rebalance_frequency': 'daily',
            'risk_level': 'medium',

            # 创业板策略特定参数
            'market_cap_min': 15e8,      # 最小市值15亿元
            'market_cap_max': 250e8,     # 最大市值250亿元
            'volume_ratio_min': 2.0,     # 量比 > 2
            'dde_net_ratio_min': 0.5,    # DDE大单净量 > 0.5%
            'turnover_rate_min': 4.0,    # 换手率 > 4%
            'turnover_growth_min': 0.0,  # 换手率环比增长 > 0

            # 价格特征参数
            'limit_up_days': 60,         # 近60个交易日
            'min_limit_up_count': 1,     # 至少1次涨停

            # 交易触发参数
            'market_volume_threshold': 1.5e12,  # 1.5万亿元成交额阈值

            # 资金管理参数
            'initial_capital': 1000000,  # 初始资金100万元
            'max_positions': 10,         # 最大持仓数量
            'position_size': 100000,     # 单个标的建仓资金10万元
            'max_position_ratio': 0.1,   # 单只标的最大仓位10%

            # 交易时点参数
            'buy_time_before_close': 30,  # 收盘前30分钟买入
            'sell_time_after_open': 30,   # 开盘后30分钟卖出

            # 数据缓存参数
            'cache_dir': 'strategy_cache',
            'use_cache': True,
        }
        
        print("1. 创建策略实例...")
        strategy = ChiNextDynamicFactorStrategy(config)
        
        print("2. 初始化策略...")
        strategy.initialize(config)
        
        print("3. 策略参数:")
        for key, value in config.items():
            if isinstance(value, float) and value > 1e6:
                print(f"   {key}: {value/1e8:.1f}亿" if value > 1e8 else f"   {key}: {value/1e4:.1f}万")
            else:
                print(f"   {key}: {value}")
        
        print("\n4. 测试选股功能...")
        test_date = "2024-01-15"
        print(f"   测试日期: {test_date}")
        
        # 生成交易信号
        print("   正在生成交易信号...")
        signals = strategy.generate_signals(test_date)
        
        print("\n5. 交易信号结果:")
        print(f"   买入信号数量: {len(signals['buy_signals'])}")
        print(f"   卖出信号数量: {len(signals['sell_signals'])}")
        print(f"   市场交易条件: {'满足' if signals['market_condition'] else '不满足'}")
        print(f"   信号生成耗时: {signals['generation_time']:.2f}秒")
        
        if signals['buy_signals']:
            print("\n   买入信号详情:")
            for i, signal in enumerate(signals['buy_signals'][:5], 1):  # 只显示前5个
                print(f"   {i}. {signal['stock_code']} - 数量: {signal['quantity']}股")
        
        print("\n6. 性能统计:")
        stats = strategy.get_performance_stats()
        for key, value in stats['performance_stats'].items():
            if isinstance(value, float):
                print(f"   {key}: {value:.3f}秒")
            else:
                print(f"   {key}: {value}")
        
        print(f"\n   创业板股票池大小: {stats['chinext_stocks_count']}只")
        
        print("\n7. 清理资源...")
        strategy.cleanup()
        
        print("\n" + "=" * 60)
        print("测试完成!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用模拟数据测试缓存功能
"""

import sys
import os
import time
import pandas as pd
import sqlite3
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.quantization.strategies.chinext_minute_data_downloader import ChinextMinuteDataDownloader

def create_mock_minute_data(stock_code: str, date: str) -> pd.DataFrame:
    """创建模拟分时数据"""
    # 生成交易时间段
    morning_start = pd.to_datetime(f"{date} 09:30:00")
    morning_end = pd.to_datetime(f"{date} 11:30:00")
    afternoon_start = pd.to_datetime(f"{date} 13:00:00")
    afternoon_end = pd.to_datetime(f"{date} 15:00:00")
    
    # 生成时间序列
    morning_times = pd.date_range(morning_start, morning_end, freq='1min')[:-1]  # 排除11:30
    afternoon_times = pd.date_range(afternoon_start, afternoon_end, freq='1min')
    
    all_times = morning_times.append(afternoon_times)
    
    # 生成模拟价格数据
    base_price = 10.0
    data = []
    
    for i, dt in enumerate(all_times):
        # 简单的价格波动模拟
        price_change = (i % 10 - 5) * 0.01
        open_price = base_price + price_change
        high_price = open_price + abs(price_change) * 0.5
        low_price = open_price - abs(price_change) * 0.5
        close_price = open_price + price_change * 0.3
        
        data.append({
            'datetime': dt,
            'open': round(open_price, 2),
            'high': round(high_price, 2),
            'low': round(low_price, 2),
            'close': round(close_price, 2),
            'volume': 1000 + i * 10,
            'amount': (1000 + i * 10) * close_price
        })
    
    return pd.DataFrame(data)

def setup_mock_data_in_db(downloader: ChinextMinuteDataDownloader, stock_code: str, date: str):
    """在数据库中设置模拟数据"""
    mock_data = create_mock_minute_data(stock_code, date)
    
    # 直接保存到数据库
    conn = sqlite3.connect(downloader.db_path)
    cursor = conn.cursor()
    
    for _, row in mock_data.iterrows():
        cursor.execute("""
            INSERT OR REPLACE INTO minute_data
            (stock_code, date, time, open, high, low, close, volume, amount)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            stock_code, date, row['datetime'].strftime('%H:%M:%S'),
            row['open'], row['high'], row['low'], row['close'],
            row['volume'], row['amount']
        ))
    
    conn.commit()
    conn.close()
    
    print(f"已在数据库中设置 {stock_code} {date} 的模拟数据，共 {len(mock_data)} 条记录")

def test_cache_with_existing_data():
    """测试从数据库加载数据到缓存的功能"""
    print("=" * 60)
    print("缓存功能测试（使用数据库中的现有数据）")
    print("=" * 60)
    
    downloader = ChinextMinuteDataDownloader()
    downloader.clear_cache()
    
    test_stock = "300001.SZ"
    test_date = "2024-06-27"
    
    # 在数据库中设置模拟数据
    setup_mock_data_in_db(downloader, test_stock, test_date)
    
    print(f"测试股票: {test_stock}")
    print(f"测试日期: {test_date}")
    print()
    
    # 第一次调用（应该从数据库加载到缓存）
    print("第一次调用（从数据库加载到缓存）...")
    start_time = time.time()
    success1, count1, msg1 = downloader.download_stock_minute_data(test_stock, test_date)
    time1 = time.time() - start_time
    
    print(f"  结果: {'成功' if success1 else '失败'}")
    print(f"  记录数: {count1}")
    print(f"  消息: {msg1}")
    print(f"  耗时: {time1:.4f}秒")
    
    cache_stats = downloader.get_cache_stats()
    print("  缓存统计:")
    for key, value in cache_stats.items():
        print(f"    {key}: {value}")
    print()
    
    # 第二次调用（应该从缓存获取）
    print("第二次调用（从缓存获取）...")
    start_time = time.time()
    success2, count2, msg2 = downloader.download_stock_minute_data(test_stock, test_date)
    time2 = time.time() - start_time
    
    print(f"  结果: {'成功' if success2 else '失败'}")
    print(f"  记录数: {count2}")
    print(f"  消息: {msg2}")
    print(f"  耗时: {time2:.4f}秒")
    
    cache_stats = downloader.get_cache_stats()
    print("  缓存统计:")
    for key, value in cache_stats.items():
        print(f"    {key}: {value}")
    print()
    
    # 性能对比
    if time1 > 0 and time2 > 0:
        speedup = time1 / time2 if time2 > 0 else float('inf')
        time_saved = ((time1 - time2) / time1 * 100) if time1 > 0 else 0
        print(f"性能提升: {speedup:.1f}倍")
        print(f"时间节省: {time_saved:.1f}%")
    
    # 验证缓存命中
    cache_hit = "缓存命中" in msg2
    print(f"缓存命中验证: {'✓' if cache_hit else '✗'}")
    print()
    
    return success1 and success2 and cache_hit

def test_cache_memory_efficiency():
    """测试缓存内存效率"""
    print("=" * 60)
    print("缓存内存效率测试")
    print("=" * 60)
    
    downloader = ChinextMinuteDataDownloader()
    downloader.clear_cache()
    
    # 测试多个股票的缓存
    test_stocks = [f"30000{i}.SZ" for i in range(1, 6)]
    test_date = "2024-06-27"
    
    print(f"测试股票: {test_stocks}")
    print(f"测试日期: {test_date}")
    print()
    
    # 为每个股票设置数据并测试缓存
    for i, stock in enumerate(test_stocks):
        setup_mock_data_in_db(downloader, stock, test_date)
        
        # 第一次调用（加载到缓存）
        success, count, msg = downloader.download_stock_minute_data(stock, test_date)
        
        cache_stats = downloader.get_cache_stats()
        print(f"{stock}: 成功={success}, 记录数={count}, 缓存大小={cache_stats['cache_size']}")
        
        # 第二次调用（从缓存获取）
        success2, count2, msg2 = downloader.download_stock_minute_data(stock, test_date)
        cache_hit = "缓存命中" in msg2
        print(f"  第二次调用: 缓存命中={'✓' if cache_hit else '✗'}")
    
    # 最终缓存统计
    final_stats = downloader.get_cache_stats()
    print()
    print("最终缓存统计:")
    for key, value in final_stats.items():
        print(f"  {key}: {value}")
    
    # 验证缓存命中率
    hit_rate_good = final_stats['hit_rate'] > 0.4  # 至少40%命中率
    print(f"缓存命中率验证: {'✓' if hit_rate_good else '✗'} ({final_stats['hit_rate']:.1%})")
    print()
    
    return hit_rate_good

def main():
    """主测试函数"""
    print("开始缓存功能测试（使用模拟数据）...")
    print()
    
    try:
        # 测试缓存基本功能
        test1_result = test_cache_with_existing_data()
        print(f"✓ 缓存基本功能测试: {'通过' if test1_result else '失败'}")
        print()
        
        # 测试缓存内存效率
        test2_result = test_cache_memory_efficiency()
        print(f"✓ 缓存内存效率测试: {'通过' if test2_result else '失败'}")
        print()
        
        # 总结
        all_passed = test1_result and test2_result
        print("=" * 60)
        print(f"缓存功能测试结果: {'全部通过' if all_passed else '部分失败'}")
        print("=" * 60)
        
        if all_passed:
            print("✅ 缓存系统工作正常，优化效果显著")
            print("主要改进:")
            print("  - 内存缓存机制，显著提升重复访问速度")
            print("  - 智能缓存过期管理")
            print("  - 缓存大小限制，防止内存溢出")
            print("  - 数据库与缓存的无缝集成")
        else:
            print("❌ 缓存系统存在问题，需要进一步调试")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

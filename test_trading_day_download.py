#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易日数据下载测试脚本
验证系统在真实交易环境下的表现
"""

import sys
import os
import time
import pandas as pd
from datetime import datetime, timedelta
import akshare as ak

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.quantization.strategies.chinext_minute_data_downloader import ChinextMinuteDataDownloader

def get_recent_trading_days(days=5):
    """获取最近的交易日"""
    try:
        # 获取交易日历
        today = datetime.now()
        start_date = (today - timedelta(days=30)).strftime('%Y%m%d')
        end_date = today.strftime('%Y%m%d')
        
        # 使用akshare获取交易日历
        trade_cal = ak.stock_zh_a_hist_min_em(
            symbol="000001",  # 使用平安银行作为参考
            start_date=start_date,
            end_date=end_date,
            period="1",
            adjust=""
        )
        
        if not trade_cal.empty:
            # 获取最近的交易日
            dates = pd.to_datetime(trade_cal['时间']).dt.date.unique()
            recent_dates = sorted(dates, reverse=True)[:days]
            return [d.strftime('%Y-%m-%d') for d in recent_dates]
        
    except Exception as e:
        print(f"获取交易日历失败: {e}")
    
    # 如果获取失败，返回最近几个工作日（可能包含非交易日）
    trading_days = []
    current_date = datetime.now()
    
    while len(trading_days) < days:
        # 跳过周末
        if current_date.weekday() < 5:  # 0-4 是周一到周五
            trading_days.append(current_date.strftime('%Y-%m-%d'))
        current_date -= timedelta(days=1)
    
    return trading_days

def test_single_stock_download():
    """测试单只股票下载"""
    print("=" * 60)
    print("单只股票下载测试")
    print("=" * 60)
    
    downloader = ChinextMinuteDataDownloader()
    
    # 测试股票（创业板）
    test_stocks = ["300001.SZ", "300002.SZ", "300003.SZ"]
    trading_days = get_recent_trading_days(3)
    
    print(f"测试股票: {test_stocks}")
    print(f"测试日期: {trading_days}")
    print()
    
    results = []
    
    for stock in test_stocks:
        for date in trading_days:
            print(f"下载 {stock} {date}...")
            start_time = time.time()
            
            success, count, msg = downloader.download_stock_minute_data(stock, date)
            
            elapsed_time = time.time() - start_time
            
            result = {
                'stock': stock,
                'date': date,
                'success': success,
                'count': count,
                'message': msg,
                'time': elapsed_time
            }
            results.append(result)
            
            status = "✓" if success else "✗"
            print(f"  {status} 结果: {'成功' if success else '失败'}")
            print(f"    记录数: {count}")
            print(f"    耗时: {elapsed_time:.2f}秒")
            if not success:
                print(f"    错误: {msg}")
            print()
    
    # 统计结果
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r['success'])
    success_rate = successful_tests / total_tests if total_tests > 0 else 0
    
    print("下载结果统计:")
    print(f"  总测试数: {total_tests}")
    print(f"  成功数: {successful_tests}")
    print(f"  成功率: {success_rate:.1%}")
    
    if successful_tests > 0:
        successful_results = [r for r in results if r['success']]
        avg_time = sum(r['time'] for r in successful_results) / len(successful_results)
        avg_records = sum(r['count'] for r in successful_results) / len(successful_results)
        print(f"  平均耗时: {avg_time:.2f}秒")
        print(f"  平均记录数: {avg_records:.0f}")
    
    print()
    return success_rate > 0.5  # 至少50%成功率

def test_batch_download():
    """测试批量下载"""
    print("=" * 60)
    print("批量下载测试")
    print("=" * 60)
    
    downloader = ChinextMinuteDataDownloader()
    
    # 测试股票列表
    test_stocks = ["300001.SZ", "300002.SZ", "300015.SZ", "300033.SZ"]
    trading_days = get_recent_trading_days(2)  # 最近2个交易日
    
    print(f"测试股票: {test_stocks}")
    print(f"测试日期: {trading_days}")
    print()
    
    # 批量下载
    print("开始批量下载...")
    start_time = time.time()

    result = downloader.batch_download_minute_data(test_stocks, trading_days)

    elapsed_time = time.time() - start_time

    total_count = result['total']
    success_count = result['success']
    failed_count = result['failed']
    success_rate = result['success_rate']

    print(f"批量下载完成:")
    print(f"  成功: {success_count}/{total_count}")
    print(f"  成功率: {success_rate:.1%}")
    print(f"  总耗时: {elapsed_time:.2f}秒")
    print(f"  平均每个任务: {elapsed_time/total_count:.2f}秒")
    print()

    # 获取下载统计信息
    stats = downloader.get_download_statistics()
    print("下载统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")

    print()
    return success_rate > 0.5

def test_cache_performance():
    """测试缓存性能"""
    print("=" * 60)
    print("缓存性能测试")
    print("=" * 60)
    
    downloader = ChinextMinuteDataDownloader()
    
    test_stock = "300001.SZ"
    test_date = get_recent_trading_days(1)[0]
    
    print(f"测试股票: {test_stock}")
    print(f"测试日期: {test_date}")
    print()
    
    # 清空缓存
    downloader.clear_cache()
    
    # 第一次下载
    print("第一次下载（无缓存）...")
    start_time = time.time()
    success1, count1, msg1 = downloader.download_stock_minute_data(test_stock, test_date)
    time1 = time.time() - start_time
    
    print(f"  结果: {'成功' if success1 else '失败'}")
    print(f"  记录数: {count1}")
    print(f"  耗时: {time1:.4f}秒")
    
    if not success1:
        print(f"  错误: {msg1}")
        return False
    
    # 第二次下载（使用缓存）
    print("第二次下载（使用缓存）...")
    start_time = time.time()
    success2, count2, msg2 = downloader.download_stock_minute_data(test_stock, test_date)
    time2 = time.time() - start_time
    
    print(f"  结果: {'成功' if success2 else '失败'}")
    print(f"  记录数: {count2}")
    print(f"  耗时: {time2:.4f}秒")
    print(f"  消息: {msg2}")
    
    # 性能对比
    if time1 > 0 and time2 > 0:
        speedup = time1 / time2 if time2 > 0 else float('inf')
        time_saved = ((time1 - time2) / time1 * 100) if time1 > 0 else 0
        print(f"  性能提升: {speedup:.1f}倍")
        print(f"  时间节省: {time_saved:.1f}%")
    
    # 缓存统计
    cache_stats = downloader.get_cache_stats()
    print("  缓存统计:")
    for key, value in cache_stats.items():
        print(f"    {key}: {value}")
    
    print()
    return success1 and success2 and "缓存命中" in msg2

def test_error_handling():
    """测试错误处理"""
    print("=" * 60)
    print("错误处理测试")
    print("=" * 60)
    
    downloader = ChinextMinuteDataDownloader()
    
    # 测试无效股票代码
    print("测试无效股票代码...")
    success, count, msg = downloader.download_stock_minute_data("999999.SZ", "2024-06-27")
    print(f"  结果: {'成功' if success else '失败'}")
    print(f"  消息: {msg}")
    print()
    
    # 测试无效日期
    print("测试无效日期...")
    success, count, msg = downloader.download_stock_minute_data("300001.SZ", "2024-02-30")
    print(f"  结果: {'成功' if success else '失败'}")
    print(f"  消息: {msg}")
    print()
    
    # 测试非交易日
    print("测试非交易日（周末）...")
    success, count, msg = downloader.download_stock_minute_data("300001.SZ", "2024-06-29")  # 周六
    print(f"  结果: {'成功' if success else '失败'}")
    print(f"  消息: {msg}")
    print()
    
    return True  # 错误处理测试总是通过

def main():
    """主测试函数"""
    print("开始交易日数据下载测试...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查是否为交易时间
    now = datetime.now()
    is_weekend = now.weekday() >= 5
    is_trading_hours = 9 <= now.hour <= 15
    
    if is_weekend:
        print("⚠️  当前为周末，可能无法获取实时数据")
        print("   建议在交易日（周一至周五）运行此测试")
    elif not is_trading_hours:
        print("⚠️  当前非交易时间，可能无法获取最新数据")
        print("   交易时间：09:00-15:00")
    else:
        print("✓ 当前为交易时间，可以获取实时数据")
    
    print()
    
    try:
        # 测试单只股票下载
        test1_result = test_single_stock_download()
        print(f"✓ 单只股票下载测试: {'通过' if test1_result else '失败'}")
        print()
        
        # 测试批量下载
        test2_result = test_batch_download()
        print(f"✓ 批量下载测试: {'通过' if test2_result else '失败'}")
        print()
        
        # 测试缓存性能
        test3_result = test_cache_performance()
        print(f"✓ 缓存性能测试: {'通过' if test3_result else '失败'}")
        print()
        
        # 测试错误处理
        test4_result = test_error_handling()
        print(f"✓ 错误处理测试: {'通过' if test4_result else '失败'}")
        print()
        
        # 总结
        all_passed = test1_result and test2_result and test3_result and test4_result
        print("=" * 60)
        print(f"交易日数据下载测试结果: {'全部通过' if all_passed else '部分失败'}")
        print("=" * 60)
        
        if all_passed:
            print("✅ 系统在交易日环境下工作正常")
        else:
            print("❌ 系统存在问题，需要进一步调试")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

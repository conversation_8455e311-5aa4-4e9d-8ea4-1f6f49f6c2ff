#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创业板动态因子量化交易策略

实现基于多因子模型的创业板短期交易策略，包括：
1. 标的筛选规则：创业板非ST个股，市值15-250亿元
2. 技术因子：量能指标、价格特征
3. 交易触发机制：基于市场成交额的风险控制
4. 资金管理：等分10份，分散化建仓
5. 策略执行：收盘前30分钟买入，次日开盘后30分钟卖出
"""

import sys
from pathlib import Path
import sqlite3
import time
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Any, Union
import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import akshare as ak
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))
src_path = Path(__file__).parent.parent.parent
sys.path.insert(0, str(src_path))

try:
    from data_acquisition import DataManager
    from data_acquisition.utils.technical_indicators import TechnicalIndicators
except ImportError:
    # 如果导入失败，创建简单的替代类
    class DataManager:
        def get_stock_data(self, stock_code, start_date, end_date):
            return None
        def get_stock_info(self, stock_code):
            return None

    class TechnicalIndicators:
        pass

try:
    from quantization.strategies.base import BaseStrategy
except ImportError:
    # 创建简单的基类
    class BaseStrategy:
        def __init__(self, name, config=None):
            self.name = name
            self.config = config or {}
            import logging
            self.logger = logging.getLogger(name)
            logging.basicConfig(level=logging.INFO)

        def initialize(self, config):
            pass

        def _get_default_parameters(self):
            return {}

        def _initialize_strategy(self):
            pass


class ChiNextDynamicFactorStrategy(BaseStrategy):
    """
    创业板动态因子量化交易策略
    
    策略特点：
    - 多因子选股模型
    - 动态市场风险控制
    - 短期持仓策略（隔夜持仓）
    - 精确的交易时点控制
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化策略
        
        Args:
            config: 策略配置参数
        """
        super().__init__("创业板动态因子策略", config)
        
        # 数据管理器
        self.data_manager = DataManager()
        self.indicators = TechnicalIndicators()
        
        # 策略参数
        self.strategy_params = {
            # 标的筛选参数
            'market_cap_min': 15e8,      # 最小市值15亿元
            'market_cap_max': 250e8,     # 最大市值250亿元
            'volume_ratio_min': 2.0,     # 量比 > 2
            'dde_net_ratio_min': 0.5,    # DDE大单净量 > 0.5%
            'turnover_rate_min': 4.0,    # 换手率 > 4%
            'turnover_growth_min': 0.0,  # 换手率环比增长 > 0
            
            # 价格特征参数
            'limit_up_days': 60,         # 近60个交易日
            'min_limit_up_count': 1,     # 至少1次涨停
            
            # 交易触发参数
            'market_volume_threshold': 1.5e12,  # 1.5万亿元成交额阈值
            
            # 资金管理参数
            'initial_capital': 1000000,  # 初始资金100万元
            'max_positions': 10,         # 最大持仓数量
            'position_size': 100000,     # 单个标的建仓资金10万元
            'max_position_ratio': 0.1,   # 单只标的最大仓位10%
            
            # 交易时点参数
            'buy_time_before_close': 30,  # 收盘前30分钟买入
            'sell_time_after_open': 30,   # 开盘后30分钟卖出
            
            # 数据缓存参数
            'cache_dir': 'strategy_cache',
            'use_cache': True,
        }
        
        # 更新参数
        self.strategy_params.update(self.config)
        
        # 初始化缓存目录
        self.cache_dir = Path(self.strategy_params['cache_dir'])
        self.cache_dir.mkdir(exist_ok=True)
        
        # 数据库路径
        self.db_path = self.cache_dir / "chinext_strategy.db"
        
        # 创业板股票池
        self.chinext_stocks = []
        
        # 性能统计
        self.performance_stats = {
            'data_load_time': 0.0,
            'screening_time': 0.0,
            'signal_generation_time': 0.0,
            'total_time': 0.0
        }
        
        self.logger.info(f"创业板动态因子策略初始化完成")
        self.logger.info(f"策略参数: {self.strategy_params}")
    
    def _get_default_parameters(self) -> Dict[str, Any]:
        """获取默认参数"""
        return {
            'max_stocks': 10,
            'rebalance_frequency': 'daily',
            'risk_level': 'medium'
        }
    
    def _initialize_strategy(self) -> None:
        """策略特定初始化"""
        try:
            # 初始化数据库
            self._init_database()
            
            # 获取创业板股票列表
            self._load_chinext_stocks()
            
            self.logger.info(f"策略初始化完成，创业板股票数量: {len(self.chinext_stocks)}")
            
        except Exception as e:
            self.logger.error(f"策略初始化失败: {str(e)}")
            raise
    
    def _init_database(self) -> None:
        """初始化数据库表结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建股票基础数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS stock_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    date TEXT NOT NULL,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    volume REAL,
                    amount REAL,
                    market_cap REAL,
                    turnover_rate REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, date)
                )
            """)
            
            # 创建技术指标表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS technical_indicators (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    date TEXT NOT NULL,
                    volume_ratio REAL,
                    dde_net_ratio REAL,
                    turnover_growth REAL,
                    williams_r REAL,
                    rsi REAL,
                    has_limit_up INTEGER DEFAULT 0,
                    limit_up_count_60d INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, date)
                )
            """)
            
            # 创建市场数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS market_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL UNIQUE,
                    sh_volume REAL,
                    sz_volume REAL,
                    total_volume REAL,
                    trading_enabled INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建交易信号表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS trading_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    date TEXT NOT NULL,
                    signal_type TEXT NOT NULL,
                    quantity INTEGER DEFAULT 0,
                    price REAL DEFAULT 0.0,
                    signal_strength REAL,
                    market_cap REAL,
                    volume_ratio REAL,
                    dde_net_ratio REAL,
                    turnover_rate REAL,
                    reason TEXT DEFAULT '',
                    priority INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, date, signal_type)
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_stock_data_code_date ON stock_data(stock_code, date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_indicators_code_date ON technical_indicators(stock_code, date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_market_data_date ON market_data(date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_signals_code_date ON trading_signals(stock_code, date)")
            
            conn.commit()
            conn.close()
            
            self.logger.info("数据库初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {str(e)}")
            raise
    
    def _load_chinext_stocks(self) -> None:
        """加载创业板股票列表"""
        try:
            # 尝试从akshare获取创业板股票列表
            chinext_df = ak.stock_zh_a_spot_em()
            
            # 筛选创业板股票（代码以300开头）
            chinext_stocks = chinext_df[
                (chinext_df['代码'].str.startswith('300')) &
                (~chinext_df['名称'].str.contains('ST'))  # 排除ST股票
            ]['代码'].tolist()
            
            # 转换为标准格式
            self.chinext_stocks = [f"{code}.SZ" for code in chinext_stocks]
            
            self.logger.info(f"成功获取创业板股票 {len(self.chinext_stocks)} 只")
            
        except Exception as e:
            self.logger.warning(f"获取创业板股票列表失败: {e}，使用默认列表")
            # 使用默认的创业板股票列表
            self.chinext_stocks = [
                '300001.SZ', '300002.SZ', '300008.SZ', '300015.SZ', '300029.SZ',
                '300033.SZ', '300059.SZ', '300124.SZ', '300144.SZ', '300251.SZ',
                '300274.SZ', '300296.SZ', '300347.SZ', '300408.SZ', '300413.SZ',
                '300450.SZ', '300498.SZ', '300529.SZ', '300558.SZ', '300601.SZ',
                '300628.SZ', '300661.SZ', '300699.SZ', '300750.SZ', '300760.SZ'
            ]

    def _execute_selection(self, date: Union[str, date, datetime]) -> List[str]:
        """
        执行选股逻辑

        Args:
            date: 选股日期

        Returns:
            选中的股票代码列表
        """
        start_time = time.time()

        try:
            # 转换日期格式
            if isinstance(date, str):
                date_str = date
            elif isinstance(date, datetime):
                date_str = date.strftime('%Y-%m-%d')
            else:  # date object
                date_str = date.strftime('%Y-%m-%d')

            self.logger.info(f"开始执行选股逻辑，日期: {date_str}")

            # 1. 检查市场交易触发条件
            if not self._check_market_trading_condition(date_str):
                self.logger.info(f"市场成交额未达到阈值，不进行交易")
                return []

            # 2. 获取候选股票池
            candidate_stocks = self._get_candidate_stocks(date_str)
            if not candidate_stocks:
                self.logger.warning(f"未找到候选股票")
                return []

            # 3. 批量筛选股票
            selected_stocks = self._batch_screen_stocks(candidate_stocks, date_str)

            # 4. 按信号强度排序
            if selected_stocks:
                selected_stocks = self._rank_stocks_by_signal_strength(selected_stocks, date_str)

            # 5. 限制选股数量
            max_stocks = self.strategy_params['max_positions']
            if len(selected_stocks) > max_stocks:
                selected_stocks = selected_stocks[:max_stocks]

            screening_time = time.time() - start_time
            self.performance_stats['screening_time'] = screening_time

            self.logger.info(f"选股完成，耗时: {screening_time:.2f}秒，选中股票: {len(selected_stocks)}只")

            return selected_stocks

        except Exception as e:
            self.logger.error(f"选股执行失败: {str(e)}")
            return []

    def _check_market_trading_condition(self, date: str) -> bool:
        """
        检查市场交易触发条件

        Args:
            date: 交易日期

        Returns:
            是否满足交易条件
        """
        try:
            # 先从数据库查询
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT total_volume, trading_enabled
                FROM market_data
                WHERE date = ?
            """, (date,))

            result = cursor.fetchone()
            conn.close()

            if result:
                _, trading_enabled = result
                return bool(trading_enabled)

            # 如果数据库中没有数据，则获取市场数据
            market_volume = self._get_market_volume(date)

            # 判断是否达到交易阈值
            trading_enabled = market_volume >= self.strategy_params['market_volume_threshold']

            # 保存到数据库
            self._save_market_data(date, market_volume, trading_enabled)

            return trading_enabled

        except Exception as e:
            self.logger.error(f"检查市场交易条件失败: {str(e)}")
            return False

    def _get_market_volume(self, date: str) -> float:
        """
        获取市场成交额

        Args:
            date: 日期

        Returns:
            市场总成交额（元）
        """
        try:
            # 获取沪深两市成交额
            sh_volume = 0.0
            sz_volume = 0.0

            # 尝试获取上证指数成交额
            try:
                sh_data = ak.stock_zh_index_daily(symbol="sh000001")
                if not sh_data.empty:
                    # 检查数据字段
                    self.logger.debug(f"上证数据字段: {sh_data.columns.tolist()}")
                    sh_data['date'] = pd.to_datetime(sh_data['date']).dt.strftime('%Y-%m-%d')
                    sh_row = sh_data[sh_data['date'] == date]
                    if not sh_row.empty:
                        # 尝试不同的成交额字段名
                        volume_fields = ['amount', 'volume', 'vol', 'turnover', '成交额']
                        for field in volume_fields:
                            if field in sh_row.columns:
                                raw_value = float(sh_row[field].iloc[0])
                                # akshare返回的成交额通常已经是以元为单位，无需转换
                                # 如果数值过小（小于1000万），可能是以万元为单位，需要转换
                                if raw_value < 1e7:  # 小于1000万，可能是万元单位
                                    sh_volume = raw_value * 1e4  # 万元转元
                                else:
                                    sh_volume = raw_value  # 已经是元单位
                                break
                        else:
                            # 如果没有找到成交额字段，使用估算值
                            sh_volume = 8000e8  # 8000亿元估算值
                            self.logger.warning("未找到上证成交额字段，使用估算值")
            except Exception as e:
                self.logger.warning(f"获取上证成交额失败: {e}")
                sh_volume = 8000e8  # 使用估算值

            # 尝试获取深证成指成交额
            try:
                sz_data = ak.stock_zh_index_daily(symbol="sz399001")
                if not sz_data.empty:
                    # 检查数据字段
                    self.logger.debug(f"深证数据字段: {sz_data.columns.tolist()}")
                    sz_data['date'] = pd.to_datetime(sz_data['date']).dt.strftime('%Y-%m-%d')
                    sz_row = sz_data[sz_data['date'] == date]
                    if not sz_row.empty:
                        # 尝试不同的成交额字段名
                        volume_fields = ['amount', 'volume', 'vol', 'turnover', '成交额']
                        for field in volume_fields:
                            if field in sz_row.columns:
                                raw_value = float(sz_row[field].iloc[0])
                                # akshare返回的成交额通常已经是以元为单位，无需转换
                                # 如果数值过小（小于1000万），可能是以万元为单位，需要转换
                                if raw_value < 1e7:  # 小于1000万，可能是万元单位
                                    sz_volume = raw_value * 1e4  # 万元转元
                                else:
                                    sz_volume = raw_value  # 已经是元单位
                                break
                        else:
                            # 如果没有找到成交额字段，使用估算值
                            sz_volume = 7000e8  # 7000亿元估算值
                            self.logger.warning("未找到深证成交额字段，使用估算值")
            except Exception as e:
                self.logger.warning(f"获取深证成交额失败: {e}")
                sz_volume = 7000e8  # 使用估算值

            total_volume = sh_volume + sz_volume

            self.logger.info(f"市场成交额 - 上证: {sh_volume/1e8:.2f}亿元, 深证: {sz_volume/1e8:.2f}亿元, 总计: {total_volume/1e8:.2f}亿元")

            return total_volume

        except Exception as e:
            self.logger.error(f"获取市场成交额失败: {str(e)}")
            return 0.0

    def _save_market_data(self, date: str, total_volume: float, trading_enabled: bool) -> None:
        """保存市场数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT OR REPLACE INTO market_data
                (date, total_volume, trading_enabled)
                VALUES (?, ?, ?)
            """, (date, total_volume, int(trading_enabled)))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"保存市场数据失败: {str(e)}")

    def _get_candidate_stocks(self, date: str) -> List[str]:
        """
        获取候选股票池

        Args:
            date: 日期

        Returns:
            候选股票列表
        """
        try:
            # 基础筛选：创业板非ST股票
            candidate_stocks = []

            for stock_code in self.chinext_stocks:
                # 检查是否为ST股票
                if self._is_st_stock(stock_code, date):
                    continue

                # 检查市值范围
                market_cap = self._get_stock_market_cap(stock_code, date)
                if market_cap is None:
                    continue

                if (market_cap >= self.strategy_params['market_cap_min'] and
                    market_cap <= self.strategy_params['market_cap_max']):
                    candidate_stocks.append(stock_code)

            self.logger.info(f"候选股票池: {len(candidate_stocks)}只")
            return candidate_stocks

        except Exception as e:
            self.logger.error(f"获取候选股票池失败: {str(e)}")
            return []

    def _is_st_stock(self, stock_code: str, date: str) -> bool:
        """
        检查是否为ST股票

        Args:
            stock_code: 股票代码
            date: 日期

        Returns:
            是否为ST股票
        """
        try:
            # 获取股票基本信息
            stock_info = self.data_manager.get_stock_info(stock_code)
            if stock_info and 'name' in stock_info:
                stock_name = stock_info['name']
                return 'ST' in stock_name or '*ST' in stock_name
            return False
        except Exception as e:
            self.logger.warning(f"检查ST股票失败 {stock_code}: {str(e)}")
            return False

    def _get_stock_market_cap(self, stock_code: str, date: str) -> Optional[float]:
        """
        获取股票市值

        Args:
            stock_code: 股票代码
            date: 日期

        Returns:
            市值（元），失败返回None
        """
        try:
            # 先从数据库查询
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT market_cap FROM stock_data
                WHERE stock_code = ? AND date = ?
            """, (stock_code, date))

            result = cursor.fetchone()
            conn.close()

            if result and result[0] is not None:
                return float(result[0])

            # 如果数据库中没有，则尝试通过akshare获取
            try:
                # 使用akshare获取股票基本信息
                stock_info = ak.stock_individual_info_em(symbol=stock_code.replace('.SZ', '').replace('.SH', ''))
                if not stock_info.empty:
                    # 查找总市值或流通市值
                    for _, row in stock_info.iterrows():
                        if '总市值' in str(row['item']) or '流通市值' in str(row['item']):
                            value_str = str(row['value'])
                            # 解析市值（可能包含单位）
                            if '亿' in value_str:
                                market_cap = float(value_str.replace('亿', '').replace(',', '')) * 1e8
                                return market_cap
                            elif '万' in value_str:
                                market_cap = float(value_str.replace('万', '').replace(',', '')) * 1e4
                                return market_cap
            except Exception as e:
                self.logger.warning(f"获取市值数据失败 {stock_code}: {e}")

            return None

        except Exception as e:
            self.logger.warning(f"获取市值失败 {stock_code}: {str(e)}")
            return None

    def _batch_screen_stocks(self, candidate_stocks: List[str], date: str) -> List[str]:
        """
        批量筛选股票

        Args:
            candidate_stocks: 候选股票列表
            date: 日期

        Returns:
            筛选后的股票列表
        """
        try:
            selected_stocks = []

            # 使用线程池并行处理
            with ThreadPoolExecutor(max_workers=5) as executor:
                # 提交所有任务
                future_to_stock = {
                    executor.submit(self._screen_single_stock, stock, date): stock
                    for stock in candidate_stocks
                }

                # 收集结果
                for future in as_completed(future_to_stock):
                    stock_code = future_to_stock[future]
                    try:
                        is_selected = future.result()
                        if is_selected:
                            selected_stocks.append(stock_code)
                    except Exception as e:
                        self.logger.warning(f"筛选股票失败 {stock_code}: {str(e)}")

            self.logger.info(f"批量筛选完成，选中: {len(selected_stocks)}只")
            return selected_stocks

        except Exception as e:
            self.logger.error(f"批量筛选失败: {str(e)}")
            return []

    def _screen_single_stock(self, stock_code: str, date: str) -> bool:
        """
        筛选单只股票

        Args:
            stock_code: 股票代码
            date: 日期

        Returns:
            是否通过筛选
        """
        try:
            # 获取股票数据
            stock_data = self._get_stock_data_for_screening(stock_code, date)
            if stock_data is None:
                return False

            # 获取技术指标
            indicators = self._calculate_screening_indicators(stock_code, date, stock_data)
            if indicators is None:
                return False

            # 应用筛选条件
            return self._apply_screening_criteria(stock_code, date, stock_data, indicators)

        except Exception as e:
            self.logger.warning(f"筛选单只股票失败 {stock_code}: {str(e)}")
            return False

    def _get_stock_data_for_screening(self, stock_code: str, date: str) -> Optional[pd.DataFrame]:
        """获取用于筛选的股票数据"""
        try:
            # 获取最近30天的数据用于计算技术指标
            end_date = datetime.strptime(date, '%Y-%m-%d')
            start_date = end_date - timedelta(days=60)  # 多获取一些数据确保有足够的交易日

            data = self.data_manager.get_stock_data(
                stock_code,
                start_date.strftime('%Y-%m-%d'),
                date
            )

            if data is None or data.empty or len(data) < 20:
                return None

            return data

        except Exception as e:
            self.logger.warning(f"获取股票数据失败 {stock_code}: {str(e)}")
            return None

    def _calculate_screening_indicators(self, stock_code: str, date: str, data: pd.DataFrame) -> Optional[Dict[str, float]]:
        """计算筛选用的技术指标"""
        try:
            if data.empty or len(data) < 20:
                return None

            # 获取最新一天的数据
            latest_data = data.iloc[-1]

            # 计算量比
            volume_ratio = self._calculate_volume_ratio(data)

            # 计算DDE大单净量（模拟计算）
            dde_net_ratio = self._calculate_dde_net_ratio(data)

            # 计算换手率
            turnover_rate = latest_data.get('turnover_rate', 0.0)
            if turnover_rate == 0.0:
                # 如果没有换手率数据，用成交量/流通股本估算
                turnover_rate = self._estimate_turnover_rate(stock_code, latest_data)

            # 计算换手率环比增长
            turnover_growth = self._calculate_turnover_growth(data)

            # 检查涨停记录
            limit_up_count = self._count_limit_up_days(data, self.strategy_params['limit_up_days'])

            # 检查急速拉升形态
            has_rapid_rise = self._check_rapid_rise_pattern(data)

            return {
                'volume_ratio': volume_ratio,
                'dde_net_ratio': dde_net_ratio,
                'turnover_rate': turnover_rate,
                'turnover_growth': turnover_growth,
                'limit_up_count': limit_up_count,
                'has_rapid_rise': has_rapid_rise
            }

        except Exception as e:
            self.logger.warning(f"计算技术指标失败 {stock_code}: {str(e)}")
            return None

    def _calculate_volume_ratio(self, data: pd.DataFrame) -> float:
        """计算量比"""
        try:
            if len(data) < 20:
                return 0.0

            # 最新成交量
            latest_volume = data['volume'].iloc[-1]

            # 前20日平均成交量
            avg_volume = data['volume'].iloc[-21:-1].mean()

            if avg_volume > 0:
                return latest_volume / avg_volume
            return 0.0

        except Exception:
            return 0.0

    def _calculate_dde_net_ratio(self, data: pd.DataFrame) -> float:
        """计算DDE大单净量比例（模拟）"""
        try:
            # 由于无法获取真实的大单数据，使用价量关系模拟
            if len(data) < 2:
                return 0.0

            latest = data.iloc[-1]
            prev = data.iloc[-2]

            # 价格涨幅
            price_change = (latest['close'] - prev['close']) / prev['close']

            # 成交量放大倍数
            volume_ratio = latest['volume'] / prev['volume'] if prev['volume'] > 0 else 1.0

            # 模拟大单净量：价格上涨且放量时为正值
            if price_change > 0 and volume_ratio > 1.5:
                return min(price_change * volume_ratio * 100, 10.0)  # 限制在10%以内
            elif price_change < 0 and volume_ratio > 1.5:
                return max(price_change * volume_ratio * 100, -10.0)  # 限制在-10%以内
            else:
                return price_change * 50  # 基础值

        except Exception:
            return 0.0

    def _estimate_turnover_rate(self, stock_code: str, latest_data: pd.Series) -> float:
        """估算换手率"""
        try:
            # 简单估算，实际应该获取流通股本数据
            volume = latest_data.get('volume', 0)
            amount = latest_data.get('amount', 0)

            if amount > 0 and volume > 0:
                avg_price = amount / volume
                # 假设流通股本为成交额的1/100（粗略估算）
                estimated_shares = amount / avg_price / 100
                return (volume / estimated_shares) * 100 if estimated_shares > 0 else 0.0

            return 0.0
        except Exception:
            return 0.0

    def _calculate_turnover_growth(self, data: pd.DataFrame) -> float:
        """计算换手率环比增长"""
        try:
            if len(data) < 2:
                return 0.0

            # 获取最近两天的换手率
            latest_turnover = data['turnover_rate'].iloc[-1] if 'turnover_rate' in data.columns else 0.0
            prev_turnover = data['turnover_rate'].iloc[-2] if 'turnover_rate' in data.columns else 0.0

            if prev_turnover > 0:
                return (latest_turnover - prev_turnover) / prev_turnover
            return 0.0

        except Exception:
            return 0.0

    def _count_limit_up_days(self, data: pd.DataFrame, days: int) -> int:
        """统计指定天数内的涨停次数"""
        try:
            if len(data) < 2:
                return 0

            # 计算涨跌幅
            data_copy = data.copy()
            data_copy['pct_change'] = data_copy['close'].pct_change()

            # 统计涨停次数（涨幅 >= 9.8%，考虑误差）
            limit_up_count = (data_copy['pct_change'] >= 0.098).sum()

            return int(limit_up_count)

        except Exception:
            return 0

    def _check_rapid_rise_pattern(self, data: pd.DataFrame) -> bool:
        """检查急速拉升形态"""
        try:
            if len(data) < 5:
                return False

            # 检查最近5天内是否有急速拉升
            recent_data = data.tail(5)

            # 计算日内涨幅
            recent_data = recent_data.copy()
            recent_data['intraday_rise'] = (recent_data['high'] - recent_data['open']) / recent_data['open']

            # 检查是否有日内涨幅超过5%的情况
            has_rapid_rise = (recent_data['intraday_rise'] >= 0.05).any()

            return bool(has_rapid_rise)

        except Exception:
            return False

    def _apply_screening_criteria(self, stock_code: str, date: str, data: pd.DataFrame, indicators: Dict[str, float]) -> bool:
        """应用筛选条件"""
        try:
            # 1. 量比检查
            if indicators['volume_ratio'] < self.strategy_params['volume_ratio_min']:
                return False

            # 2. DDE大单净量检查
            if indicators['dde_net_ratio'] < self.strategy_params['dde_net_ratio_min']:
                return False

            # 3. 换手率检查
            if indicators['turnover_rate'] < self.strategy_params['turnover_rate_min']:
                return False

            # 4. 换手率环比增长检查
            if indicators['turnover_growth'] < self.strategy_params['turnover_growth_min']:
                return False

            # 5. 涨停记录检查
            if indicators['limit_up_count'] < self.strategy_params['min_limit_up_count']:
                return False

            # 6. 急速拉升形态检查
            if not indicators['has_rapid_rise']:
                return False

            # 保存筛选结果（这里不是交易信号，只是筛选通过的记录）
            # 实际的交易信号在generate_signals方法中生成和保存

            return True

        except Exception as e:
            self.logger.warning(f"应用筛选条件失败 {stock_code}: {str(e)}")
            return False

    def _save_trading_signal(self, stock_code: str, date: str, indicators: Dict[str, float]) -> None:
        """保存交易信号"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 计算信号强度
            signal_strength = (
                indicators['volume_ratio'] * 0.3 +
                indicators['dde_net_ratio'] * 0.3 +
                indicators['turnover_rate'] * 0.2 +
                indicators['limit_up_count'] * 0.1 +
                (1.0 if indicators['has_rapid_rise'] else 0.0) * 0.1
            )

            cursor.execute("""
                INSERT OR REPLACE INTO trading_signals
                (stock_code, date, signal_type, signal_strength, volume_ratio, dde_net_ratio, turnover_rate)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                stock_code, date, 'BUY', signal_strength,
                indicators['volume_ratio'], indicators['dde_net_ratio'], indicators['turnover_rate']
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.warning(f"保存交易信号失败 {stock_code}: {str(e)}")

    def _rank_stocks_by_signal_strength(self, selected_stocks: List[str], date: str) -> List[str]:
        """按信号强度排序股票"""
        try:
            conn = sqlite3.connect(self.db_path)

            # 获取信号强度
            placeholders = ','.join(['?' for _ in selected_stocks])
            query = f"""
                SELECT stock_code, signal_strength
                FROM trading_signals
                WHERE stock_code IN ({placeholders}) AND date = ?
                ORDER BY signal_strength DESC
            """

            df = pd.read_sql_query(query, conn, params=tuple(selected_stocks + [date]))
            conn.close()

            return df['stock_code'].tolist()

        except Exception as e:
            self.logger.warning(f"按信号强度排序失败: {str(e)}")
            return selected_stocks

    def generate_signals(self, date: Union[str, date, datetime]) -> Dict[str, Any]:
        """
        生成交易信号

        Args:
            date: 信号生成日期

        Returns:
            交易信号字典
        """
        start_time = time.time()

        try:
            # 执行选股
            selected_stocks = self._execute_selection(date)

            # 生成买入信号
            buy_signals = []
            for stock_code in selected_stocks:
                buy_signals.append({
                    'stock_code': stock_code,
                    'action': 'BUY',
                    'quantity': self._calculate_position_size(stock_code),
                    'price': None,  # 实际交易时确定
                    'timestamp': f"{date} 14:30:00"  # 收盘前30分钟
                })

            # 生成卖出信号（针对持仓股票）
            sell_signals = self._generate_sell_signals(date)

            signal_time = time.time() - start_time
            self.performance_stats['signal_generation_time'] = signal_time

            signals = {
                'date': str(date),
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'market_condition': self._check_market_trading_condition(str(date)),
                'total_signals': len(buy_signals) + len(sell_signals),
                'generation_time': signal_time
            }

            self.logger.info(f"信号生成完成，买入: {len(buy_signals)}只，卖出: {len(sell_signals)}只")

            return signals

        except Exception as e:
            self.logger.error(f"生成交易信号失败: {str(e)}")
            return {
                'date': str(date),
                'buy_signals': [],
                'sell_signals': [],
                'market_condition': False,
                'total_signals': 0,
                'generation_time': 0.0,
                'error': str(e)
            }

    def _calculate_position_size(self, stock_code: str) -> int:
        """计算仓位大小"""
        try:
            # 固定仓位：10万元
            position_value = self.strategy_params['position_size']

            # 获取当前股价（这里简化处理，实际应该获取实时价格）
            # 假设平均股价为20元
            estimated_price = 20.0

            # 计算股数（100股的整数倍）
            shares = int(position_value / estimated_price / 100) * 100

            return max(shares, 100)  # 至少买100股

        except Exception as e:
            self.logger.warning(f"计算仓位大小失败 {stock_code}: {str(e)}")
            return 100

    def _generate_sell_signals(self, date: Union[str, date, datetime]) -> List[Dict[str, Any]]:
        """生成卖出信号"""
        try:
            sell_signals = []

            # 获取当前持仓
            current_positions = self._get_current_positions(str(date))

            if not current_positions:
                return sell_signals

            self.logger.info(f"当前持仓数量: {len(current_positions)}")

            # 根据策略规则生成卖出信号
            for position in current_positions:
                stock_code = position['stock_code']
                quantity = position['quantity']

                # 策略规则：隔夜持仓，次日开盘后30分钟卖出
                sell_signal = {
                    'stock_code': stock_code,
                    'quantity': quantity,
                    'signal_type': 'sell',
                    'reason': '策略规则：隔夜持仓到期',
                    'timestamp': str(date),
                    'priority': 1  # 高优先级
                }

                sell_signals.append(sell_signal)

                self.logger.debug(f"生成卖出信号: {stock_code}, 数量: {quantity}")

            self.logger.info(f"生成卖出信号 {len(sell_signals)} 个")
            return sell_signals

        except Exception as e:
            self.logger.warning(f"生成卖出信号失败: {str(e)}")
            return []

    def _get_current_positions(self, date: str) -> List[Dict[str, Any]]:
        """
        获取当前持仓

        Args:
            date: 查询日期

        Returns:
            List[Dict]: 持仓列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 查询当前持仓（简化实现，实际应该维护持仓表）
            cursor.execute("""
                SELECT stock_code, SUM(quantity) as total_quantity
                FROM trading_signals
                WHERE date <= ? AND signal_type = 'buy'
                GROUP BY stock_code
                HAVING total_quantity > 0
            """, (date,))

            positions = []
            for row in cursor.fetchall():
                stock_code, quantity = row
                positions.append({
                    'stock_code': stock_code,
                    'quantity': int(quantity),
                    'date': date
                })

            conn.close()
            return positions

        except Exception as e:
            self.logger.error(f"获取当前持仓失败: {str(e)}")
            return []

    def _save_trading_signal(self, signal: Dict[str, Any], date: str):
        """
        保存交易信号到数据库

        Args:
            signal: 交易信号
            date: 日期
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT OR REPLACE INTO trading_signals
                (stock_code, date, signal_type, quantity, price, reason, priority)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                signal['stock_code'],
                date,
                signal['signal_type'],
                signal['quantity'],
                signal.get('price', 0.0),
                signal.get('reason', ''),
                signal.get('priority', 0)
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"保存交易信号失败: {str(e)}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return {
            'strategy_name': self.name,
            'performance_stats': self.performance_stats.copy(),
            'strategy_params': self.strategy_params.copy(),
            'chinext_stocks_count': len(self.chinext_stocks)
        }

    def cleanup(self) -> None:
        """清理资源"""
        try:
            # 关闭数据库连接等清理工作
            self.logger.info("策略资源清理完成")
        except Exception as e:
            self.logger.error(f"资源清理失败: {str(e)}")


# 测试函数
def test_chinext_strategy():
    """测试创业板动态因子策略"""
    try:
        print("开始测试创业板动态因子策略...")

        # 创建策略实例
        config = {
            'market_cap_min': 15e8,
            'market_cap_max': 250e8,
            'volume_ratio_min': 2.0,
            'dde_net_ratio_min': 0.5,
            'turnover_rate_min': 4.0,
            'market_volume_threshold': 1.5e12,
            'max_positions': 10
        }

        strategy = ChiNextDynamicFactorStrategy(config)
        strategy.initialize(config)

        # 测试选股
        test_date = "2024-01-15"
        print(f"测试日期: {test_date}")

        # 生成交易信号
        signals = strategy.generate_signals(test_date)

        print(f"交易信号生成结果:")
        print(f"- 买入信号: {len(signals['buy_signals'])}个")
        print(f"- 卖出信号: {len(signals['sell_signals'])}个")
        print(f"- 市场条件: {signals['market_condition']}")
        print(f"- 生成耗时: {signals['generation_time']:.2f}秒")

        # 显示性能统计
        stats = strategy.get_performance_stats()
        print(f"\n性能统计:")
        for key, value in stats['performance_stats'].items():
            print(f"- {key}: {value}")

        # 清理资源
        strategy.cleanup()

        print("测试完成!")

    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_chinext_strategy()

"""
动态交易策略

策略特点：
1. 持股4天后强制卖出
2. 达到选股条件的TICK点立即买入
3. 动态买入股票数量（基于资金管理）
4. 动态滑点模拟（基于成交量和波动率）
5. 完整的交易记录和成本分析
"""

import sys
import os
from pathlib import Path
import time
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from data_acquisition import DataManager
from data_acquisition.utils.technical_indicators import TechnicalIndicators

@dataclass
class Position:
    """持仓信息"""
    stock_code: str
    buy_date: str
    buy_price: float
    shares: int
    buy_cost: float  # 包含滑点的实际买入成本
    hold_days: int = 0

@dataclass
class Trade:
    """交易记录"""
    stock_code: str
    action: str  # 'BUY' or 'SELL'
    date: str
    price: float
    shares: int
    cost: float  # 实际成本（包含滑点）
    slippage: float  # 滑点
    reason: str  # 交易原因

class DynamicTradingStrategy:
    """动态交易策略"""
    
    def __init__(self, initial_capital: float = 1000000):
        """
        初始化动态交易策略
        
        参数:
            initial_capital: 初始资金（默认100万）
        """
        self.data_manager = DataManager()
        self.indicators = TechnicalIndicators()
        
        # 资金管理
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.available_cash = initial_capital
        
        # 持仓管理
        self.positions: Dict[str, Position] = {}  # 当前持仓
        self.trades: List[Trade] = []  # 交易记录
        
        # 策略参数
        self.hold_days = 4  # 持股天数
        self.max_positions = 20  # 最大持仓数量
        self.position_size_pct = 0.05  # 单只股票最大仓位比例（5%）
        
        # 选股条件
        self.selection_criteria = {
            'market_cap_min': 15e8,    # 15亿
            'market_cap_max': 300e8,   # 300亿
            'wr_threshold': -20,       # WR > -20
            'big_order_net_ratio': 0.4, # 大单净量 > 0.4
            'volume_ratio': 2.0        # 量比 > 2
        }
        
        # 滑点参数
        self.slippage_config = {
            'base_slippage': 0.001,    # 基础滑点 0.1%
            'volume_factor': 0.0005,   # 成交量因子
            'volatility_factor': 0.002, # 波动率因子
            'max_slippage': 0.01       # 最大滑点 1%
        }
        
        # 数据缓存
        self.data_cache = {}
        
    def get_demo_stocks(self) -> List[str]:
        """获取演示用的创业板股票"""
        return [
            '300001.SZ', '300002.SZ', '300008.SZ', '300015.SZ', '300029.SZ',
            '300033.SZ', '300059.SZ', '300124.SZ', '300144.SZ', '300251.SZ',
            '300274.SZ', '300296.SZ', '300347.SZ', '300408.SZ', '300413.SZ',
            '300450.SZ', '300498.SZ', '300529.SZ', '300558.SZ', '300601.SZ'
        ]
    
    def preload_data(self, stock_list: List[str], start_date: str, end_date: str):
        """预加载股票数据"""
        print("📥 预加载股票数据...")
        
        successful_loads = 0
        for stock_code in stock_list:
            try:
                data = self.data_manager.get_stock_data(stock_code, start_date, end_date)
                if data is not None and not data.empty and len(data) >= 30:
                    # 计算技术指标
                    indicators_data = self.indicators.calculate_all_indicators(data)
                    self.data_cache[stock_code] = indicators_data
                    successful_loads += 1
            except Exception as e:
                print(f"加载 {stock_code} 失败: {e}")
        
        print(f"✅ 数据预加载完成: {successful_loads}/{len(stock_list)} 只股票")
    
    def estimate_market_cap(self, stock_code: str, price: float) -> float:
        """估算流通市值"""
        try:
            code_num = int(stock_code[3:6])
            
            if code_num < 100:
                shares = (2 + (code_num % 10) * 0.5) * 1e8
            elif code_num < 500:
                shares = (1.5 + (code_num % 20) * 0.2) * 1e8
            else:
                shares = (1 + (code_num % 30) * 0.1) * 1e8
            
            return price * shares
        except:
            return 0
    
    def simulate_big_order_ratio(self, volume: float, price_change: float) -> float:
        """模拟大单净量比例"""
        base_ratio = 0.3
        volume_factor = min(volume / 1e6, 2.0) * 0.1
        price_factor = max(price_change, 0) * 0.5
        
        return base_ratio + volume_factor + price_factor
    
    def calculate_dynamic_slippage(self, stock_code: str, date: str, action: str, 
                                 shares: int, price: float) -> float:
        """
        计算动态滑点
        
        参数:
            stock_code: 股票代码
            date: 交易日期
            action: 交易方向 ('BUY' or 'SELL')
            shares: 交易股数
            price: 基准价格
            
        返回:
            float: 滑点比例
        """
        try:
            if stock_code not in self.data_cache:
                return self.slippage_config['base_slippage']
            
            data = self.data_cache[stock_code]
            date_index = pd.to_datetime(date)
            
            if date_index not in data.index:
                return self.slippage_config['base_slippage']
            
            current_data = data.loc[date_index]
            
            # 基础滑点
            base_slippage = self.slippage_config['base_slippage']
            
            # 成交量因子（成交量越小，滑点越大）
            current_volume = float(current_data['volume'])
            avg_volume = float(data['volume'].rolling(20).mean().loc[date_index])
            
            if avg_volume > 0:
                volume_ratio = current_volume / avg_volume
                volume_slippage = self.slippage_config['volume_factor'] / max(volume_ratio, 0.1)
            else:
                volume_slippage = self.slippage_config['volume_factor']
            
            # 波动率因子（波动率越大，滑点越大）
            returns = data['close'].pct_change().rolling(20)
            volatility = float(returns.std().loc[date_index]) if not pd.isna(returns.std().loc[date_index]) else 0.02
            volatility_slippage = volatility * self.slippage_config['volatility_factor']
            
            # 交易规模因子（交易金额越大，滑点越大）
            trade_amount = shares * price
            market_cap = self.estimate_market_cap(stock_code, price)
            if market_cap > 0:
                size_factor = min(trade_amount / market_cap * 1000, 0.005)  # 最大0.5%
            else:
                size_factor = 0.001
            
            # 买卖方向调整（买入滑点为正，卖出滑点为负）
            direction_factor = 1 if action == 'BUY' else -1
            
            # 总滑点
            total_slippage = (base_slippage + volume_slippage + volatility_slippage + size_factor) * direction_factor
            
            # 限制最大滑点
            max_slippage = self.slippage_config['max_slippage']
            total_slippage = max(-max_slippage, min(max_slippage, total_slippage))
            
            return total_slippage
            
        except Exception as e:
            print(f"计算滑点失败 {stock_code}: {e}")
            return self.slippage_config['base_slippage']
    
    def calculate_position_size(self, stock_code: str, price: float) -> int:
        """
        计算动态买入股票数量
        
        参数:
            stock_code: 股票代码
            price: 股票价格
            
        返回:
            int: 买入股数
        """
        try:
            # 单只股票最大投资金额
            max_investment = self.current_capital * self.position_size_pct
            
            # 可用现金限制
            available_investment = min(max_investment, self.available_cash * 0.95)  # 保留5%现金
            
            # 根据价格计算股数（100股为一手）
            shares = int(available_investment / price / 100) * 100
            
            # 最小买入1手
            shares = max(shares, 100)
            
            # 检查是否有足够现金
            required_cash = shares * price * 1.01  # 预留1%滑点
            if required_cash > self.available_cash:
                shares = int(self.available_cash / price / 100 * 0.99) * 100
            
            return max(shares, 0)
            
        except Exception as e:
            print(f"计算仓位失败 {stock_code}: {e}")
            return 0
    
    def check_buy_signals(self, date: str) -> List[str]:
        """检查买入信号"""
        buy_signals = []
        
        try:
            date_index = pd.to_datetime(date)
            
            for stock_code, data in self.data_cache.items():
                # 跳过已持有的股票
                if stock_code in self.positions:
                    continue
                
                # 检查日期是否存在
                if date_index not in data.index:
                    continue
                
                current_data = data.loc[date_index]
                
                # 条件1: 流通市值
                close_price = float(current_data['close'])
                market_cap = self.estimate_market_cap(stock_code, close_price)
                
                if not (self.selection_criteria['market_cap_min'] <= market_cap <= self.selection_criteria['market_cap_max']):
                    continue
                
                # 条件2: WR值
                if 'williams_r' not in data.columns or pd.isna(current_data['williams_r']):
                    continue
                
                wr_value = float(current_data['williams_r'])
                if wr_value <= self.selection_criteria['wr_threshold']:
                    continue
                
                # 条件3: 量比
                if 'volume_ratio' not in data.columns or pd.isna(current_data['volume_ratio']):
                    continue
                
                volume_ratio = float(current_data['volume_ratio'])
                if volume_ratio <= self.selection_criteria['volume_ratio']:
                    continue
                
                # 条件4: 大单净量（模拟）
                current_volume = float(current_data['volume'])
                price_change = (current_data['close'] - current_data['open']) / current_data['open']
                big_order_ratio = self.simulate_big_order_ratio(current_volume, price_change)
                
                if big_order_ratio <= self.selection_criteria['big_order_net_ratio']:
                    continue
                
                # 所有条件满足，加入买入信号
                buy_signals.append(stock_code)
                
                # 限制同时买入数量
                if len(buy_signals) >= 5:
                    break
            
        except Exception as e:
            print(f"检查买入信号失败 {date}: {e}")
        
        return buy_signals
    
    def execute_buy(self, stock_code: str, date: str) -> bool:
        """执行买入操作"""
        try:
            if stock_code not in self.data_cache:
                return False
            
            data = self.data_cache[stock_code]
            date_index = pd.to_datetime(date)
            
            if date_index not in data.index:
                return False
            
            current_data = data.loc[date_index]
            base_price = float(current_data['close'])
            
            # 计算买入股数
            shares = self.calculate_position_size(stock_code, base_price)
            
            if shares <= 0:
                return False
            
            # 计算滑点
            slippage = self.calculate_dynamic_slippage(stock_code, date, 'BUY', shares, base_price)
            actual_price = base_price * (1 + slippage)
            total_cost = shares * actual_price
            
            # 检查资金是否充足
            if total_cost > self.available_cash:
                return False
            
            # 执行买入
            position = Position(
                stock_code=stock_code,
                buy_date=date,
                buy_price=actual_price,
                shares=shares,
                buy_cost=total_cost,
                hold_days=0
            )
            
            self.positions[stock_code] = position
            self.available_cash -= total_cost
            
            # 记录交易
            trade = Trade(
                stock_code=stock_code,
                action='BUY',
                date=date,
                price=actual_price,
                shares=shares,
                cost=total_cost,
                slippage=slippage,
                reason='选股条件满足'
            )
            
            self.trades.append(trade)
            
            return True
            
        except Exception as e:
            print(f"买入执行失败 {stock_code}: {e}")
            return False
    
    def execute_sell(self, stock_code: str, date: str, reason: str = '持股到期') -> bool:
        """执行卖出操作"""
        try:
            if stock_code not in self.positions:
                return False
            
            if stock_code not in self.data_cache:
                return False
            
            data = self.data_cache[stock_code]
            date_index = pd.to_datetime(date)
            
            if date_index not in data.index:
                return False
            
            position = self.positions[stock_code]
            current_data = data.loc[date_index]
            base_price = float(current_data['close'])
            
            # 计算滑点
            slippage = self.calculate_dynamic_slippage(stock_code, date, 'SELL', position.shares, base_price)
            actual_price = base_price * (1 + slippage)
            total_proceeds = position.shares * actual_price
            
            # 执行卖出
            self.available_cash += total_proceeds
            del self.positions[stock_code]
            
            # 记录交易
            trade = Trade(
                stock_code=stock_code,
                action='SELL',
                date=date,
                price=actual_price,
                shares=position.shares,
                cost=total_proceeds,
                slippage=slippage,
                reason=reason
            )
            
            self.trades.append(trade)
            
            return True
            
        except Exception as e:
            print(f"卖出执行失败 {stock_code}: {e}")
            return False

    def update_positions(self, date: str):
        """更新持仓信息"""
        positions_to_sell = []

        for stock_code, position in self.positions.items():
            # 更新持股天数
            position.hold_days += 1

            # 检查是否需要卖出
            if position.hold_days >= self.hold_days:
                positions_to_sell.append(stock_code)

        # 执行卖出
        for stock_code in positions_to_sell:
            self.execute_sell(stock_code, date, f'持股{self.hold_days}天到期')

    def calculate_current_portfolio_value(self, date: str) -> float:
        """计算当前投资组合总价值"""
        total_value = self.available_cash

        try:
            date_index = pd.to_datetime(date)

            for stock_code, position in self.positions.items():
                if stock_code in self.data_cache:
                    data = self.data_cache[stock_code]
                    if date_index in data.index:
                        current_price = float(data.loc[date_index]['close'])
                        position_value = position.shares * current_price
                        total_value += position_value

        except Exception as e:
            print(f"计算组合价值失败 {date}: {e}")

        return total_value

    def run_dynamic_backtest(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """运行动态交易回测"""
        print(f"🚀 动态交易策略回测")
        print(f"回测期间: {start_date} 到 {end_date}")
        print(f"初始资金: {self.initial_capital:,.0f} 元")
        print(f"持股周期: {self.hold_days} 天")
        print(f"=" * 60)

        start_time = time.time()

        # 1. 获取股票列表并预加载数据
        stock_list = self.get_demo_stocks()
        self.preload_data(stock_list, start_date, end_date)

        # 2. 生成交易日期
        trading_dates = pd.date_range(start=start_date, end=end_date, freq='B')
        trading_dates = [d.strftime('%Y-%m-%d') for d in trading_dates]

        print(f"交易日数量: {len(trading_dates)} 天")

        # 3. 执行逐日交易
        daily_portfolio_values = []
        daily_positions_count = []
        daily_cash = []

        print("📈 开始逐日交易...")

        for i, date in enumerate(trading_dates):
            try:
                # 更新持仓（检查卖出条件）
                self.update_positions(date)

                # 检查买入信号
                if len(self.positions) < self.max_positions:
                    buy_signals = self.check_buy_signals(date)

                    # 执行买入
                    for stock_code in buy_signals:
                        if len(self.positions) >= self.max_positions:
                            break
                        self.execute_buy(stock_code, date)

                # 计算当日组合价值
                portfolio_value = self.calculate_current_portfolio_value(date)
                daily_portfolio_values.append(portfolio_value)
                daily_positions_count.append(len(self.positions))
                daily_cash.append(self.available_cash)

                # 更新当前资本
                self.current_capital = portfolio_value

                # 进度显示
                if (i + 1) % 20 == 0 or i == len(trading_dates) - 1:
                    print(f"  {date}: 组合价值 {portfolio_value:,.0f}, 持仓 {len(self.positions)} 只, 现金 {self.available_cash:,.0f}")

            except Exception as e:
                print(f"交易执行失败 {date}: {e}")
                daily_portfolio_values.append(self.current_capital)
                daily_positions_count.append(len(self.positions))
                daily_cash.append(self.available_cash)

        # 4. 强制清仓（回测结束）
        final_date = trading_dates[-1]
        for stock_code in list(self.positions.keys()):
            self.execute_sell(stock_code, final_date, '回测结束清仓')

        # 5. 计算最终价值
        final_value = self.available_cash

        # 6. 计算回测指标
        results = self.calculate_backtest_results(
            trading_dates, daily_portfolio_values, daily_positions_count, daily_cash
        )

        execution_time = time.time() - start_time
        results['execution_time'] = execution_time
        results['final_value'] = final_value

        return results

    def calculate_backtest_results(self, trading_dates: List[str],
                                 portfolio_values: List[float],
                                 positions_count: List[int],
                                 cash_values: List[float]) -> Dict[str, Any]:
        """计算回测结果"""
        try:
            # 基础数据
            portfolio_series = pd.Series(portfolio_values, index=pd.to_datetime(trading_dates))
            returns = portfolio_series.pct_change().dropna()

            # 收益指标
            total_return = (portfolio_values[-1] - self.initial_capital) / self.initial_capital
            annual_return = (1 + total_return) ** (252 / len(returns)) - 1 if len(returns) > 0 else 0
            volatility = returns.std() * np.sqrt(252) if len(returns) > 0 else 0
            sharpe_ratio = annual_return / volatility if volatility > 0 else 0

            # 最大回撤
            cumulative_returns = (1 + returns).cumprod()
            running_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = drawdown.min() if len(drawdown) > 0 else 0

            # 胜率
            win_rate = (returns > 0).mean() if len(returns) > 0 else 0

            # 交易统计
            buy_trades = [t for t in self.trades if t.action == 'BUY']
            sell_trades = [t for t in self.trades if t.action == 'SELL']

            total_trades = len(buy_trades)
            total_slippage_cost = sum(abs(t.slippage * t.shares * t.price) for t in self.trades)

            # 持仓统计
            avg_positions = np.mean(positions_count) if positions_count else 0
            max_positions_held = max(positions_count) if positions_count else 0

            # 资金利用率
            avg_cash_ratio = np.mean([cash / self.initial_capital for cash in cash_values])

            return {
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'total_trades': total_trades,
                'buy_trades': len(buy_trades),
                'sell_trades': len(sell_trades),
                'total_slippage_cost': total_slippage_cost,
                'avg_positions': avg_positions,
                'max_positions_held': max_positions_held,
                'avg_cash_ratio': avg_cash_ratio,
                'final_cash': cash_values[-1] if cash_values else 0,
                'portfolio_values': portfolio_values,
                'returns': returns,
                'trading_dates': trading_dates
            }

        except Exception as e:
            print(f"计算回测结果失败: {e}")
            return {
                'total_return': 0,
                'annual_return': 0,
                'volatility': 0,
                'sharpe_ratio': 0,
                'max_drawdown': 0,
                'win_rate': 0,
                'total_trades': 0,
                'buy_trades': 0,
                'sell_trades': 0,
                'total_slippage_cost': 0,
                'avg_positions': 0,
                'max_positions_held': 0,
                'avg_cash_ratio': 1,
                'final_cash': self.initial_capital,
                'portfolio_values': [self.initial_capital],
                'returns': pd.Series([]),
                'trading_dates': []
            }

    def analyze_trades(self) -> Dict[str, Any]:
        """分析交易记录"""
        if not self.trades:
            return {}

        # 按股票分组分析
        stock_trades = defaultdict(list)
        for trade in self.trades:
            stock_trades[trade.stock_code].append(trade)

        # 计算每只股票的盈亏
        stock_pnl = {}
        for stock_code, trades in stock_trades.items():
            buy_trades = [t for t in trades if t.action == 'BUY']
            sell_trades = [t for t in trades if t.action == 'SELL']

            total_buy_cost = sum(t.cost for t in buy_trades)
            total_sell_proceeds = sum(t.cost for t in sell_trades)

            pnl = total_sell_proceeds - total_buy_cost
            stock_pnl[stock_code] = {
                'pnl': pnl,
                'buy_cost': total_buy_cost,
                'sell_proceeds': total_sell_proceeds,
                'trades_count': len(trades)
            }

        # 滑点分析
        buy_slippages = [abs(t.slippage) for t in self.trades if t.action == 'BUY']
        sell_slippages = [abs(t.slippage) for t in self.trades if t.action == 'SELL']

        return {
            'stock_pnl': stock_pnl,
            'avg_buy_slippage': np.mean(buy_slippages) if buy_slippages else 0,
            'avg_sell_slippage': np.mean(sell_slippages) if sell_slippages else 0,
            'max_slippage': max([abs(t.slippage) for t in self.trades]) if self.trades else 0,
            'total_slippage_cost': sum(abs(t.slippage * t.shares * t.price) for t in self.trades)
        }

    def print_results(self, results: Dict[str, Any]):
        """打印回测结果"""
        print(f"\n" + "="*80)
        print(f"📊 动态交易策略回测结果")
        print(f"="*80)

        # 基础信息
        print(f"\n💰 资金情况:")
        print(f"  初始资金:       {self.initial_capital:,.0f} 元")
        print(f"  最终价值:       {results['final_cash']:,.0f} 元")
        print(f"  绝对收益:       {results['final_cash'] - self.initial_capital:,.0f} 元")

        # 收益指标
        print(f"\n📈 收益指标:")
        print(f"  总收益率:       {results['total_return']:.2%}")
        print(f"  年化收益率:     {results['annual_return']:.2%}")
        print(f"  年化波动率:     {results['volatility']:.2%}")
        print(f"  夏普比率:       {results['sharpe_ratio']:.2f}")

        # 风险指标
        print(f"\n📉 风险指标:")
        print(f"  最大回撤:       {results['max_drawdown']:.2%}")
        print(f"  胜率:           {results['win_rate']:.2%}")

        # 交易统计
        print(f"\n📊 交易统计:")
        print(f"  总交易次数:     {results['total_trades']} 次")
        print(f"  买入次数:       {results['buy_trades']} 次")
        print(f"  卖出次数:       {results['sell_trades']} 次")
        print(f"  平均持仓数:     {results['avg_positions']:.1f} 只")
        print(f"  最大持仓数:     {results['max_positions_held']} 只")
        print(f"  平均现金比例:   {results['avg_cash_ratio']:.1%}")

        # 成本分析
        print(f"\n💸 成本分析:")
        print(f"  总滑点成本:     {results['total_slippage_cost']:,.0f} 元")
        print(f"  滑点成本率:     {results['total_slippage_cost']/self.initial_capital:.2%}")

        # 交易分析
        trade_analysis = self.analyze_trades()
        if trade_analysis:
            print(f"\n🔍 滑点分析:")
            print(f"  平均买入滑点:   {trade_analysis['avg_buy_slippage']:.3%}")
            print(f"  平均卖出滑点:   {trade_analysis['avg_sell_slippage']:.3%}")
            print(f"  最大滑点:       {trade_analysis['max_slippage']:.3%}")

        # 执行效率
        if 'execution_time' in results:
            print(f"\n⚡ 执行效率:")
            print(f"  回测耗时:       {results['execution_time']:.2f} 秒")
            print(f"  平均每日耗时:   {results['execution_time']/len(results['trading_dates']):.3f} 秒")

        # 策略评价
        print(f"\n🎯 策略评价:")
        if results['annual_return'] > 0.15:
            print(f"  ✅ 年化收益率优秀: {results['annual_return']:.2%}")
        elif results['annual_return'] > 0:
            print(f"  ⚠️  年化收益率一般: {results['annual_return']:.2%}")
        else:
            print(f"  ❌ 年化收益率为负: {results['annual_return']:.2%}")

        if results['sharpe_ratio'] > 1.5:
            print(f"  ✅ 夏普比率优秀: {results['sharpe_ratio']:.2f}")
        elif results['sharpe_ratio'] > 0.5:
            print(f"  ⚠️  夏普比率一般: {results['sharpe_ratio']:.2f}")
        else:
            print(f"  ❌ 夏普比率较差: {results['sharpe_ratio']:.2f}")

        if abs(results['max_drawdown']) < 0.15:
            print(f"  ✅ 最大回撤控制良好: {results['max_drawdown']:.2%}")
        elif abs(results['max_drawdown']) < 0.25:
            print(f"  ⚠️  最大回撤中等: {results['max_drawdown']:.2%}")
        else:
            print(f"  ❌ 最大回撤较大: {results['max_drawdown']:.2%}")

    def print_trade_details(self, limit: int = 10):
        """打印交易明细"""
        print(f"\n📋 交易明细 (最近{limit}笔):")
        print(f"{'日期':<12} {'代码':<10} {'操作':<4} {'价格':<8} {'股数':<8} {'金额':<12} {'滑点':<8} {'原因':<12}")
        print("-" * 80)

        recent_trades = self.trades[-limit:] if len(self.trades) > limit else self.trades

        for trade in recent_trades:
            print(f"{trade.date:<12} {trade.stock_code:<10} {trade.action:<4} "
                  f"{trade.price:<8.2f} {trade.shares:<8} {trade.cost:<12,.0f} "
                  f"{trade.slippage:<8.3%} {trade.reason:<12}")

    def cleanup(self):
        """清理资源"""
        if hasattr(self, 'data_manager'):
            self.data_manager.cleanup()

def main():
    """主函数"""
    print("🚀 动态交易策略回测系统")
    print("=" * 80)

    # 创建策略实例
    strategy = DynamicTradingStrategy(initial_capital=1000000)  # 100万初始资金

    try:
        # 设置回测参数
        end_date = date.today()
        start_date = end_date - timedelta(days=90)  # 最近3个月

        print(f"📅 回测参数:")
        print(f"  开始日期: {start_date}")
        print(f"  结束日期: {end_date}")
        print(f"  初始资金: {strategy.initial_capital:,.0f} 元")
        print(f"  持股周期: {strategy.hold_days} 天")
        print(f"  最大持仓: {strategy.max_positions} 只")
        print(f"  单股仓位: {strategy.position_size_pct:.1%}")

        print(f"\n🎯 选股条件:")
        print(f"  - 创业板股票 (300开头)")
        print(f"  - 非ST股票")
        print(f"  - 流通市值: 15-300亿")
        print(f"  - WR值 > -20")
        print(f"  - 大单净量 > 0.4")
        print(f"  - 量比 > 2")

        print(f"\n💰 交易机制:")
        print(f"  - 达到选股条件立即买入")
        print(f"  - 持股{strategy.hold_days}天后强制卖出")
        print(f"  - 动态计算买入股数")
        print(f"  - 动态滑点模拟")

        # 执行回测
        print(f"\n🔄 开始执行回测...")
        results = strategy.run_dynamic_backtest(
            start_date.strftime('%Y-%m-%d'),
            end_date.strftime('%Y-%m-%d')
        )

        # 打印结果
        strategy.print_results(results)

        # 打印交易明细
        strategy.print_trade_details(15)

        # 策略特点总结
        print(f"\n💡 策略特点:")
        print(f"  ✅ 持股周期固定: {strategy.hold_days}天")
        print(f"  ✅ 动态仓位管理: 根据资金和价格动态调整")
        print(f"  ✅ 真实滑点模拟: 考虑成交量、波动率等因素")
        print(f"  ✅ 风险控制: 单股最大{strategy.position_size_pct:.1%}仓位")
        print(f"  ✅ 资金利用: 平均现金比例{results.get('avg_cash_ratio', 0):.1%}")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断执行")
    except Exception as e:
        print(f"\n❌ 回测执行失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        strategy.cleanup()
        print("\n🔚 回测系统已关闭")

if __name__ == "__main__":
    main()

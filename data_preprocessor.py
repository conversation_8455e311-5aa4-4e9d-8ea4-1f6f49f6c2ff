"""
数据预处理工具

用于批量下载和预处理创业板股票数据，提高回测效率
"""

import sys
import os
from pathlib import Path
import sqlite3
import time
from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional
import warnings
warnings.filterwarnings('ignore')

from data_acquisition import DataManager
from data_acquisition.utils.technical_indicators import TechnicalIndicators

class DataPreprocessor:
    """数据预处理器"""
    
    def __init__(self, cache_dir: str = "backtest_cache"):
        """初始化数据预处理器"""
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        self.data_manager = DataManager()
        self.indicators = TechnicalIndicators()
        
        # 数据库文件路径
        self.db_path = self.cache_dir / "stock_data.db"
        
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建股票数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_data (
                stock_code TEXT,
                date TEXT,
                open REAL,
                high REAL,
                low REAL,
                close REAL,
                volume INTEGER,
                PRIMARY KEY (stock_code, date)
            )
        ''')
        
        # 创建技术指标表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS technical_indicators (
                stock_code TEXT,
                date TEXT,
                williams_r REAL,
                volume_ratio REAL,
                sma_20 REAL,
                rsi REAL,
                PRIMARY KEY (stock_code, date)
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_date ON stock_data(stock_code, date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_indicator_date ON technical_indicators(stock_code, date)')
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库初始化完成")
    
    def get_chinext_stocks(self) -> List[str]:
        """获取创业板股票列表"""
        try:
            # 获取所有股票列表
            all_stocks = self.data_manager.get_available_stocks()
            
            # 筛选创业板股票
            chinext_stocks = []
            for stock in all_stocks:
                if stock.startswith('300') and stock.endswith('.SZ'):
                    # 排除ST股票
                    if 'ST' not in stock and '*ST' not in stock:
                        chinext_stocks.append(stock)
            
            print(f"找到创业板股票: {len(chinext_stocks)} 只")
            return chinext_stocks
            
        except Exception as e:
            print(f"获取创业板股票列表失败: {e}")
            # 返回常见创业板股票
            return [
                '300001.SZ', '300002.SZ', '300008.SZ', '300015.SZ', '300029.SZ',
                '300033.SZ', '300059.SZ', '300124.SZ', '300144.SZ', '300251.SZ',
                '300274.SZ', '300296.SZ', '300347.SZ', '300408.SZ', '300413.SZ',
                '300450.SZ', '300498.SZ', '300529.SZ', '300558.SZ', '300601.SZ',
                '300628.SZ', '300661.SZ', '300699.SZ', '300750.SZ', '300760.SZ',
                '300770.SZ', '300780.SZ', '300790.SZ', '300800.SZ', '300810.SZ'
            ]
    
    def download_single_stock(self, stock_code: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """下载单只股票数据"""
        try:
            data = self.data_manager.get_stock_data(stock_code, start_date, end_date)
            if data is not None and not data.empty:
                return data
        except Exception as e:
            print(f"下载 {stock_code} 失败: {e}")
        return None
    
    def batch_download_all_data(self, start_date: str, end_date: str, max_workers: int = 10):
        """批量下载所有数据"""
        print(f"📥 开始批量下载数据 ({start_date} 到 {end_date})")
        start_time = time.time()
        
        # 获取股票列表
        stock_list = self.get_chinext_stocks()
        
        # 检查已有数据
        conn = sqlite3.connect(self.db_path)
        existing_query = """
            SELECT DISTINCT stock_code, COUNT(*) as count
            FROM stock_data 
            WHERE date >= ? AND date <= ?
            GROUP BY stock_code
        """
        existing_data = pd.read_sql_query(existing_query, conn, params=[start_date, end_date])
        conn.close()
        
        # 计算预期的交易日数量
        expected_days = len(pd.date_range(start=start_date, end=end_date, freq='B'))
        
        # 筛选需要下载的股票（数据不完整的）
        existing_dict = dict(zip(existing_data['stock_code'], existing_data['count']))
        stocks_to_download = [
            stock for stock in stock_list 
            if existing_dict.get(stock, 0) < expected_days * 0.8  # 允许20%的缺失
        ]
        
        if not stocks_to_download:
            print("✅ 所有数据已存在且完整")
            return
        
        print(f"需要下载/更新 {len(stocks_to_download)} 只股票数据")
        
        # 并行下载
        successful_downloads = 0
        failed_downloads = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交下载任务
            futures = {
                executor.submit(self.download_single_stock, stock, start_date, end_date): stock 
                for stock in stocks_to_download
            }
            
            # 收集结果
            conn = sqlite3.connect(self.db_path)
            
            for future in tqdm(futures, desc="下载股票数据"):
                stock_code = futures[future]
                try:
                    data = future.result(timeout=60)  # 60秒超时
                    if data is not None and not data.empty:
                        # 删除旧数据
                        conn.execute(
                            "DELETE FROM stock_data WHERE stock_code = ? AND date >= ? AND date <= ?",
                            (stock_code, start_date, end_date)
                        )
                        
                        # 保存新数据
                        data_to_save = data.reset_index()
                        data_to_save['stock_code'] = stock_code
                        data_to_save['date'] = data_to_save['date'].dt.strftime('%Y-%m-%d')
                        
                        data_to_save[['stock_code', 'date', 'open', 'high', 'low', 'close', 'volume']].to_sql(
                            'stock_data', conn, if_exists='append', index=False
                        )
                        successful_downloads += 1
                    else:
                        failed_downloads.append(stock_code)
                        
                except Exception as e:
                    failed_downloads.append(stock_code)
                    print(f"处理 {stock_code} 失败: {e}")
            
            conn.commit()
            conn.close()
        
        download_time = time.time() - start_time
        
        print(f"✅ 数据下载完成:")
        print(f"  成功: {successful_downloads} 只")
        print(f"  失败: {len(failed_downloads)} 只")
        print(f"  耗时: {download_time:.2f} 秒")
        
        if failed_downloads:
            print(f"  失败股票: {failed_downloads[:10]}{'...' if len(failed_downloads) > 10 else ''}")
    
    def calculate_indicators_for_stock(self, stock_code: str, start_date: str, end_date: str) -> bool:
        """计算单只股票的技术指标"""
        try:
            # 从数据库加载数据
            conn = sqlite3.connect(self.db_path)
            
            query = """
                SELECT date, open, high, low, close, volume
                FROM stock_data 
                WHERE stock_code = ? AND date >= ? AND date <= ?
                ORDER BY date
            """
            
            data = pd.read_sql_query(query, conn, params=[stock_code, start_date, end_date])
            
            if data.empty or len(data) < 20:
                conn.close()
                return False
            
            data['date'] = pd.to_datetime(data['date'])
            data.set_index('date', inplace=True)
            
            # 计算技术指标
            # 威廉指标
            high_14 = data['high'].rolling(14).max()
            low_14 = data['low'].rolling(14).min()
            williams_r = ((high_14 - data['close']) / (high_14 - low_14)) * -100
            
            # 量比
            avg_volume_20 = data['volume'].rolling(20).mean()
            volume_ratio = data['volume'] / avg_volume_20
            
            # SMA
            sma_20 = data['close'].rolling(20).mean()
            
            # RSI
            delta = data['close'].diff()
            gain = delta.where(delta > 0, 0).rolling(14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            # 准备保存数据
            indicators_df = pd.DataFrame({
                'stock_code': stock_code,
                'date': data.index.strftime('%Y-%m-%d'),
                'williams_r': williams_r,
                'volume_ratio': volume_ratio,
                'sma_20': sma_20,
                'rsi': rsi
            })
            
            # 删除旧指标数据
            conn.execute(
                "DELETE FROM technical_indicators WHERE stock_code = ? AND date >= ? AND date <= ?",
                (stock_code, start_date, end_date)
            )
            
            # 保存新指标数据
            indicators_df.dropna().to_sql('technical_indicators', conn, if_exists='append', index=False)
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"计算 {stock_code} 指标失败: {e}")
            return False
    
    def batch_calculate_all_indicators(self, start_date: str, end_date: str, max_workers: int = 4):
        """批量计算所有技术指标"""
        print(f"📊 开始批量计算技术指标 ({start_date} 到 {end_date})")
        start_time = time.time()
        
        # 获取有数据的股票列表
        conn = sqlite3.connect(self.db_path)
        stocks_with_data = pd.read_sql_query(
            "SELECT DISTINCT stock_code FROM stock_data WHERE date >= ? AND date <= ?",
            conn, params=[start_date, end_date]
        )
        conn.close()
        
        if stocks_with_data.empty:
            print("❌ 没有找到股票数据，请先下载数据")
            return
        
        stock_list = stocks_with_data['stock_code'].tolist()
        print(f"需要计算 {len(stock_list)} 只股票的技术指标")
        
        # 并行计算
        successful_calculations = 0
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {
                executor.submit(self.calculate_indicators_for_stock, stock, start_date, end_date): stock 
                for stock in stock_list
            }
            
            for future in tqdm(futures, desc="计算技术指标"):
                stock_code = futures[future]
                try:
                    success = future.result(timeout=120)  # 2分钟超时
                    if success:
                        successful_calculations += 1
                except Exception as e:
                    print(f"计算 {stock_code} 指标超时或失败: {e}")
        
        calc_time = time.time() - start_time
        
        print(f"✅ 技术指标计算完成:")
        print(f"  成功: {successful_calculations}/{len(stock_list)} 只")
        print(f"  耗时: {calc_time:.2f} 秒")
    
    def get_data_statistics(self):
        """获取数据统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 股票数据统计
            stock_stats = pd.read_sql_query("""
                SELECT 
                    COUNT(DISTINCT stock_code) as stock_count,
                    COUNT(*) as total_records,
                    MIN(date) as start_date,
                    MAX(date) as end_date
                FROM stock_data
            """, conn)
            
            # 技术指标统计
            indicator_stats = pd.read_sql_query("""
                SELECT 
                    COUNT(DISTINCT stock_code) as stock_count,
                    COUNT(*) as total_records
                FROM technical_indicators
            """, conn)
            
            conn.close()
            
            print(f"\n📊 数据库统计信息:")
            print(f"股票数据:")
            print(f"  股票数量: {stock_stats.iloc[0]['stock_count']} 只")
            print(f"  总记录数: {stock_stats.iloc[0]['total_records']:,} 条")
            print(f"  日期范围: {stock_stats.iloc[0]['start_date']} 到 {stock_stats.iloc[0]['end_date']}")
            
            print(f"技术指标:")
            print(f"  股票数量: {indicator_stats.iloc[0]['stock_count']} 只")
            print(f"  总记录数: {indicator_stats.iloc[0]['total_records']:,} 条")
            
        except Exception as e:
            print(f"获取统计信息失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        if hasattr(self, 'data_manager'):
            self.data_manager.cleanup()

def main():
    """主函数"""
    print("🔧 数据预处理工具")
    print("=" * 50)
    
    preprocessor = DataPreprocessor()
    
    try:
        # 初始化数据库
        preprocessor.init_database()
        
        # 设置日期范围
        start_date = '2024-01-01'
        end_date = '2025-06-01'
        
        print(f"📅 处理日期范围: {start_date} 到 {end_date}")
        
        # 批量下载数据
        preprocessor.batch_download_all_data(start_date, end_date)
        
        # 批量计算技术指标
        preprocessor.batch_calculate_all_indicators(start_date, end_date)
        
        # 显示统计信息
        preprocessor.get_data_statistics()
        
        print(f"\n✅ 数据预处理完成！")
        print(f"现在可以运行优化版回测系统了")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断执行")
    except Exception as e:
        print(f"\n❌ 预处理失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        preprocessor.cleanup()

if __name__ == "__main__":
    main()

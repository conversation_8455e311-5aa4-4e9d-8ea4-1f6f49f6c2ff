"""
优化版创业板选股策略回测系统

主要优化：
1. 数据预加载和缓存机制
2. 向量化计算替代循环
3. 并行处理技术指标计算
4. 日频选股和调仓
5. 内存优化和进度显示
"""

import sys
import os
from pathlib import Path
import sqlite3
import pickle
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor
from multiprocessing import cpu_count
import time
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

from data_acquisition import DataManager
from data_acquisition.utils.technical_indicators import TechnicalIndicators

class OptimizedBacktestSystem:
    """优化版回测系统"""
    
    def __init__(self, cache_dir: str = "backtest_cache"):
        """
        初始化优化回测系统
        
        参数:
            cache_dir: 缓存目录
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        self.data_manager = DataManager()
        self.indicators = TechnicalIndicators()
        
        # 数据库文件路径
        self.db_path = self.cache_dir / "stock_data.db"
        
        # 选股条件
        self.selection_criteria = {
            'market_cap_min': 15e8,    # 15亿
            'market_cap_max': 300e8,   # 300亿
            'wr_threshold': -20,       # WR > -20
            'big_order_net_ratio': 0.4, # 大单净量 > 0.4
            'volume_ratio': 2.0        # 量比 > 2
        }
        
        # 性能统计
        self.performance_stats = {
            'data_load_time': 0.0,
            'indicator_calc_time': 0.0,
            'selection_time': 0.0,
            'backtest_time': 0.0,
            'total_time': 0.0
        }
        
        # 内存缓存
        self.data_cache = {}
        self.indicator_cache = {}
        
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建股票数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_data (
                stock_code TEXT,
                date TEXT,
                open REAL,
                high REAL,
                low REAL,
                close REAL,
                volume INTEGER,
                PRIMARY KEY (stock_code, date)
            )
        ''')
        
        # 创建技术指标表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS technical_indicators (
                stock_code TEXT,
                date TEXT,
                williams_r REAL,
                volume_ratio REAL,
                sma_20 REAL,
                rsi REAL,
                PRIMARY KEY (stock_code, date)
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_date ON stock_data(stock_code, date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_indicator_date ON technical_indicators(stock_code, date)')
        
        conn.commit()
        conn.close()
        
    def get_chinext_stocks(self) -> List[str]:
        """获取创业板股票列表"""
        try:
            # 获取所有股票列表
            all_stocks = self.data_manager.get_available_stocks()
            
            # 筛选创业板股票
            chinext_stocks = []
            for stock in all_stocks:
                if stock.startswith('300') and stock.endswith('.SZ'):
                    # 排除ST股票
                    if 'ST' not in stock and '*ST' not in stock:
                        chinext_stocks.append(stock)
            
            print(f"找到创业板股票: {len(chinext_stocks)} 只")
            return chinext_stocks[:100]  # 限制数量以提高效率
            
        except Exception as e:
            print(f"获取创业板股票列表失败: {e}")
            # 返回常见创业板股票
            return [
                '300001.SZ', '300002.SZ', '300008.SZ', '300015.SZ', '300029.SZ',
                '300033.SZ', '300059.SZ', '300124.SZ', '300144.SZ', '300251.SZ',
                '300274.SZ', '300296.SZ', '300347.SZ', '300408.SZ', '300413.SZ',
                '300450.SZ', '300498.SZ', '300529.SZ', '300558.SZ', '300601.SZ',
                '300628.SZ', '300661.SZ', '300699.SZ', '300750.SZ', '300760.SZ'
            ]
    
    def download_stock_data(self, stock_code: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """下载单只股票数据"""
        try:
            data = self.data_manager.get_stock_data(stock_code, start_date, end_date)
            if data is not None and not data.empty:
                return data
        except Exception as e:
            print(f"下载 {stock_code} 数据失败: {e}")
        return None
    
    def batch_download_data(self, stock_list: List[str], start_date: str, end_date: str):
        """批量下载股票数据"""
        print(f"📥 开始批量下载数据...")
        start_time = time.time()
        
        # 检查数据库中已有的数据
        conn = sqlite3.connect(self.db_path)
        existing_data = pd.read_sql_query(
            "SELECT DISTINCT stock_code FROM stock_data WHERE date >= ? AND date <= ?",
            conn, params=[start_date, end_date]
        )
        existing_stocks = set(existing_data['stock_code'].tolist()) if not existing_data.empty else set()
        conn.close()
        
        # 筛选需要下载的股票
        stocks_to_download = [stock for stock in stock_list if stock not in existing_stocks]
        
        if not stocks_to_download:
            print("所有数据已存在，跳过下载")
            return
        
        print(f"需要下载 {len(stocks_to_download)} 只股票数据")
        
        # 使用线程池并行下载
        with ThreadPoolExecutor(max_workers=min(10, len(stocks_to_download))) as executor:
            # 提交下载任务
            futures = {
                executor.submit(self.download_stock_data, stock, start_date, end_date): stock 
                for stock in stocks_to_download
            }
            
            # 收集结果并保存到数据库
            conn = sqlite3.connect(self.db_path)
            successful_downloads = 0
            
            for future in tqdm(futures, desc="下载股票数据"):
                stock_code = futures[future]
                try:
                    data = future.result(timeout=30)  # 30秒超时
                    if data is not None:
                        # 保存到数据库
                        data_to_save = data.reset_index()
                        data_to_save['stock_code'] = stock_code
                        data_to_save['date'] = data_to_save['date'].dt.strftime('%Y-%m-%d')
                        
                        data_to_save.to_sql('stock_data', conn, if_exists='append', index=False)
                        successful_downloads += 1
                        
                except Exception as e:
                    print(f"处理 {stock_code} 数据失败: {e}")
            
            conn.commit()
            conn.close()
        
        download_time = time.time() - start_time
        self.performance_stats['data_load_time'] = download_time
        
        print(f"✅ 数据下载完成: {successful_downloads}/{len(stocks_to_download)} 只股票")
        print(f"⏱️  下载耗时: {download_time:.2f} 秒")
    
    def load_stock_data_from_db(self, stock_code: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """从数据库加载股票数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = """
                SELECT date, open, high, low, close, volume
                FROM stock_data 
                WHERE stock_code = ? AND date >= ? AND date <= ?
                ORDER BY date
            """
            
            data = pd.read_sql_query(query, conn, params=[stock_code, start_date, end_date])
            conn.close()
            
            if not data.empty:
                data['date'] = pd.to_datetime(data['date'])
                data.set_index('date', inplace=True)
                return data
                
        except Exception as e:
            print(f"从数据库加载 {stock_code} 数据失败: {e}")
        
        return None
    
    def calculate_indicators_vectorized(self, data: pd.DataFrame) -> pd.DataFrame:
        """向量化计算技术指标"""
        if data.empty or len(data) < 20:
            return data
        
        result = data.copy()
        
        try:
            # 威廉指标
            high_14 = data['high'].rolling(14).max()
            low_14 = data['low'].rolling(14).min()
            result['williams_r'] = ((high_14 - data['close']) / (high_14 - low_14)) * -100
            
            # 量比
            avg_volume_20 = data['volume'].rolling(20).mean()
            result['volume_ratio'] = data['volume'] / avg_volume_20
            
            # SMA
            result['sma_20'] = data['close'].rolling(20).mean()
            
            # RSI
            delta = data['close'].diff()
            gain = delta.where(delta > 0, 0).rolling(14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
            rs = gain / loss
            result['rsi'] = 100 - (100 / (1 + rs))
            
        except Exception as e:
            print(f"计算技术指标失败: {e}")
        
        return result
    
    def batch_calculate_indicators(self, stock_list: List[str], start_date: str, end_date: str):
        """批量计算技术指标"""
        print(f"📊 开始批量计算技术指标...")
        start_time = time.time()
        
        # 检查已计算的指标
        conn = sqlite3.connect(self.db_path)
        existing_indicators = pd.read_sql_query(
            "SELECT DISTINCT stock_code FROM technical_indicators WHERE date >= ? AND date <= ?",
            conn, params=[start_date, end_date]
        )
        existing_stocks = set(existing_indicators['stock_code'].tolist()) if not existing_indicators.empty else set()
        conn.close()
        
        # 筛选需要计算的股票
        stocks_to_calculate = [stock for stock in stock_list if stock not in existing_stocks]
        
        if not stocks_to_calculate:
            print("所有技术指标已计算，跳过计算")
            return
        
        print(f"需要计算 {len(stocks_to_calculate)} 只股票的技术指标")
        
        def calculate_single_stock(stock_code: str):
            """计算单只股票的技术指标"""
            try:
                data = self.load_stock_data_from_db(stock_code, start_date, end_date)
                if data is not None and len(data) >= 20:
                    indicators_data = self.calculate_indicators_vectorized(data)
                    
                    # 准备保存的数据
                    save_data = indicators_data[['williams_r', 'volume_ratio', 'sma_20', 'rsi']].reset_index()
                    save_data['stock_code'] = stock_code
                    save_data['date'] = save_data['date'].dt.strftime('%Y-%m-%d')
                    
                    return save_data
            except Exception as e:
                print(f"计算 {stock_code} 指标失败: {e}")
            return None
        
        # 使用进程池并行计算
        with ProcessPoolExecutor(max_workers=min(cpu_count(), len(stocks_to_calculate))) as executor:
            results = list(tqdm(
                executor.map(calculate_single_stock, stocks_to_calculate),
                total=len(stocks_to_calculate),
                desc="计算技术指标"
            ))
        
        # 保存结果到数据库
        conn = sqlite3.connect(self.db_path)
        successful_calculations = 0
        
        for result in results:
            if result is not None:
                try:
                    result.to_sql('technical_indicators', conn, if_exists='append', index=False)
                    successful_calculations += 1
                except Exception as e:
                    print(f"保存指标数据失败: {e}")
        
        conn.commit()
        conn.close()
        
        calc_time = time.time() - start_time
        self.performance_stats['indicator_calc_time'] = calc_time
        
        print(f"✅ 技术指标计算完成: {successful_calculations}/{len(stocks_to_calculate)} 只股票")
        print(f"⏱️  计算耗时: {calc_time:.2f} 秒")
    
    def estimate_market_cap_vectorized(self, stock_codes: pd.Series, prices: pd.Series) -> pd.Series:
        """向量化计算市值估算"""
        def estimate_single_cap(stock_code: str, price: float) -> float:
            try:
                code_num = int(stock_code[3:6])
                
                if code_num < 100:
                    shares = (2 + (code_num % 10) * 0.5) * 1e8
                elif code_num < 500:
                    shares = (1.5 + (code_num % 20) * 0.2) * 1e8
                else:
                    shares = (1 + (code_num % 30) * 0.1) * 1e8
                
                return price * shares
            except:
                return 0
        
        return pd.Series([
            estimate_single_cap(code, price) 
            for code, price in zip(stock_codes, prices)
        ], index=stock_codes.index)
    
    def simulate_big_order_ratio_vectorized(self, volumes: pd.Series, price_changes: pd.Series) -> pd.Series:
        """向量化模拟大单净量比例"""
        base_ratio = 0.3
        volume_factor = np.minimum(volumes / 1e6, 2.0) * 0.1
        price_factor = np.maximum(price_changes, 0) * 0.5
        
        return base_ratio + volume_factor + price_factor
    
    def screen_stocks_vectorized(self, date: str, stock_list: List[str]) -> List[str]:
        """向量化选股"""
        try:
            # 从数据库批量加载当日数据
            conn = sqlite3.connect(self.db_path)
            
            # 获取股票基础数据
            stock_data_query = """
                SELECT stock_code, open, high, low, close, volume
                FROM stock_data 
                WHERE stock_code IN ({}) AND date = ?
            """.format(','.join(['?' for _ in stock_list]))
            
            stock_data = pd.read_sql_query(
                stock_data_query, conn, 
                params=stock_list + [date]
            )
            
            # 获取技术指标数据
            indicator_query = """
                SELECT stock_code, williams_r, volume_ratio
                FROM technical_indicators 
                WHERE stock_code IN ({}) AND date = ?
            """.format(','.join(['?' for _ in stock_list]))
            
            indicator_data = pd.read_sql_query(
                indicator_query, conn,
                params=stock_list + [date]
            )
            
            conn.close()
            
            if stock_data.empty or indicator_data.empty:
                return []
            
            # 合并数据
            merged_data = pd.merge(stock_data, indicator_data, on='stock_code', how='inner')
            
            if merged_data.empty:
                return []
            
            # 向量化筛选条件
            
            # 条件1: 流通市值
            market_caps = self.estimate_market_cap_vectorized(
                merged_data['stock_code'], merged_data['close']
            )
            market_cap_mask = (
                (market_caps >= self.selection_criteria['market_cap_min']) &
                (market_caps <= self.selection_criteria['market_cap_max'])
            )
            
            # 条件2: WR值
            wr_mask = merged_data['williams_r'] > self.selection_criteria['wr_threshold']
            
            # 条件3: 量比
            volume_ratio_mask = merged_data['volume_ratio'] > self.selection_criteria['volume_ratio']
            
            # 条件4: 大单净量（模拟）
            price_changes = (merged_data['close'] - merged_data['open']) / merged_data['open']
            big_order_ratios = self.simulate_big_order_ratio_vectorized(
                merged_data['volume'], price_changes
            )
            big_order_mask = big_order_ratios > self.selection_criteria['big_order_net_ratio']
            
            # 组合所有条件
            final_mask = market_cap_mask & wr_mask & volume_ratio_mask & big_order_mask
            
            # 获取符合条件的股票
            selected_stocks = merged_data[final_mask]['stock_code'].tolist()
            
            return selected_stocks[:20]  # 限制最大持仓数量
            
        except Exception as e:
            print(f"向量化选股失败 {date}: {e}")
            return []

    def calculate_portfolio_returns_vectorized(self, selections: Dict[str, List[str]],
                                             start_date: str, end_date: str) -> pd.Series:
        """向量化计算投资组合收益率"""
        print("📈 计算投资组合收益率...")

        # 获取所有交易日期
        trading_dates = pd.date_range(start=start_date, end=end_date, freq='B')
        trading_dates = [d.strftime('%Y-%m-%d') for d in trading_dates]

        portfolio_returns = []

        for i, current_date in enumerate(tqdm(trading_dates[:-1], desc="计算收益率")):
            next_date = trading_dates[i + 1]

            # 获取当日选中的股票
            selected_stocks = selections.get(current_date, [])

            if not selected_stocks:
                portfolio_returns.append(0.0)
                continue

            try:
                # 批量获取股票价格数据
                conn = sqlite3.connect(self.db_path)

                price_query = """
                    SELECT stock_code, close as current_close
                    FROM stock_data
                    WHERE stock_code IN ({}) AND date = ?
                """.format(','.join(['?' for _ in selected_stocks]))

                current_prices = pd.read_sql_query(
                    price_query, conn,
                    params=selected_stocks + [current_date]
                )

                next_prices = pd.read_sql_query(
                    price_query.replace('current_close', 'next_close'), conn,
                    params=selected_stocks + [next_date]
                )

                conn.close()

                if current_prices.empty or next_prices.empty:
                    portfolio_returns.append(0.0)
                    continue

                # 计算个股收益率
                merged_prices = pd.merge(
                    current_prices, next_prices,
                    on='stock_code', suffixes=('_current', '_next')
                )

                if merged_prices.empty:
                    portfolio_returns.append(0.0)
                    continue

                stock_returns = (
                    (merged_prices['next_close'] - merged_prices['current_close']) /
                    merged_prices['current_close']
                )

                # 等权重组合收益率
                portfolio_return = stock_returns.mean()
                portfolio_returns.append(float(portfolio_return))

            except Exception as e:
                portfolio_returns.append(0.0)

        return pd.Series(portfolio_returns, index=trading_dates[:-1])

    def run_optimized_backtest(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """运行优化版回测"""
        print(f"🚀 开始优化版回测")
        print(f"回测期间: {start_date} 到 {end_date}")
        print(f"=" * 60)

        total_start_time = time.time()

        # 1. 初始化数据库
        print("🔧 初始化数据库...")
        self.init_database()

        # 2. 获取股票列表
        print("📋 获取创业板股票列表...")
        stock_list = self.get_chinext_stocks()
        print(f"股票池大小: {len(stock_list)} 只")

        # 3. 批量下载数据
        self.batch_download_data(stock_list, start_date, end_date)

        # 4. 批量计算技术指标
        self.batch_calculate_indicators(stock_list, start_date, end_date)

        # 5. 执行日频选股
        print("🎯 开始日频选股...")
        selection_start_time = time.time()

        trading_dates = pd.date_range(start=start_date, end=end_date, freq='B')
        trading_dates = [d.strftime('%Y-%m-%d') for d in trading_dates]

        daily_selections = {}

        for date in tqdm(trading_dates, desc="日频选股"):
            selected_stocks = self.screen_stocks_vectorized(date, stock_list)
            daily_selections[date] = selected_stocks

        selection_time = time.time() - selection_start_time
        self.performance_stats['selection_time'] = selection_time

        # 6. 计算投资组合收益率
        backtest_start_time = time.time()
        portfolio_returns = self.calculate_portfolio_returns_vectorized(
            daily_selections, start_date, end_date
        )
        backtest_time = time.time() - backtest_start_time
        self.performance_stats['backtest_time'] = backtest_time

        # 7. 计算回测指标
        print("📊 计算回测指标...")
        results = self.calculate_backtest_metrics(portfolio_returns, daily_selections)

        # 8. 性能统计
        total_time = time.time() - total_start_time
        self.performance_stats['total_time'] = total_time

        results['performance_stats'] = self.performance_stats
        results['daily_returns'] = portfolio_returns
        results['daily_selections'] = daily_selections

        return results

    def calculate_backtest_metrics(self, returns: pd.Series, selections: Dict[str, List[str]]) -> Dict[str, Any]:
        """计算回测指标"""
        try:
            returns_array = np.array(returns.dropna(), dtype=float)

            if len(returns_array) == 0:
                return self._empty_results()

            # 基本统计
            total_return = float(np.prod(1 + returns_array) - 1)
            annual_return = float((1 + total_return) ** (252 / len(returns_array)) - 1)
            volatility = float(np.std(returns_array) * np.sqrt(252))
            sharpe_ratio = float(annual_return / volatility) if volatility > 0 else 0.0

            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = float(np.min(drawdown))

            # 胜率和其他指标
            win_rate = float(np.mean(returns_array > 0))
            avg_win = float(np.mean(returns_array[returns_array > 0])) if np.any(returns_array > 0) else 0
            avg_loss = float(np.mean(returns_array[returns_array < 0])) if np.any(returns_array < 0) else 0
            profit_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0

            # 选股统计
            selection_counts = [len(stocks) for stocks in selections.values()]
            avg_selection_count = float(np.mean(selection_counts)) if selection_counts else 0
            max_selection_count = max(selection_counts) if selection_counts else 0

            # 交易统计
            total_trading_days = len(returns_array)
            profitable_days = int(np.sum(returns_array > 0))
            losing_days = int(np.sum(returns_array < 0))

            return {
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'profit_loss_ratio': profit_loss_ratio,
                'avg_selection_count': avg_selection_count,
                'max_selection_count': max_selection_count,
                'total_trading_days': total_trading_days,
                'profitable_days': profitable_days,
                'losing_days': losing_days,
                'cumulative_returns': cumulative_returns
            }

        except Exception as e:
            print(f"计算回测指标失败: {e}")
            return self._empty_results()

    def _empty_results(self) -> Dict[str, Any]:
        """返回空结果"""
        return {
            'total_return': 0.0,
            'annual_return': 0.0,
            'volatility': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'profit_loss_ratio': 0.0,
            'avg_selection_count': 0.0,
            'max_selection_count': 0,
            'total_trading_days': 0,
            'profitable_days': 0,
            'losing_days': 0,
            'cumulative_returns': np.array([])
        }

    def print_results(self, results: Dict[str, Any]):
        """打印回测结果"""
        print(f"\n" + "="*80)
        print(f"📊 优化版创业板选股策略回测结果")
        print(f"="*80)

        # 收益指标
        print(f"\n📈 收益指标:")
        print(f"  总收益率:       {results['total_return']:.2%}")
        print(f"  年化收益率:     {results['annual_return']:.2%}")
        print(f"  年化波动率:     {results['volatility']:.2%}")
        print(f"  夏普比率:       {results['sharpe_ratio']:.2f}")

        # 风险指标
        print(f"\n📉 风险指标:")
        print(f"  最大回撤:       {results['max_drawdown']:.2%}")
        print(f"  胜率:           {results['win_rate']:.2%}")
        print(f"  平均盈利:       {results['avg_win']:.2%}")
        print(f"  平均亏损:       {results['avg_loss']:.2%}")
        print(f"  盈亏比:         {results['profit_loss_ratio']:.2f}")

        # 交易统计
        print(f"\n📊 交易统计:")
        print(f"  总交易天数:     {results['total_trading_days']} 天")
        print(f"  盈利天数:       {results['profitable_days']} 天")
        print(f"  亏损天数:       {results['losing_days']} 天")
        print(f"  平均选股数量:   {results['avg_selection_count']:.1f} 只")
        print(f"  最大选股数量:   {results['max_selection_count']} 只")

        # 性能统计
        if 'performance_stats' in results:
            perf = results['performance_stats']
            print(f"\n⚡ 性能统计:")
            print(f"  数据加载时间:   {perf['data_load_time']:.2f} 秒")
            print(f"  指标计算时间:   {perf['indicator_calc_time']:.2f} 秒")
            print(f"  选股执行时间:   {perf['selection_time']:.2f} 秒")
            print(f"  回测计算时间:   {perf['backtest_time']:.2f} 秒")
            print(f"  总执行时间:     {perf['total_time']:.2f} 秒")
            print(f"  平均每日耗时:   {perf['total_time']/results['total_trading_days']:.3f} 秒/天")

        # 选股样例
        if 'daily_selections' in results:
            selections = results['daily_selections']
            print(f"\n📋 选股记录样例:")

            # 显示前5个有选股的日期
            selection_samples = [(date, stocks) for date, stocks in selections.items() if stocks][:5]

            for date, stocks in selection_samples:
                print(f"  {date}: {len(stocks)} 只股票 - {', '.join(stocks[:3])}{'...' if len(stocks) > 3 else ''}")

            if len(selection_samples) == 0:
                print("  未找到符合条件的选股记录")

        # 策略评价
        print(f"\n🎯 策略评价:")
        if results['annual_return'] > 0.15:
            print(f"  ✅ 年化收益率优秀: {results['annual_return']:.2%}")
        elif results['annual_return'] > 0:
            print(f"  ⚠️  年化收益率一般: {results['annual_return']:.2%}")
        else:
            print(f"  ❌ 年化收益率为负: {results['annual_return']:.2%}")

        if results['sharpe_ratio'] > 1.5:
            print(f"  ✅ 夏普比率优秀: {results['sharpe_ratio']:.2f}")
        elif results['sharpe_ratio'] > 0.5:
            print(f"  ⚠️  夏普比率一般: {results['sharpe_ratio']:.2f}")
        else:
            print(f"  ❌ 夏普比率较差: {results['sharpe_ratio']:.2f}")

        if abs(results['max_drawdown']) < 0.15:
            print(f"  ✅ 最大回撤控制良好: {results['max_drawdown']:.2%}")
        elif abs(results['max_drawdown']) < 0.25:
            print(f"  ⚠️  最大回撤中等: {results['max_drawdown']:.2%}")
        else:
            print(f"  ❌ 最大回撤较大: {results['max_drawdown']:.2%}")

    def save_results(self, results: Dict[str, Any], filename: str = "backtest_results.pkl"):
        """保存回测结果"""
        try:
            filepath = self.cache_dir / filename
            with open(filepath, 'wb') as f:
                pickle.dump(results, f)
            print(f"✅ 回测结果已保存到: {filepath}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

    def cleanup(self):
        """清理资源"""
        if hasattr(self, 'data_manager'):
            self.data_manager.cleanup()

def main():
    """主函数"""
    print("🚀 优化版创业板选股策略回测系统")
    print("=" * 80)

    # 创建回测系统
    backtest_system = OptimizedBacktestSystem()

    try:
        # 设置回测参数
        start_date = '2024-01-01'
        end_date = '2025-06-01'

        print(f"📅 回测参数:")
        print(f"  开始日期: {start_date}")
        print(f"  结束日期: {end_date}")
        print(f"  回测期间: 约17个月")
        print(f"  选股频率: 每日")
        print(f"  调仓频率: 每日")

        print(f"\n🎯 选股条件:")
        print(f"  - 创业板股票 (300开头)")
        print(f"  - 非ST股票")
        print(f"  - 流通市值: 15-300亿")
        print(f"  - WR值 > -20")
        print(f"  - 大单净量 > 0.4")
        print(f"  - 量比 > 2")

        # 执行回测
        print(f"\n🔄 开始执行回测...")
        results = backtest_system.run_optimized_backtest(start_date, end_date)

        # 打印结果
        backtest_system.print_results(results)

        # 保存结果
        backtest_system.save_results(results)

        # 性能总结
        total_time = results.get('performance_stats', {}).get('total_time', 0)
        if total_time > 0:
            print(f"\n🏆 性能目标达成情况:")
            target_time = 10 * 60  # 10分钟
            if total_time <= target_time:
                print(f"  ✅ 执行时间: {total_time:.1f}秒 (目标: {target_time}秒)")
            else:
                print(f"  ⚠️  执行时间: {total_time:.1f}秒 (超过目标: {target_time}秒)")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断执行")
    except Exception as e:
        print(f"\n❌ 回测执行失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        backtest_system.cleanup()
        print("\n🔚 回测系统已关闭")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并行处理工具

提供高效的多线程和多进程处理能力，支持数据获取和计算的并行化。
"""

import threading
import multiprocessing as mp
import concurrent.futures
import queue
import time
from typing import Any, Callable, Dict, List, Optional, Union, Tuple
from functools import partial
import asyncio
import aiohttp
from dataclasses import dataclass

from quantization.utils.logger import get_logger


@dataclass
class TaskResult:
    """任务结果"""
    task_id: str
    success: bool
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0


class ThreadPoolManager:
    """
    线程池管理器
    
    提供高效的多线程任务处理，适用于I/O密集型任务。
    """
    
    def __init__(self, max_workers: Optional[int] = None, thread_name_prefix: str = "Worker"):
        """
        初始化线程池管理器
        
        Args:
            max_workers: 最大工作线程数，None则使用CPU核心数*5
            thread_name_prefix: 线程名称前缀
        """
        self.max_workers = max_workers or min(32, (mp.cpu_count() or 1) * 5)
        self.thread_name_prefix = thread_name_prefix
        
        self.executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=self.max_workers,
            thread_name_prefix=thread_name_prefix
        )
        
        self.logger = get_logger(self.__class__.__name__)
        self.logger.info(f"线程池初始化完成，最大工作线程数: {self.max_workers}")
    
    def submit_task(self, func: Callable, *args, **kwargs) -> concurrent.futures.Future:
        """
        提交单个任务
        
        Args:
            func: 要执行的函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            Future对象
        """
        return self.executor.submit(func, *args, **kwargs)
    
    def submit_batch_tasks(
        self, 
        func: Callable, 
        task_args_list: List[Tuple], 
        timeout: Optional[float] = None
    ) -> List[TaskResult]:
        """
        批量提交任务并等待完成
        
        Args:
            func: 要执行的函数
            task_args_list: 任务参数列表，每个元素是(args, kwargs)元组
            timeout: 超时时间（秒）
            
        Returns:
            任务结果列表
        """
        futures = []
        results = []
        
        # 提交所有任务
        for i, task_args in enumerate(task_args_list):
            if isinstance(task_args, tuple) and len(task_args) == 2:
                args, kwargs = task_args
            else:
                args, kwargs = task_args, {}
            
            future = self.executor.submit(func, *args, **kwargs)
            futures.append((f"task_{i}", future))
        
        # 等待所有任务完成
        for task_id, future in futures:
            start_time = time.time()
            try:
                result = future.result(timeout=timeout)
                execution_time = time.time() - start_time
                
                results.append(TaskResult(
                    task_id=task_id,
                    success=True,
                    result=result,
                    execution_time=execution_time
                ))
                
            except Exception as e:
                execution_time = time.time() - start_time
                
                results.append(TaskResult(
                    task_id=task_id,
                    success=False,
                    error=str(e),
                    execution_time=execution_time
                ))
                
                self.logger.warning(f"任务 {task_id} 执行失败: {str(e)}")
        
        return results
    
    def map_tasks(
        self, 
        func: Callable, 
        iterable: List[Any], 
        timeout: Optional[float] = None,
        chunksize: int = 1
    ) -> List[Any]:
        """
        映射任务到可迭代对象
        
        Args:
            func: 要执行的函数
            iterable: 可迭代对象
            timeout: 超时时间（秒）
            chunksize: 块大小
            
        Returns:
            结果列表
        """
        try:
            results = list(self.executor.map(func, iterable, timeout=timeout, chunksize=chunksize))
            return results
        except Exception as e:
            self.logger.error(f"映射任务执行失败: {str(e)}")
            raise
    
    def shutdown(self, wait: bool = True):
        """关闭线程池"""
        self.executor.shutdown(wait=wait)
        self.logger.info("线程池已关闭")


class ProcessPoolManager:
    """
    进程池管理器
    
    提供高效的多进程任务处理，适用于CPU密集型任务。
    """
    
    def __init__(self, max_workers: Optional[int] = None):
        """
        初始化进程池管理器
        
        Args:
            max_workers: 最大工作进程数，None则使用CPU核心数
        """
        self.max_workers = max_workers or mp.cpu_count()
        
        self.executor = concurrent.futures.ProcessPoolExecutor(
            max_workers=self.max_workers
        )
        
        self.logger = get_logger(self.__class__.__name__)
        self.logger.info(f"进程池初始化完成，最大工作进程数: {self.max_workers}")
    
    def submit_task(self, func: Callable, *args, **kwargs) -> concurrent.futures.Future:
        """
        提交单个任务
        
        Args:
            func: 要执行的函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            Future对象
        """
        return self.executor.submit(func, *args, **kwargs)
    
    def submit_batch_tasks(
        self, 
        func: Callable, 
        task_args_list: List[Tuple], 
        timeout: Optional[float] = None
    ) -> List[TaskResult]:
        """
        批量提交任务并等待完成
        
        Args:
            func: 要执行的函数
            task_args_list: 任务参数列表
            timeout: 超时时间（秒）
            
        Returns:
            任务结果列表
        """
        futures = []
        results = []
        
        # 提交所有任务
        for i, task_args in enumerate(task_args_list):
            if isinstance(task_args, tuple) and len(task_args) == 2:
                args, kwargs = task_args
            else:
                args, kwargs = task_args, {}
            
            future = self.executor.submit(func, *args, **kwargs)
            futures.append((f"task_{i}", future))
        
        # 等待所有任务完成
        for task_id, future in futures:
            start_time = time.time()
            try:
                result = future.result(timeout=timeout)
                execution_time = time.time() - start_time
                
                results.append(TaskResult(
                    task_id=task_id,
                    success=True,
                    result=result,
                    execution_time=execution_time
                ))
                
            except Exception as e:
                execution_time = time.time() - start_time
                
                results.append(TaskResult(
                    task_id=task_id,
                    success=False,
                    error=str(e),
                    execution_time=execution_time
                ))
                
                self.logger.warning(f"任务 {task_id} 执行失败: {str(e)}")
        
        return results
    
    def map_tasks(
        self, 
        func: Callable, 
        iterable: List[Any], 
        timeout: Optional[float] = None,
        chunksize: int = 1
    ) -> List[Any]:
        """
        映射任务到可迭代对象
        
        Args:
            func: 要执行的函数
            iterable: 可迭代对象
            timeout: 超时时间（秒）
            chunksize: 块大小
            
        Returns:
            结果列表
        """
        try:
            results = list(self.executor.map(func, iterable, timeout=timeout, chunksize=chunksize))
            return results
        except Exception as e:
            self.logger.error(f"映射任务执行失败: {str(e)}")
            raise
    
    def shutdown(self, wait: bool = True):
        """关闭进程池"""
        self.executor.shutdown(wait=wait)
        self.logger.info("进程池已关闭")


class AsyncHttpClient:
    """
    异步HTTP客户端
    
    提供高效的异步HTTP请求处理能力。
    """
    
    def __init__(
        self, 
        max_connections: int = 100,
        timeout: float = 30.0,
        retry_attempts: int = 3
    ):
        """
        初始化异步HTTP客户端
        
        Args:
            max_connections: 最大连接数
            timeout: 请求超时时间（秒）
            retry_attempts: 重试次数
        """
        self.max_connections = max_connections
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.retry_attempts = retry_attempts
        
        # 连接器配置
        self.connector = aiohttp.TCPConnector(
            limit=max_connections,
            limit_per_host=20,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        self.session = None
        self.logger = get_logger(self.__class__.__name__)
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            connector=self.connector,
            timeout=self.timeout
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def fetch_single(
        self, 
        url: str, 
        method: str = 'GET',
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Any] = None
    ) -> Dict[str, Any]:
        """
        发送单个HTTP请求
        
        Args:
            url: 请求URL
            method: HTTP方法
            headers: 请求头
            params: 查询参数
            data: 请求数据
            
        Returns:
            响应结果字典
        """
        for attempt in range(self.retry_attempts):
            try:
                async with self.session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    params=params,
                    data=data
                ) as response:
                    content = await response.text()
                    
                    return {
                        'url': url,
                        'status': response.status,
                        'headers': dict(response.headers),
                        'content': content,
                        'success': response.status < 400
                    }
                    
            except Exception as e:
                if attempt == self.retry_attempts - 1:
                    return {
                        'url': url,
                        'status': 0,
                        'headers': {},
                        'content': '',
                        'success': False,
                        'error': str(e)
                    }
                
                # 等待后重试
                await asyncio.sleep(0.5 * (attempt + 1))
    
    async def fetch_batch(
        self, 
        requests: List[Dict[str, Any]],
        max_concurrent: int = 50
    ) -> List[Dict[str, Any]]:
        """
        批量发送HTTP请求
        
        Args:
            requests: 请求列表，每个元素包含url, method等参数
            max_concurrent: 最大并发数
            
        Returns:
            响应结果列表
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def fetch_with_semaphore(request_config):
            async with semaphore:
                return await self.fetch_single(**request_config)
        
        tasks = [fetch_with_semaphore(req) for req in requests]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'url': requests[i].get('url', ''),
                    'status': 0,
                    'headers': {},
                    'content': '',
                    'success': False,
                    'error': str(result)
                })
            else:
                processed_results.append(result)
        
        return processed_results


class DataParallelProcessor:
    """
    数据并行处理器

    专门用于股票数据的并行获取和处理。
    """

    def __init__(
        self,
        thread_pool_size: Optional[int] = None,
        process_pool_size: Optional[int] = None,
        enable_async: bool = True
    ):
        """
        初始化数据并行处理器

        Args:
            thread_pool_size: 线程池大小
            process_pool_size: 进程池大小
            enable_async: 是否启用异步处理
        """
        self.thread_pool = ThreadPoolManager(thread_pool_size)
        self.process_pool = ProcessPoolManager(process_pool_size)
        self.enable_async = enable_async

        self.logger = get_logger(self.__class__.__name__)

        # 性能统计
        self.stats = {
            'total_tasks': 0,
            'successful_tasks': 0,
            'failed_tasks': 0,
            'total_execution_time': 0.0
        }

    def parallel_stock_data_fetch(
        self,
        stock_codes: List[str],
        fetch_func: Callable,
        max_workers: Optional[int] = None,
        timeout: float = 30.0,
        use_processes: bool = False
    ) -> Dict[str, Any]:
        """
        并行获取股票数据

        Args:
            stock_codes: 股票代码列表
            fetch_func: 数据获取函数
            max_workers: 最大工作线程/进程数
            timeout: 超时时间
            use_processes: 是否使用进程池

        Returns:
            获取结果字典
        """
        start_time = time.time()

        # 准备任务参数
        task_args = [(code,) for code in stock_codes]

        # 选择执行器
        if use_processes:
            executor = self.process_pool
        else:
            executor = self.thread_pool

        # 执行批量任务
        results = executor.submit_batch_tasks(fetch_func, task_args, timeout)

        # 统计结果
        successful_results = {}
        failed_results = {}

        for result in results:
            if result.success:
                # 从task_id中提取股票代码索引
                task_index = int(result.task_id.split('_')[1])
                stock_code = stock_codes[task_index]
                successful_results[stock_code] = result.result
                self.stats['successful_tasks'] += 1
            else:
                task_index = int(result.task_id.split('_')[1])
                stock_code = stock_codes[task_index]
                failed_results[stock_code] = result.error
                self.stats['failed_tasks'] += 1

        execution_time = time.time() - start_time
        self.stats['total_tasks'] += len(stock_codes)
        self.stats['total_execution_time'] += execution_time

        self.logger.info(
            f"并行数据获取完成 - 总数: {len(stock_codes)}, "
            f"成功: {len(successful_results)}, "
            f"失败: {len(failed_results)}, "
            f"耗时: {execution_time:.2f}秒"
        )

        return {
            'successful': successful_results,
            'failed': failed_results,
            'stats': {
                'total_count': len(stock_codes),
                'success_count': len(successful_results),
                'failure_count': len(failed_results),
                'success_rate': len(successful_results) / len(stock_codes) if stock_codes else 0,
                'execution_time': execution_time
            }
        }

    def parallel_indicator_calculation(
        self,
        stock_data_dict: Dict[str, Any],
        calc_func: Callable,
        max_workers: Optional[int] = None,
        timeout: float = 60.0
    ) -> Dict[str, Any]:
        """
        并行计算技术指标

        Args:
            stock_data_dict: 股票数据字典
            calc_func: 计算函数
            max_workers: 最大工作进程数
            timeout: 超时时间

        Returns:
            计算结果字典
        """
        start_time = time.time()

        # 准备任务参数
        task_args = [(code, data) for code, data in stock_data_dict.items()]

        # 使用进程池进行CPU密集型计算
        results = self.process_pool.submit_batch_tasks(calc_func, task_args, timeout)

        # 统计结果
        successful_results = {}
        failed_results = {}

        stock_codes = list(stock_data_dict.keys())

        for result in results:
            if result.success:
                task_index = int(result.task_id.split('_')[1])
                stock_code = stock_codes[task_index]
                successful_results[stock_code] = result.result
                self.stats['successful_tasks'] += 1
            else:
                task_index = int(result.task_id.split('_')[1])
                stock_code = stock_codes[task_index]
                failed_results[stock_code] = result.error
                self.stats['failed_tasks'] += 1

        execution_time = time.time() - start_time
        self.stats['total_tasks'] += len(stock_data_dict)
        self.stats['total_execution_time'] += execution_time

        self.logger.info(
            f"并行指标计算完成 - 总数: {len(stock_data_dict)}, "
            f"成功: {len(successful_results)}, "
            f"失败: {len(failed_results)}, "
            f"耗时: {execution_time:.2f}秒"
        )

        return {
            'successful': successful_results,
            'failed': failed_results,
            'stats': {
                'total_count': len(stock_data_dict),
                'success_count': len(successful_results),
                'failure_count': len(failed_results),
                'success_rate': len(successful_results) / len(stock_data_dict) if stock_data_dict else 0,
                'execution_time': execution_time
            }
        }

    async def async_batch_requests(
        self,
        urls: List[str],
        max_concurrent: int = 50,
        timeout: float = 30.0
    ) -> List[Dict[str, Any]]:
        """
        异步批量HTTP请求

        Args:
            urls: URL列表
            max_concurrent: 最大并发数
            timeout: 超时时间

        Returns:
            响应结果列表
        """
        if not self.enable_async:
            raise RuntimeError("异步处理未启用")

        requests = [{'url': url} for url in urls]

        async with AsyncHttpClient(timeout=timeout) as client:
            results = await client.fetch_batch(requests, max_concurrent)

        return results

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            性能统计字典
        """
        total_tasks = self.stats['total_tasks']
        if total_tasks == 0:
            return {
                'total_tasks': 0,
                'success_rate': 0.0,
                'average_execution_time': 0.0,
                'tasks_per_second': 0.0
            }

        success_rate = self.stats['successful_tasks'] / total_tasks
        avg_execution_time = self.stats['total_execution_time'] / total_tasks
        tasks_per_second = total_tasks / self.stats['total_execution_time'] if self.stats['total_execution_time'] > 0 else 0

        return {
            'total_tasks': total_tasks,
            'successful_tasks': self.stats['successful_tasks'],
            'failed_tasks': self.stats['failed_tasks'],
            'success_rate': success_rate,
            'total_execution_time': self.stats['total_execution_time'],
            'average_execution_time': avg_execution_time,
            'tasks_per_second': tasks_per_second
        }

    def reset_stats(self):
        """重置性能统计"""
        self.stats = {
            'total_tasks': 0,
            'successful_tasks': 0,
            'failed_tasks': 0,
            'total_execution_time': 0.0
        }

    def shutdown(self):
        """关闭所有执行器"""
        self.thread_pool.shutdown()
        self.process_pool.shutdown()
        self.logger.info("数据并行处理器已关闭")


# 工具函数
def run_async_function(async_func, *args, **kwargs):
    """
    在同步环境中运行异步函数

    Args:
        async_func: 异步函数
        *args: 位置参数
        **kwargs: 关键字参数

    Returns:
        异步函数的返回值
    """
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    try:
        return loop.run_until_complete(async_func(*args, **kwargs))
    finally:
        if loop.is_running():
            loop.close()


def batch_process_with_progress(
    items: List[Any],
    process_func: Callable,
    batch_size: int = 100,
    max_workers: Optional[int] = None,
    show_progress: bool = True
) -> List[Any]:
    """
    带进度显示的批量处理

    Args:
        items: 要处理的项目列表
        process_func: 处理函数
        batch_size: 批次大小
        max_workers: 最大工作线程数
        show_progress: 是否显示进度

    Returns:
        处理结果列表
    """
    results = []
    total_batches = (len(items) + batch_size - 1) // batch_size

    with ThreadPoolManager(max_workers) as thread_pool:
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            batch_number = i // batch_size + 1

            if show_progress:
                print(f"处理批次 {batch_number}/{total_batches} ({len(batch)} 项)")

            # 并行处理当前批次
            batch_results = thread_pool.map_tasks(process_func, batch)
            results.extend(batch_results)

    return results

"""
Advanced backtesting example using the A-Share Data Acquisition Framework
"""

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
import matplotlib.pyplot as plt
from data_acquisition import BacktestingDataInterface
from data_acquisition.backtesting_interface import DataPreprocessor

class SimpleMovingAverageStrategy:
    """Simple moving average crossover strategy"""
    
    def __init__(self, short_window=20, long_window=50):
        self.short_window = short_window
        self.long_window = long_window
    
    def generate_signals(self, price_data):
        """Generate trading signals"""
        signals = pd.DataFrame(index=price_data.index)
        
        # Calculate moving averages
        signals['short_ma'] = price_data.rolling(window=self.short_window).mean()
        signals['long_ma'] = price_data.rolling(window=self.long_window).mean()
        
        # Generate signals
        signals['signal'] = 0
        signals['signal'][self.short_window:] = np.where(
            signals['short_ma'][self.short_window:] > signals['long_ma'][self.short_window:], 1, 0
        )
        
        # Generate trading orders
        signals['positions'] = signals['signal'].diff()
        
        return signals

def run_single_stock_backtest():
    """Run backtest on a single stock"""
    print("=== Single Stock Backtest Example ===")
    
    # Initialize data interface
    bt_interface = BacktestingDataInterface()
    preprocessor = DataPreprocessor()
    
    # Parameters
    stock_code = "000001.SZ"
    start_date = "2022-01-01"
    end_date = "2023-12-31"
    
    # Get stock data
    print(f"Fetching data for {stock_code}...")
    data = bt_interface.get_stock_data(stock_code, start_date, end_date)
    
    if data is None or data.empty:
        print("No data available")
        return
    
    print(f"Retrieved {len(data)} records")
    
    # Add technical indicators
    data_with_indicators = preprocessor.calculate_technical_indicators(data)
    
    # Initialize strategy
    strategy = SimpleMovingAverageStrategy(short_window=20, long_window=50)
    
    # Generate signals
    signals = strategy.generate_signals(data['close'])
    
    # Calculate returns
    returns = data['close'].pct_change()
    
    # Calculate strategy returns
    strategy_returns = signals['signal'].shift(1) * returns
    
    # Calculate cumulative returns
    cumulative_returns = (1 + returns).cumprod()
    cumulative_strategy_returns = (1 + strategy_returns).cumprod()
    
    # Performance metrics
    total_return = cumulative_returns.iloc[-1] - 1
    strategy_total_return = cumulative_strategy_returns.iloc[-1] - 1
    
    annual_return = (1 + total_return) ** (252 / len(returns)) - 1
    strategy_annual_return = (1 + strategy_total_return) ** (252 / len(strategy_returns)) - 1
    
    volatility = returns.std() * np.sqrt(252)
    strategy_volatility = strategy_returns.std() * np.sqrt(252)
    
    sharpe_ratio = annual_return / volatility if volatility > 0 else 0
    strategy_sharpe_ratio = strategy_annual_return / strategy_volatility if strategy_volatility > 0 else 0
    
    print(f"\nPerformance Results for {stock_code}:")
    print(f"Buy & Hold Total Return: {total_return:.2%}")
    print(f"Strategy Total Return: {strategy_total_return:.2%}")
    print(f"Buy & Hold Annual Return: {annual_return:.2%}")
    print(f"Strategy Annual Return: {strategy_annual_return:.2%}")
    print(f"Buy & Hold Volatility: {volatility:.2%}")
    print(f"Strategy Volatility: {strategy_volatility:.2%}")
    print(f"Buy & Hold Sharpe Ratio: {sharpe_ratio:.2f}")
    print(f"Strategy Sharpe Ratio: {strategy_sharpe_ratio:.2f}")
    
    # Plot results
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 1, 1)
    plt.plot(cumulative_returns.index, cumulative_returns.values, label='Buy & Hold', linewidth=2)
    plt.plot(cumulative_strategy_returns.index, cumulative_strategy_returns.values, label='Strategy', linewidth=2)
    plt.title(f'{stock_code} - Cumulative Returns')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 1, 2)
    plt.plot(data.index, data['close'], label='Price', alpha=0.7)
    plt.plot(signals.index, signals['short_ma'], label=f'MA{strategy.short_window}', linewidth=1)
    plt.plot(signals.index, signals['long_ma'], label=f'MA{strategy.long_window}', linewidth=1)
    
    # Mark buy/sell signals
    buy_signals = signals[signals['positions'] == 1]
    sell_signals = signals[signals['positions'] == -1]
    
    plt.scatter(buy_signals.index, data.loc[buy_signals.index, 'close'], 
               color='green', marker='^', s=100, label='Buy')
    plt.scatter(sell_signals.index, data.loc[sell_signals.index, 'close'], 
               color='red', marker='v', s=100, label='Sell')
    
    plt.title(f'{stock_code} - Price and Signals')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(f'backtest_{stock_code.replace(".", "_")}.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    bt_interface.cleanup()

def run_portfolio_backtest():
    """Run backtest on a portfolio of stocks"""
    print("\n=== Portfolio Backtest Example ===")
    
    bt_interface = BacktestingDataInterface()
    
    # Define universe
    universe = [
        "000001.SZ",  # Ping An Bank
        "000002.SZ",  # Vanke
        "600000.SH",  # Pudong Development Bank
        "600036.SH",  # China Merchants Bank
        "600519.SH"   # Kweichow Moutai
    ]
    
    start_date = "2022-01-01"
    end_date = "2023-12-31"
    
    print(f"Running portfolio backtest on {len(universe)} stocks...")
    
    # Get price matrix
    price_matrix = bt_interface.get_price_matrix(universe, start_date, end_date, 'close')
    
    if price_matrix.empty:
        print("No price data available")
        return
    
    print(f"Price matrix shape: {price_matrix.shape}")
    
    # Fill missing data
    preprocessor = DataPreprocessor()
    price_matrix = preprocessor.fill_missing_data(price_matrix, method='forward', limit=5)
    
    # Calculate returns
    returns_matrix = preprocessor.calculate_returns(price_matrix)
    
    # Simple equal-weight portfolio
    portfolio_weights = np.ones(len(universe)) / len(universe)
    
    # Calculate portfolio returns
    portfolio_returns = (returns_matrix * portfolio_weights).sum(axis=1)
    
    # Calculate cumulative returns
    portfolio_cumulative = (1 + portfolio_returns).cumprod()
    
    # Individual stock cumulative returns
    individual_cumulative = (1 + returns_matrix).cumprod()
    
    # Performance metrics
    portfolio_total_return = portfolio_cumulative.iloc[-1] - 1
    portfolio_annual_return = (1 + portfolio_total_return) ** (252 / len(portfolio_returns)) - 1
    portfolio_volatility = portfolio_returns.std() * np.sqrt(252)
    portfolio_sharpe = portfolio_annual_return / portfolio_volatility if portfolio_volatility > 0 else 0
    
    print(f"\nPortfolio Performance:")
    print(f"Total Return: {portfolio_total_return:.2%}")
    print(f"Annual Return: {portfolio_annual_return:.2%}")
    print(f"Volatility: {portfolio_volatility:.2%}")
    print(f"Sharpe Ratio: {portfolio_sharpe:.2f}")
    
    # Individual stock performance
    print(f"\nIndividual Stock Performance:")
    for stock in universe:
        if stock in individual_cumulative.columns:
            stock_return = individual_cumulative[stock].iloc[-1] - 1
            print(f"{stock}: {stock_return:.2%}")
    
    # Plot portfolio vs individual stocks
    plt.figure(figsize=(12, 6))
    
    # Plot individual stocks
    for stock in universe:
        if stock in individual_cumulative.columns:
            plt.plot(individual_cumulative.index, individual_cumulative[stock], 
                    alpha=0.6, linewidth=1, label=stock)
    
    # Plot portfolio
    plt.plot(portfolio_cumulative.index, portfolio_cumulative, 
            color='black', linewidth=3, label='Equal-Weight Portfolio')
    
    plt.title('Portfolio vs Individual Stocks - Cumulative Returns')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True)
    plt.tight_layout()
    plt.savefig('portfolio_backtest.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    bt_interface.cleanup()

def run_sector_analysis():
    """Run sector-based analysis"""
    print("\n=== Sector Analysis Example ===")
    
    bt_interface = BacktestingDataInterface()
    
    # Define sectors
    sectors = {
        'Banking': ["000001.SZ", "600000.SH", "600036.SH"],
        'Real Estate': ["000002.SZ", "000858.SZ"],
        'Consumer': ["600519.SH", "000858.SZ"]
    }
    
    start_date = "2022-01-01"
    end_date = "2023-12-31"
    
    sector_performance = {}
    
    for sector_name, stocks in sectors.items():
        print(f"Analyzing {sector_name} sector...")
        
        # Get data for sector stocks
        sector_data = bt_interface.get_multiple_stocks_data(stocks, start_date, end_date)
        
        if not sector_data:
            continue
        
        # Calculate sector returns (equal-weighted)
        sector_returns = []
        for stock, data in sector_data.items():
            if data is not None and not data.empty:
                returns = data['close'].pct_change()
                sector_returns.append(returns)
        
        if sector_returns:
            # Combine returns
            sector_returns_df = pd.concat(sector_returns, axis=1)
            sector_avg_returns = sector_returns_df.mean(axis=1)
            
            # Calculate performance metrics
            cumulative_returns = (1 + sector_avg_returns).cumprod()
            total_return = cumulative_returns.iloc[-1] - 1
            annual_return = (1 + total_return) ** (252 / len(sector_avg_returns)) - 1
            volatility = sector_avg_returns.std() * np.sqrt(252)
            
            sector_performance[sector_name] = {
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': annual_return / volatility if volatility > 0 else 0,
                'cumulative_returns': cumulative_returns
            }
    
    # Display sector performance
    print(f"\nSector Performance Summary:")
    print("-" * 60)
    print(f"{'Sector':<15} {'Total Return':<12} {'Annual Return':<13} {'Volatility':<10} {'Sharpe':<8}")
    print("-" * 60)
    
    for sector, metrics in sector_performance.items():
        print(f"{sector:<15} {metrics['total_return']:<12.2%} {metrics['annual_return']:<13.2%} "
              f"{metrics['volatility']:<10.2%} {metrics['sharpe_ratio']:<8.2f}")
    
    # Plot sector comparison
    plt.figure(figsize=(12, 6))
    
    for sector, metrics in sector_performance.items():
        plt.plot(metrics['cumulative_returns'].index, metrics['cumulative_returns'], 
                linewidth=2, label=sector)
    
    plt.title('Sector Performance Comparison')
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.savefig('sector_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    bt_interface.cleanup()

if __name__ == "__main__":
    """Run backtesting examples"""
    
    print("A-Share Data Acquisition Framework - Backtesting Examples")
    print("=" * 60)
    
    try:
        # Run examples
        run_single_stock_backtest()
        run_portfolio_backtest()
        run_sector_analysis()
        
        print("\n" + "=" * 60)
        print("All backtesting examples completed successfully!")
        
    except Exception as e:
        print(f"\nError running backtesting examples: {e}")
        import traceback
        traceback.print_exc()

"""
Cache manager for A-Share data acquisition framework
"""

import pickle
import pandas as pd
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date, timedelta
from pathlib import Path
import hashlib
import json
import os
from ..config.settings import Config
from ..utils.logger import get_cache_logger

class CacheManager:
    """Cache manager for data acquisition framework"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize cache manager
        
        Args:
            config: Configuration instance
        """
        self.config = config or Config()
        self.cache_config = self.config.get_cache_config()
        self.logger = get_cache_logger(config)
        
        # Create cache directory
        self.cache_dir = Path(self.cache_config['cache_dir'])
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Cache subdirectories
        self.stock_cache_dir = self.cache_dir / 'stocks'
        self.index_cache_dir = self.cache_dir / 'indices'
        self.financial_cache_dir = self.cache_dir / 'financial'
        self.info_cache_dir = self.cache_dir / 'info'
        
        for cache_subdir in [self.stock_cache_dir, self.index_cache_dir, 
                           self.financial_cache_dir, self.info_cache_dir]:
            cache_subdir.mkdir(exist_ok=True)
        
        # Cache metadata
        self.metadata_file = self.cache_dir / 'cache_metadata.json'
        self.metadata = self._load_metadata()
        
        self.logger.info(f"Cache manager initialized with directory: {self.cache_dir}")
    
    def _load_metadata(self) -> Dict[str, Any]:
        """Load cache metadata"""
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {'cache_entries': {}, 'created_at': datetime.now().isoformat()}
        except Exception as e:
            self.logger.error(f"Failed to load cache metadata: {e}")
            return {'cache_entries': {}, 'created_at': datetime.now().isoformat()}
    
    def _save_metadata(self):
        """Save cache metadata"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save cache metadata: {e}")
    
    def _generate_cache_key(self, data_type: str, identifier: str, **kwargs) -> str:
        """
        Generate cache key for data
        
        Args:
            data_type: Type of data ('stock', 'index', 'financial', 'info')
            identifier: Stock code or index code
            **kwargs: Additional parameters
            
        Returns:
            str: Cache key
        """
        # Create a string representation of all parameters
        key_parts = [data_type, identifier]
        
        # Add sorted kwargs to ensure consistent key generation
        for key, value in sorted(kwargs.items()):
            if isinstance(value, (date, datetime)):
                value = value.isoformat()
            key_parts.append(f"{key}={value}")
        
        key_string = "|".join(key_parts)
        
        # Generate hash for consistent key length
        return hashlib.md5(key_string.encode('utf-8')).hexdigest()
    
    def _get_cache_file_path(self, data_type: str, cache_key: str) -> Path:
        """
        Get cache file path
        
        Args:
            data_type: Type of data
            cache_key: Cache key
            
        Returns:
            Path: Cache file path
        """
        if data_type == 'stock':
            return self.stock_cache_dir / f"{cache_key}.pkl"
        elif data_type == 'index':
            return self.index_cache_dir / f"{cache_key}.pkl"
        elif data_type == 'financial':
            return self.financial_cache_dir / f"{cache_key}.pkl"
        elif data_type == 'info':
            return self.info_cache_dir / f"{cache_key}.pkl"
        else:
            return self.cache_dir / f"{cache_key}.pkl"
    
    def is_cache_valid(self, cache_key: str) -> bool:
        """
        Check if cache entry is valid (not expired)
        
        Args:
            cache_key: Cache key
            
        Returns:
            bool: True if cache is valid
        """
        if not self.cache_config['enabled']:
            return False
        
        if cache_key not in self.metadata['cache_entries']:
            return False
        
        entry = self.metadata['cache_entries'][cache_key]
        created_at = datetime.fromisoformat(entry['created_at'])
        ttl_delta = timedelta(days=self.cache_config['ttl_days'])
        
        return datetime.now() - created_at < ttl_delta
    
    def get_cached_data(self, data_type: str, identifier: str, **kwargs) -> Optional[pd.DataFrame]:
        """
        Get cached data
        
        Args:
            data_type: Type of data
            identifier: Stock code or identifier
            **kwargs: Additional parameters
            
        Returns:
            pd.DataFrame: Cached data or None if not found/expired
        """
        if not self.cache_config['enabled']:
            return None
        
        try:
            cache_key = self._generate_cache_key(data_type, identifier, **kwargs)
            
            if not self.is_cache_valid(cache_key):
                return None
            
            cache_file = self._get_cache_file_path(data_type, cache_key)
            
            if not cache_file.exists():
                # Remove invalid metadata entry
                if cache_key in self.metadata['cache_entries']:
                    del self.metadata['cache_entries'][cache_key]
                    self._save_metadata()
                return None
            
            # Load cached data
            with open(cache_file, 'rb') as f:
                data = pickle.load(f)
            
            self.logger.debug(f"Cache hit for {data_type}:{identifier}")
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to get cached data: {e}")
            return None
    
    def cache_data(self, data: pd.DataFrame, data_type: str, identifier: str, **kwargs) -> bool:
        """
        Cache data
        
        Args:
            data: Data to cache
            data_type: Type of data
            identifier: Stock code or identifier
            **kwargs: Additional parameters
            
        Returns:
            bool: True if successful
        """
        if not self.cache_config['enabled'] or data is None or data.empty:
            return False
        
        try:
            cache_key = self._generate_cache_key(data_type, identifier, **kwargs)
            cache_file = self._get_cache_file_path(data_type, cache_key)
            
            # Check cache size limits
            if not self._check_cache_size_limit():
                self._cleanup_old_cache()
            
            # Save data to cache
            with open(cache_file, 'wb') as f:
                pickle.dump(data, f)
            
            # Update metadata
            self.metadata['cache_entries'][cache_key] = {
                'data_type': data_type,
                'identifier': identifier,
                'created_at': datetime.now().isoformat(),
                'file_size': cache_file.stat().st_size,
                'record_count': len(data),
                'parameters': kwargs
            }
            
            self._save_metadata()
            
            self.logger.debug(f"Cached {len(data)} records for {data_type}:{identifier}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cache data: {e}")
            return False
    
    def _check_cache_size_limit(self) -> bool:
        """
        Check if cache size is within limits
        
        Returns:
            bool: True if within limits
        """
        try:
            total_size = 0
            for cache_file in self.cache_dir.rglob('*.pkl'):
                total_size += cache_file.stat().st_size
            
            max_size_bytes = self.cache_config['max_size_mb'] * 1024 * 1024
            return total_size < max_size_bytes
            
        except Exception as e:
            self.logger.error(f"Failed to check cache size: {e}")
            return True  # Assume OK if check fails
    
    def _cleanup_old_cache(self):
        """Clean up old cache entries"""
        try:
            # Get all cache entries sorted by creation time
            entries = []
            for cache_key, entry in self.metadata['cache_entries'].items():
                created_at = datetime.fromisoformat(entry['created_at'])
                entries.append((cache_key, created_at, entry))
            
            entries.sort(key=lambda x: x[1])  # Sort by creation time
            
            # Remove oldest 25% of entries
            remove_count = max(1, len(entries) // 4)
            
            for i in range(remove_count):
                cache_key, _, entry = entries[i]
                
                # Remove cache file
                cache_file = self._get_cache_file_path(entry['data_type'], cache_key)
                if cache_file.exists():
                    cache_file.unlink()
                
                # Remove metadata entry
                if cache_key in self.metadata['cache_entries']:
                    del self.metadata['cache_entries'][cache_key]
            
            self._save_metadata()
            self.logger.info(f"Cleaned up {remove_count} old cache entries")
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup cache: {e}")
    
    def clear_cache(self, data_type: Optional[str] = None, identifier: Optional[str] = None):
        """
        Clear cache entries
        
        Args:
            data_type: Type of data to clear (None for all)
            identifier: Specific identifier to clear (None for all)
        """
        try:
            entries_to_remove = []
            
            for cache_key, entry in self.metadata['cache_entries'].items():
                should_remove = True
                
                if data_type and entry['data_type'] != data_type:
                    should_remove = False
                
                if identifier and entry['identifier'] != identifier:
                    should_remove = False
                
                if should_remove:
                    entries_to_remove.append(cache_key)
            
            # Remove cache files and metadata entries
            for cache_key in entries_to_remove:
                entry = self.metadata['cache_entries'][cache_key]
                cache_file = self._get_cache_file_path(entry['data_type'], cache_key)
                
                if cache_file.exists():
                    cache_file.unlink()
                
                del self.metadata['cache_entries'][cache_key]
            
            self._save_metadata()
            self.logger.info(f"Cleared {len(entries_to_remove)} cache entries")
            
        except Exception as e:
            self.logger.error(f"Failed to clear cache: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics
        
        Returns:
            Dict: Cache statistics
        """
        try:
            stats = {
                'enabled': self.cache_config['enabled'],
                'total_entries': len(self.metadata['cache_entries']),
                'cache_dir': str(self.cache_dir),
                'ttl_days': self.cache_config['ttl_days'],
                'max_size_mb': self.cache_config['max_size_mb']
            }
            
            # Calculate total size
            total_size = 0
            valid_entries = 0
            expired_entries = 0
            
            for cache_key, entry in self.metadata['cache_entries'].items():
                total_size += entry.get('file_size', 0)
                
                if self.is_cache_valid(cache_key):
                    valid_entries += 1
                else:
                    expired_entries += 1
            
            stats.update({
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'valid_entries': valid_entries,
                'expired_entries': expired_entries
            })
            
            # Data type breakdown
            data_type_counts = {}
            for entry in self.metadata['cache_entries'].values():
                data_type = entry['data_type']
                data_type_counts[data_type] = data_type_counts.get(data_type, 0) + 1
            
            stats['data_type_breakdown'] = data_type_counts
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get cache stats: {e}")
            return {'error': str(e)}
    
    def cleanup_expired_cache(self):
        """Clean up expired cache entries"""
        try:
            expired_keys = []
            
            for cache_key in self.metadata['cache_entries'].keys():
                if not self.is_cache_valid(cache_key):
                    expired_keys.append(cache_key)
            
            for cache_key in expired_keys:
                entry = self.metadata['cache_entries'][cache_key]
                cache_file = self._get_cache_file_path(entry['data_type'], cache_key)
                
                if cache_file.exists():
                    cache_file.unlink()
                
                del self.metadata['cache_entries'][cache_key]
            
            if expired_keys:
                self._save_metadata()
                self.logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup expired cache: {e}")

"""
A股量化策略回测示例

本文件展示如何使用A股数据采集框架进行量化策略回测
包括移动平均策略、多因子策略、投资组合优化等
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
import matplotlib.pyplot as plt
from data_acquisition import BacktestingDataInterface
from data_acquisition.backtesting_interface import DataPreprocessor

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号

class MovingAverageStrategy:
    """移动平均线交叉策略"""
    
    def __init__(self, short_window=20, long_window=50):
        """
        初始化策略参数
        
        参数:
            short_window: 短期移动平均线周期
            long_window: 长期移动平均线周期
        """
        self.short_window = short_window
        self.long_window = long_window
    
    def generate_signals(self, price_data):
        """
        生成交易信号
        
        参数:
            price_data: 价格数据Series
            
        返回:
            DataFrame: 包含信号的数据框
        """
        signals = pd.DataFrame(index=price_data.index)
        
        # 计算移动平均线
        signals['short_ma'] = price_data.rolling(window=self.short_window).mean()
        signals['long_ma'] = price_data.rolling(window=self.long_window).mean()
        
        # 生成信号
        signals['signal'] = 0
        signals['signal'][self.short_window:] = np.where(
            signals['short_ma'][self.short_window:] > signals['long_ma'][self.short_window:], 1, 0
        )
        
        # 生成交易指令
        signals['positions'] = signals['signal'].diff()
        
        return signals

class MeanReversionStrategy:
    """均值回归策略"""
    
    def __init__(self, window=20, threshold=2.0):
        """
        初始化策略参数
        
        参数:
            window: 计算均值和标准差的窗口期
            threshold: 标准差倍数阈值
        """
        self.window = window
        self.threshold = threshold
    
    def generate_signals(self, price_data):
        """生成均值回归信号"""
        signals = pd.DataFrame(index=price_data.index)
        
        # 计算移动平均和标准差
        signals['mean'] = price_data.rolling(window=self.window).mean()
        signals['std'] = price_data.rolling(window=self.window).std()
        
        # 计算Z分数
        signals['z_score'] = (price_data - signals['mean']) / signals['std']
        
        # 生成信号：价格偏离均值超过阈值时反向操作
        signals['signal'] = 0
        signals.loc[signals['z_score'] > self.threshold, 'signal'] = -1  # 卖出
        signals.loc[signals['z_score'] < -self.threshold, 'signal'] = 1   # 买入
        
        signals['positions'] = signals['signal'].diff()
        
        return signals

def calculate_performance_metrics(returns):
    """
    计算策略表现指标
    
    参数:
        returns: 收益率序列
        
    返回:
        dict: 表现指标字典
    """
    total_return = (1 + returns).prod() - 1
    annual_return = (1 + total_return) ** (252 / len(returns)) - 1
    volatility = returns.std() * np.sqrt(252)
    sharpe_ratio = annual_return / volatility if volatility > 0 else 0
    
    # 最大回撤
    cumulative = (1 + returns).cumprod()
    running_max = cumulative.expanding().max()
    drawdown = (cumulative - running_max) / running_max
    max_drawdown = drawdown.min()
    
    # 胜率
    win_rate = (returns > 0).mean()
    
    return {
        'total_return': total_return,
        'annual_return': annual_return,
        'volatility': volatility,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'win_rate': win_rate
    }

def run_single_stock_backtest():
    """运行单只股票回测"""
    print("=== 单只股票策略回测 ===")
    
    # 初始化数据接口
    bt_interface = BacktestingDataInterface()
    preprocessor = DataPreprocessor()
    
    # 参数设置
    stock_code = "000001.SZ"  # 平安银行
    start_date = "2022-01-01"
    end_date = "2023-12-31"
    
    # 获取股票数据
    print(f"获取 {stock_code} 的数据...")
    data = bt_interface.get_stock_data(stock_code, start_date, end_date)
    
    if data is None or data.empty:
        print("无法获取数据")
        return
    
    print(f"获取到 {len(data)} 条记录")
    
    # 添加技术指标
    data_with_indicators = preprocessor.calculate_technical_indicators(data)
    
    # 初始化策略
    ma_strategy = MovingAverageStrategy(short_window=20, long_window=50)
    mr_strategy = MeanReversionStrategy(window=20, threshold=2.0)
    
    # 生成信号
    ma_signals = ma_strategy.generate_signals(data['close'])
    mr_signals = mr_strategy.generate_signals(data['close'])
    
    # 计算收益率
    returns = data['close'].pct_change()
    
    # 计算策略收益率
    ma_strategy_returns = ma_signals['signal'].shift(1) * returns
    mr_strategy_returns = mr_signals['signal'].shift(1) * returns
    
    # 计算累计收益率
    cumulative_returns = (1 + returns).cumprod()
    ma_cumulative_returns = (1 + ma_strategy_returns).cumprod()
    mr_cumulative_returns = (1 + mr_strategy_returns).cumprod()
    
    # 计算表现指标
    buy_hold_metrics = calculate_performance_metrics(returns.dropna())
    ma_metrics = calculate_performance_metrics(ma_strategy_returns.dropna())
    mr_metrics = calculate_performance_metrics(mr_strategy_returns.dropna())
    
    # 打印结果
    print(f"\n{stock_code} 策略回测结果:")
    print("-" * 60)
    print(f"{'指标':<15} {'买入持有':<12} {'移动平均':<12} {'均值回归':<12}")
    print("-" * 60)
    print(f"{'总收益率':<15} {buy_hold_metrics['total_return']:<12.2%} "
          f"{ma_metrics['total_return']:<12.2%} {mr_metrics['total_return']:<12.2%}")
    print(f"{'年化收益率':<15} {buy_hold_metrics['annual_return']:<12.2%} "
          f"{ma_metrics['annual_return']:<12.2%} {mr_metrics['annual_return']:<12.2%}")
    print(f"{'年化波动率':<15} {buy_hold_metrics['volatility']:<12.2%} "
          f"{ma_metrics['volatility']:<12.2%} {mr_metrics['volatility']:<12.2%}")
    print(f"{'夏普比率':<15} {buy_hold_metrics['sharpe_ratio']:<12.2f} "
          f"{ma_metrics['sharpe_ratio']:<12.2f} {mr_metrics['sharpe_ratio']:<12.2f}")
    print(f"{'最大回撤':<15} {buy_hold_metrics['max_drawdown']:<12.2%} "
          f"{ma_metrics['max_drawdown']:<12.2%} {mr_metrics['max_drawdown']:<12.2%}")
    print(f"{'胜率':<15} {buy_hold_metrics['win_rate']:<12.2%} "
          f"{ma_metrics['win_rate']:<12.2%} {mr_metrics['win_rate']:<12.2%}")
    
    # 绘制结果
    plt.figure(figsize=(15, 10))
    
    # 累计收益率对比
    plt.subplot(2, 2, 1)
    plt.plot(cumulative_returns.index, cumulative_returns.values, 
             label='买入持有', linewidth=2)
    plt.plot(ma_cumulative_returns.index, ma_cumulative_returns.values, 
             label='移动平均策略', linewidth=2)
    plt.plot(mr_cumulative_returns.index, mr_cumulative_returns.values, 
             label='均值回归策略', linewidth=2)
    plt.title(f'{stock_code} - 累计收益率对比')
    plt.legend()
    plt.grid(True)
    
    # 价格和移动平均线
    plt.subplot(2, 2, 2)
    plt.plot(data.index, data['close'], label='收盘价', alpha=0.7)
    plt.plot(ma_signals.index, ma_signals['short_ma'], label=f'MA{ma_strategy.short_window}', linewidth=1)
    plt.plot(ma_signals.index, ma_signals['long_ma'], label=f'MA{ma_strategy.long_window}', linewidth=1)
    
    # 标记买卖信号
    buy_signals = ma_signals[ma_signals['positions'] == 1]
    sell_signals = ma_signals[ma_signals['positions'] == -1]
    
    plt.scatter(buy_signals.index, data.loc[buy_signals.index, 'close'], 
               color='green', marker='^', s=100, label='买入')
    plt.scatter(sell_signals.index, data.loc[sell_signals.index, 'close'], 
               color='red', marker='v', s=100, label='卖出')
    
    plt.title(f'{stock_code} - 移动平均策略信号')
    plt.legend()
    plt.grid(True)
    
    # 均值回归策略
    plt.subplot(2, 2, 3)
    plt.plot(data.index, data['close'], label='收盘价', alpha=0.7)
    plt.plot(mr_signals.index, mr_signals['mean'], label='移动平均', linewidth=1)
    
    # 标记均值回归信号
    mr_buy_signals = mr_signals[mr_signals['positions'] == 1]
    mr_sell_signals = mr_signals[mr_signals['positions'] == -1]
    
    plt.scatter(mr_buy_signals.index, data.loc[mr_buy_signals.index, 'close'], 
               color='green', marker='^', s=100, label='买入')
    plt.scatter(mr_sell_signals.index, data.loc[mr_sell_signals.index, 'close'], 
               color='red', marker='v', s=100, label='卖出')
    
    plt.title(f'{stock_code} - 均值回归策略信号')
    plt.legend()
    plt.grid(True)
    
    # Z分数
    plt.subplot(2, 2, 4)
    plt.plot(mr_signals.index, mr_signals['z_score'], label='Z分数')
    plt.axhline(y=mr_strategy.threshold, color='r', linestyle='--', label=f'上阈值 ({mr_strategy.threshold})')
    plt.axhline(y=-mr_strategy.threshold, color='r', linestyle='--', label=f'下阈值 ({-mr_strategy.threshold})')
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    plt.title('均值回归 Z分数')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(f'strategy_backtest_{stock_code.replace(".", "_")}.png', 
                dpi=300, bbox_inches='tight')
    plt.show()
    
    bt_interface.cleanup()

def run_portfolio_backtest():
    """运行投资组合回测"""
    print("\n=== 投资组合策略回测 ===")
    
    bt_interface = BacktestingDataInterface()
    
    # 定义股票池
    universe = [
        "000001.SZ",  # 平安银行
        "000002.SZ",  # 万科A
        "600000.SH",  # 浦发银行
        "600036.SH",  # 招商银行
        "600519.SH"   # 贵州茅台
    ]
    
    start_date = "2022-01-01"
    end_date = "2023-12-31"
    
    print(f"运行投资组合回测，包含 {len(universe)} 只股票...")
    
    # 获取价格矩阵
    price_matrix = bt_interface.get_price_matrix(universe, start_date, end_date, 'close')
    
    if price_matrix.empty:
        print("无法获取价格数据")
        return
    
    print(f"价格矩阵形状: {price_matrix.shape}")
    
    # 填充缺失数据
    preprocessor = DataPreprocessor()
    price_matrix = preprocessor.fill_missing_data(price_matrix, method='forward', limit=5)
    
    # 计算收益率
    returns_matrix = preprocessor.calculate_returns(price_matrix)
    
    # 不同的投资组合策略
    strategies = {
        '等权重': np.ones(len(universe)) / len(universe),
        '市值加权': np.array([0.3, 0.2, 0.2, 0.2, 0.1]),  # 假设权重
        '风险平价': None  # 将动态计算
    }
    
    # 计算风险平价权重（简化版本）
    if len(returns_matrix) > 60:  # 需要足够的历史数据
        volatilities = returns_matrix.rolling(60).std().iloc[-1]
        if not volatilities.isnull().any():
            inv_vol = 1 / volatilities
            strategies['风险平价'] = inv_vol / inv_vol.sum()
    
    portfolio_results = {}
    
    for strategy_name, weights in strategies.items():
        if weights is None:
            continue
            
        # 计算投资组合收益率
        portfolio_returns = (returns_matrix * weights).sum(axis=1)
        
        # 计算累计收益率
        portfolio_cumulative = (1 + portfolio_returns).cumprod()
        
        # 计算表现指标
        metrics = calculate_performance_metrics(portfolio_returns.dropna())
        
        portfolio_results[strategy_name] = {
            'returns': portfolio_returns,
            'cumulative': portfolio_cumulative,
            'metrics': metrics,
            'weights': weights
        }
    
    # 个股表现
    individual_cumulative = (1 + returns_matrix).cumprod()
    
    # 打印结果
    print(f"\n投资组合策略表现对比:")
    print("-" * 80)
    print(f"{'策略':<12} {'总收益率':<12} {'年化收益率':<12} {'波动率':<10} {'夏普比率':<10} {'最大回撤':<10}")
    print("-" * 80)
    
    for strategy_name, result in portfolio_results.items():
        metrics = result['metrics']
        print(f"{strategy_name:<12} {metrics['total_return']:<12.2%} "
              f"{metrics['annual_return']:<12.2%} {metrics['volatility']:<10.2%} "
              f"{metrics['sharpe_ratio']:<10.2f} {metrics['max_drawdown']:<10.2%}")
    
    # 个股表现
    print(f"\n个股表现:")
    for stock in universe:
        if stock in individual_cumulative.columns:
            stock_return = individual_cumulative[stock].iloc[-1] - 1
            print(f"{stock}: {stock_return:.2%}")
    
    # 绘制对比图
    plt.figure(figsize=(15, 10))
    
    # 投资组合累计收益率对比
    plt.subplot(2, 2, 1)
    for strategy_name, result in portfolio_results.items():
        plt.plot(result['cumulative'].index, result['cumulative'], 
                linewidth=2, label=strategy_name)
    
    plt.title('投资组合策略累计收益率对比')
    plt.legend()
    plt.grid(True)
    
    # 个股累计收益率
    plt.subplot(2, 2, 2)
    for stock in universe:
        if stock in individual_cumulative.columns:
            plt.plot(individual_cumulative.index, individual_cumulative[stock], 
                    alpha=0.6, linewidth=1, label=stock)
    
    plt.title('个股累计收益率')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True)
    
    # 权重分布
    plt.subplot(2, 2, 3)
    strategies_to_plot = {k: v for k, v in strategies.items() if v is not None}
    x = np.arange(len(universe))
    width = 0.25
    
    for i, (strategy_name, weights) in enumerate(strategies_to_plot.items()):
        plt.bar(x + i * width, weights, width, label=strategy_name)
    
    plt.xlabel('股票')
    plt.ylabel('权重')
    plt.title('投资组合权重分布')
    plt.xticks(x + width, [stock.split('.')[0] for stock in universe], rotation=45)
    plt.legend()
    plt.grid(True)
    
    # 风险收益散点图
    plt.subplot(2, 2, 4)
    for strategy_name, result in portfolio_results.items():
        metrics = result['metrics']
        plt.scatter(metrics['volatility'], metrics['annual_return'], 
                   s=100, label=strategy_name)
    
    plt.xlabel('年化波动率')
    plt.ylabel('年化收益率')
    plt.title('风险收益散点图')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('portfolio_backtest.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    bt_interface.cleanup()

def run_sector_rotation_strategy():
    """运行板块轮动策略"""
    print("\n=== 板块轮动策略回测 ===")
    
    bt_interface = BacktestingDataInterface()
    
    # 定义板块
    sectors = {
        '银行': ["000001.SZ", "600000.SH", "600036.SH"],
        '地产': ["000002.SZ", "000858.SZ"],
        '消费': ["600519.SH", "000858.SZ"],
        '科技': ["000725.SZ", "002415.SZ"]
    }
    
    start_date = "2022-01-01"
    end_date = "2023-12-31"
    rebalance_freq = 60  # 60天重新平衡一次
    
    print(f"运行板块轮动策略...")
    
    sector_performance = {}
    all_sector_returns = {}
    
    # 计算各板块表现
    for sector_name, stocks in sectors.items():
        print(f"分析 {sector_name} 板块...")
        
        sector_data = bt_interface.get_multiple_stocks_data(stocks, start_date, end_date)
        
        if not sector_data:
            continue
        
        # 计算板块平均收益率（等权重）
        sector_returns = []
        for stock, data in sector_data.items():
            if data is not None and not data.empty:
                returns = data['close'].pct_change()
                sector_returns.append(returns)
        
        if sector_returns:
            sector_returns_df = pd.concat(sector_returns, axis=1)
            sector_avg_returns = sector_returns_df.mean(axis=1)
            all_sector_returns[sector_name] = sector_avg_returns
    
    if not all_sector_returns:
        print("无法获取板块数据")
        return
    
    # 合并所有板块收益率
    sector_returns_matrix = pd.DataFrame(all_sector_returns)
    sector_returns_matrix = sector_returns_matrix.dropna()
    
    # 板块轮动策略：选择过去N天表现最好的板块
    lookback_period = 20
    strategy_returns = []
    selected_sectors = []
    
    for i in range(lookback_period, len(sector_returns_matrix)):
        # 计算过去N天的累计收益率
        past_returns = sector_returns_matrix.iloc[i-lookback_period:i]
        cumulative_returns = (1 + past_returns).prod() - 1
        
        # 选择表现最好的板块
        best_sector = cumulative_returns.idxmax()
        selected_sectors.append(best_sector)
        
        # 当天的收益率
        daily_return = sector_returns_matrix.iloc[i][best_sector]
        strategy_returns.append(daily_return)
    
    # 转换为Series
    strategy_returns = pd.Series(strategy_returns, 
                                index=sector_returns_matrix.index[lookback_period:])
    
    # 计算基准（等权重所有板块）
    benchmark_returns = sector_returns_matrix.mean(axis=1)
    
    # 计算累计收益率
    strategy_cumulative = (1 + strategy_returns).cumprod()
    benchmark_cumulative = (1 + benchmark_returns).cumprod()
    
    # 计算表现指标
    strategy_metrics = calculate_performance_metrics(strategy_returns)
    benchmark_metrics = calculate_performance_metrics(benchmark_returns)
    
    # 打印结果
    print(f"\n板块轮动策略结果:")
    print("-" * 50)
    print(f"{'指标':<15} {'轮动策略':<12} {'等权基准':<12}")
    print("-" * 50)
    print(f"{'总收益率':<15} {strategy_metrics['total_return']:<12.2%} "
          f"{benchmark_metrics['total_return']:<12.2%}")
    print(f"{'年化收益率':<15} {strategy_metrics['annual_return']:<12.2%} "
          f"{benchmark_metrics['annual_return']:<12.2%}")
    print(f"{'年化波动率':<15} {strategy_metrics['volatility']:<12.2%} "
          f"{benchmark_metrics['volatility']:<12.2%}")
    print(f"{'夏普比率':<15} {strategy_metrics['sharpe_ratio']:<12.2f} "
          f"{benchmark_metrics['sharpe_ratio']:<12.2f}")
    print(f"{'最大回撤':<15} {strategy_metrics['max_drawdown']:<12.2%} "
          f"{benchmark_metrics['max_drawdown']:<12.2%}")
    
    # 板块选择统计
    sector_selection_counts = pd.Series(selected_sectors).value_counts()
    print(f"\n板块选择频率:")
    for sector, count in sector_selection_counts.items():
        print(f"  {sector}: {count} 次 ({count/len(selected_sectors):.1%})")
    
    # 绘制结果
    plt.figure(figsize=(15, 8))
    
    # 累计收益率对比
    plt.subplot(2, 2, 1)
    plt.plot(strategy_cumulative.index, strategy_cumulative, 
             label='板块轮动策略', linewidth=2)
    plt.plot(benchmark_cumulative.index, benchmark_cumulative, 
             label='等权基准', linewidth=2)
    plt.title('板块轮动策略 vs 基准')
    plt.legend()
    plt.grid(True)
    
    # 各板块累计收益率
    plt.subplot(2, 2, 2)
    sector_cumulative = (1 + sector_returns_matrix).cumprod()
    for sector in sectors.keys():
        if sector in sector_cumulative.columns:
            plt.plot(sector_cumulative.index, sector_cumulative[sector], 
                    label=sector, linewidth=1.5)
    plt.title('各板块累计收益率')
    plt.legend()
    plt.grid(True)
    
    # 板块选择时间序列
    plt.subplot(2, 2, 3)
    sector_names = list(sectors.keys())
    sector_codes = {name: i for i, name in enumerate(sector_names)}
    selection_codes = [sector_codes[sector] for sector in selected_sectors]
    
    plt.plot(strategy_returns.index, selection_codes, marker='o', markersize=3)
    plt.yticks(range(len(sector_names)), sector_names)
    plt.title('板块选择时间序列')
    plt.grid(True)
    
    # 板块选择饼图
    plt.subplot(2, 2, 4)
    plt.pie(sector_selection_counts.values, labels=sector_selection_counts.index, 
            autopct='%1.1f%%')
    plt.title('板块选择分布')
    
    plt.tight_layout()
    plt.savefig('sector_rotation_strategy.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    bt_interface.cleanup()

if __name__ == "__main__":
    """运行策略回测示例"""
    
    print("A股数据采集框架 - 量化策略回测示例")
    print("=" * 60)
    
    try:
        # 运行示例
        run_single_stock_backtest()
        run_portfolio_backtest()
        run_sector_rotation_strategy()
        
        print("\n" + "=" * 60)
        print("所有策略回测示例运行完成！")
        
    except Exception as e:
        print(f"\n运行策略回测时出错: {e}")
        import traceback
        traceback.print_exc()

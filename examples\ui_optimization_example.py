#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户体验和接口优化使用示例

演示如何使用配置管理、API接口、可视化和Web界面等功能。
"""

import sys
import os
import time
import asyncio
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

# 导入量化交易模块
try:
    from quantization.utils.logger import get_logger
    from quantization.config import config_manager, ConfigSchema, ConfigScope
    from quantization.visualization import ResultVisualizer, VisualizationConfig
    from quantization.api import api_server
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"模块导入失败: {e}")
    MODULES_AVAILABLE = False

# 可选导入
try:
    from quantization.ui import QuantizationDashboard
    DASHBOARD_AVAILABLE = True
except ImportError:
    DASHBOARD_AVAILABLE = False


def demonstrate_config_management():
    """演示配置管理功能"""
    if not MODULES_AVAILABLE:
        print("配置管理模块不可用，跳过演示")
        return

    logger = get_logger("ConfigDemo")
    logger.info("开始配置管理演示")
    
    # 1. 基本配置操作
    logger.info("1. 基本配置操作")
    
    # 设置配置
    config_manager.set("demo.test_value", 42)
    config_manager.set("demo.test_string", "Hello World")
    config_manager.set("demo.nested.value", {"key": "value"})
    
    # 获取配置
    test_value = config_manager.get("demo.test_value")
    test_string = config_manager.get("demo.test_string")
    nested_value = config_manager.get("demo.nested.value")
    
    logger.info(f"test_value: {test_value}")
    logger.info(f"test_string: {test_string}")
    logger.info(f"nested_value: {nested_value}")
    
    # 2. 配置模式验证
    logger.info("2. 配置模式验证")
    
    # 添加配置模式
    schema = ConfigSchema(
        key="demo.validated_number",
        data_type=int,
        default_value=10,
        required=True,
        description="验证的数字配置",
        min_value=1,
        max_value=100
    )
    config_manager.add_schema(schema)
    
    # 设置有效值
    success = config_manager.set("demo.validated_number", 50, validate=True)
    logger.info(f"设置有效值 50: {success}")
    
    # 尝试设置无效值
    success = config_manager.set("demo.validated_number", 150, validate=True)
    logger.info(f"设置无效值 150: {success}")
    
    # 3. 配置变更监听
    logger.info("3. 配置变更监听")
    
    def config_change_handler(event):
        logger.info(f"配置变更: {event.key} = {event.old_value} -> {event.new_value}")
    
    # 添加监听器
    config_manager.add_change_listener("demo.*", config_change_handler)
    
    # 触发变更
    config_manager.set("demo.monitored_value", "initial")
    config_manager.set("demo.monitored_value", "changed")
    
    # 4. 配置验证
    logger.info("4. 配置验证")
    
    errors = config_manager.validate_all()
    if errors:
        logger.warning(f"配置验证发现错误: {errors}")
    else:
        logger.info("所有配置验证通过")
    
    # 5. 导出配置
    logger.info("5. 导出配置")
    
    output_dir = Path("output/config")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    config_manager.export_config("output/config/demo_config.json")
    logger.info("配置已导出到 output/config/demo_config.json")


def demonstrate_visualization():
    """演示可视化功能"""
    if not MODULES_AVAILABLE:
        print("可视化模块不可用，跳过演示")
        return

    logger = get_logger("VisualizationDemo")
    logger.info("开始可视化演示")
    
    # 创建可视化配置
    viz_config = VisualizationConfig(
        style="seaborn",
        figure_size=(12, 8),
        interactive=True,
        save_format="png"
    )
    
    # 创建可视化器
    visualizer = ResultVisualizer(viz_config)
    
    # 生成模拟回测数据
    logger.info("生成模拟回测数据")
    
    np.random.seed(42)
    n_days = 252
    dates = pd.date_range(start='2023-01-01', periods=n_days, freq='D')
    
    # 模拟日收益率
    daily_returns = np.random.normal(0.0008, 0.02, n_days)
    cumulative_returns = np.cumprod(1 + daily_returns)
    
    # 构建回测结果数据
    portfolio_history = []
    for i, date in enumerate(dates):
        portfolio_history.append({
            'date': date.strftime('%Y-%m-%d'),
            'daily_return': daily_returns[i],
            'cumulative_return': cumulative_returns[i] - 1,
            'portfolio_value': 1000000 * cumulative_returns[i]
        })
    
    # 计算性能指标
    total_return = cumulative_returns[-1] - 1
    annualized_return = np.mean(daily_returns) * 252
    volatility = np.std(daily_returns) * np.sqrt(252)
    sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
    
    # 计算最大回撤
    running_max = np.maximum.accumulate(cumulative_returns)
    drawdown = (cumulative_returns - running_max) / running_max
    max_drawdown = np.min(drawdown)
    
    results = {
        'strategy_name': 'Demo Strategy',
        'start_date': '2023-01-01',
        'end_date': '2023-12-31',
        'portfolio_history': portfolio_history,
        'performance_metrics': {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'annualized_volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': np.mean(daily_returns > 0),
            'calmar_ratio': annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
        }
    }
    
    # 创建回测报告
    logger.info("创建回测报告")
    
    output_dir = "output/visualization"
    report_files = visualizer.create_backtest_report(results, output_dir)
    
    logger.info(f"回测报告已生成，包含以下图表:")
    for chart_type, file_path in report_files.items():
        logger.info(f"  {chart_type}: {file_path}")
    
    # 创建投资组合报告
    logger.info("创建投资组合报告")
    
    portfolio_data = {
        'positions': {
            '000001.SZ': 0.15,
            '000002.SZ': 0.12,
            '600000.SH': 0.18,
            '600036.SH': 0.10,
            '000858.SZ': 0.08,
            '其他': 0.37
        },
        'sector_allocation': {
            '金融': 35.2,
            '科技': 28.5,
            '消费': 18.3,
            '医药': 12.1,
            '其他': 5.9
        }
    }
    
    portfolio_files = visualizer.create_portfolio_report(portfolio_data, output_dir)
    
    logger.info(f"投资组合报告已生成，包含以下图表:")
    for chart_type, file_path in portfolio_files.items():
        logger.info(f"  {chart_type}: {file_path}")


def demonstrate_api_server():
    """演示API服务器功能"""
    if not MODULES_AVAILABLE:
        print("API模块不可用，跳过演示")
        return

    logger = get_logger("APIDemo")
    logger.info("开始API服务器演示")
    
    if not api_server:
        logger.error("API服务器不可用，请安装FastAPI: pip install fastapi uvicorn")
        return
    
    # 模拟组件
    class MockStrategyManager:
        def __init__(self):
            self.strategies = {
                'demo_strategy': {
                    'name': 'Demo Strategy',
                    'description': '演示策略',
                    'parameters': {'param1': 10, 'param2': 0.5},
                    'enabled': True
                }
            }
        
        def list_strategies(self):
            return list(self.strategies.keys())
        
        def get_strategy(self, name):
            return self.strategies.get(name)
        
        def create_strategy(self, name, description, parameters):
            self.strategies[name] = {
                'name': name,
                'description': description,
                'parameters': parameters,
                'enabled': True
            }
            return {'success': True, 'strategy_id': name}
    
    class MockBacktestEngine:
        def __init__(self):
            self.results = {}
        
        def get_result(self, task_id):
            return self.results.get(task_id)
    
    class MockDataManager:
        def get_stock_data(self, symbol, start_date, end_date, fields=None):
            # 返回模拟数据
            dates = pd.date_range(start=start_date, end=end_date, freq='D')
            data = pd.DataFrame({
                'date': dates,
                'close': np.random.uniform(10, 20, len(dates)),
                'volume': np.random.randint(1000000, 10000000, len(dates))
            })
            return data
    
    # 注入模拟组件
    api_server.set_strategy_manager(MockStrategyManager())
    api_server.set_backtest_engine(MockBacktestEngine())
    api_server.set_data_manager(MockDataManager())
    
    logger.info("API服务器组件已配置")
    logger.info("可以通过以下方式启动API服务器:")
    logger.info("  from quantization.api import api_server")
    logger.info("  api_server.run()")
    logger.info("然后访问 http://localhost:8000/docs 查看API文档")


def demonstrate_dashboard():
    """演示Web仪表板功能"""
    logger = get_logger("DashboardDemo")
    logger.info("开始Web仪表板演示")
    
    if not DASHBOARD_AVAILABLE:
        logger.error("仪表板不可用，请安装Streamlit: pip install streamlit")
        return
    
    logger.info("Web仪表板已准备就绪")
    logger.info("可以通过以下方式启动仪表板:")
    logger.info("  streamlit run examples/ui_optimization_example.py")
    logger.info("或者:")
    logger.info("  from quantization.ui import QuantizationDashboard")
    logger.info("  dashboard = QuantizationDashboard()")
    logger.info("  dashboard.run()")


def create_sample_config_files():
    """创建示例配置文件"""
    logger = get_logger("ConfigFiles")
    logger.info("创建示例配置文件")
    
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    # 全局配置
    global_config = {
        "data": {
            "akshare": {
                "enabled": True,
                "timeout": 30
            },
            "cache": {
                "enabled": True,
                "ttl": 3600
            }
        },
        "database": {
            "host": "localhost",
            "port": 3306,
            "name": "quantization",
            "pool_size": 10
        }
    }
    
    # 开发环境配置
    development_config = {
        "system": {
            "log_level": "DEBUG",
            "max_workers": 4
        },
        "api": {
            "debug": True,
            "host": "127.0.0.1",
            "port": 8000
        }
    }
    
    # 生产环境配置
    production_config = {
        "system": {
            "log_level": "INFO",
            "max_workers": 8
        },
        "api": {
            "debug": False,
            "host": "0.0.0.0",
            "port": 8000
        }
    }
    
    # 保存配置文件
    import json
    
    with open(config_dir / "global.json", 'w', encoding='utf-8') as f:
        json.dump(global_config, f, ensure_ascii=False, indent=2)
    
    with open(config_dir / "development.json", 'w', encoding='utf-8') as f:
        json.dump(development_config, f, ensure_ascii=False, indent=2)
    
    with open(config_dir / "production.json", 'w', encoding='utf-8') as f:
        json.dump(production_config, f, ensure_ascii=False, indent=2)
    
    logger.info("示例配置文件已创建:")
    logger.info(f"  {config_dir / 'global.json'}")
    logger.info(f"  {config_dir / 'development.json'}")
    logger.info(f"  {config_dir / 'production.json'}")


def main():
    """主函数"""
    print("=" * 60)
    print("用户体验和接口优化演示")
    print("=" * 60)
    
    # 创建示例配置文件
    print("\n0. 创建示例配置文件")
    print("-" * 30)
    create_sample_config_files()
    
    # 配置管理演示
    print("\n1. 配置管理演示")
    print("-" * 30)
    demonstrate_config_management()
    
    # 可视化演示
    print("\n2. 可视化演示")
    print("-" * 30)
    demonstrate_visualization()
    
    # API服务器演示
    print("\n3. API服务器演示")
    print("-" * 30)
    demonstrate_api_server()
    
    # Web仪表板演示
    print("\n4. Web仪表板演示")
    print("-" * 30)
    demonstrate_dashboard()
    
    print("\n演示完成！")
    print("\n使用说明:")
    print("1. 配置文件已创建在 config/ 目录下")
    print("2. 可视化图表已保存在 output/visualization/ 目录下")
    print("3. 启动API服务器: python -c \"from quantization.api import api_server; api_server.run()\"")
    print("4. 启动Web仪表板: streamlit run examples/ui_optimization_example.py")


# 应用入口点
if __name__ == "__main__":
    import sys

    # 检查是否通过streamlit运行
    if DASHBOARD_AVAILABLE and 'streamlit' in sys.modules:
        # Streamlit模式
        dashboard = QuantizationDashboard()
        dashboard.run()
    else:
        # 普通Python模式
        main()

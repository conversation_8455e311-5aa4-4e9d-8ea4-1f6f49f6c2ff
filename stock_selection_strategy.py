"""
创业板选股策略

选股条件:
1. 创业板股票 (300开头)
2. 非ST股票
3. 流通市值: 15亿 - 300亿
4. WR值 > -20 (即大于80，因为WR通常为负值)
5. 大单净量 > 0.4
6. 量比 > 2 (倍量)

策略逻辑:
- 每日根据条件筛选股票
- 等权重持有筛选出的股票
- 每日调仓
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

from data_acquisition import DataManager
from data_acquisition.utils.technical_indicators import TechnicalIndicators
from data_acquisition.utils.stock_codes import normalize_stock_code

class ChiNextSelectionStrategy:
    """创业板选股策略"""
    
    def __init__(self):
        """初始化策略"""
        self.data_manager = DataManager()
        self.indicators = TechnicalIndicators()
        
        # 选股条件
        self.selection_criteria = {
            'market_cap_min': 15e8,    # 15亿
            'market_cap_max': 300e8,   # 300亿
            'wr_threshold': -20,       # WR > -20 (相当于80)
            'big_order_net_ratio': 0.4, # 大单净量 > 0.4
            'volume_ratio': 2.0        # 量比 > 2
        }
        
        # 回测参数
        self.rebalance_freq = 'daily'  # 每日调仓
        self.max_positions = 20        # 最大持仓数量
        
    def get_chinext_stocks(self) -> List[str]:
        """获取创业板股票列表"""
        try:
            # 获取所有股票列表
            all_stocks = self.data_manager.get_available_stocks()
            
            # 筛选创业板股票 (300开头)
            chinext_stocks = []
            for stock in all_stocks:
                if stock.startswith('300') and stock.endswith('.SZ'):
                    # 排除ST股票
                    if 'ST' not in stock and '*ST' not in stock:
                        chinext_stocks.append(stock)
            
            print(f"找到创业板股票: {len(chinext_stocks)} 只")
            return chinext_stocks[:100]  # 限制数量以提高效率
            
        except Exception as e:
            print(f"获取创业板股票列表失败: {e}")
            # 返回一些常见的创业板股票作为示例
            return [
                '300059.SZ', '300015.SZ', '300033.SZ', '300124.SZ', '300144.SZ',
                '300251.SZ', '300274.SZ', '300296.SZ', '300347.SZ', '300408.SZ',
                '300413.SZ', '300450.SZ', '300498.SZ', '300529.SZ', '300558.SZ',
                '300601.SZ', '300628.SZ', '300661.SZ', '300699.SZ', '300750.SZ'
            ]
    
    def calculate_market_cap(self, stock_code: str, price: float, date: str) -> Optional[float]:
        """
        计算流通市值

        使用真实数据获取流通市值，多种数据源确保准确性

        参数:
            stock_code: 股票代码
            price: 当前价格（备用）
            date: 日期

        返回:
            Optional[float]: 流通市值（元），失败返回None
        """
        try:
            # 方法1: 从akshare获取实时市值数据
            market_cap = self._get_market_cap_from_akshare(stock_code)
            if market_cap is not None:
                return market_cap

            # 方法2: 从股票基本信息获取股本数据
            market_cap = self._get_market_cap_from_stock_info(stock_code, price)
            if market_cap is not None:
                return market_cap

            # 方法3: 基于历史数据和当前价格估算（改进版）
            market_cap = self._estimate_market_cap_improved(stock_code, price, date)
            if market_cap is not None:
                return market_cap

            print(f"无法获取市值数据 {stock_code}")
            return None

        except Exception as e:
            print(f"计算市值失败 {stock_code}: {e}")
            return None

    def _get_market_cap_from_akshare(self, stock_code: str) -> Optional[float]:
        """
        从akshare获取实时市值数据

        参数:
            stock_code: 股票代码

        返回:
            Optional[float]: 流通市值（元），失败返回None
        """
        try:
            import akshare as ak

            symbol = stock_code[:6]  # 提取6位数字代码

            # 尝试获取股票实时数据
            real_time_data = ak.stock_zh_a_spot_em()

            if real_time_data is not None and not real_time_data.empty:
                # 查找对应股票
                target_stock = real_time_data[real_time_data['代码'] == symbol]
                if not target_stock.empty:
                    # 获取流通市值（单位：元）
                    market_cap = target_stock['流通市值'].iloc[0]
                    if pd.notna(market_cap) and market_cap > 0:
                        return float(market_cap)

            return None

        except Exception:
            return None

    def _get_market_cap_from_stock_info(self, stock_code: str, price: float) -> Optional[float]:
        """
        从股票基本信息获取股本数据计算市值

        参数:
            stock_code: 股票代码
            price: 当前价格

        返回:
            Optional[float]: 流通市值（元），失败返回None
        """
        try:
            import akshare as ak

            symbol = stock_code[:6]

            # 获取股票基本信息
            stock_info = ak.stock_individual_info_em(symbol=symbol)

            if stock_info is not None and not stock_info.empty:
                # 查找流通股本信息
                for _, row in stock_info.iterrows():
                    if '流通股' in str(row['item']) or '流通A股' in str(row['item']):
                        shares_str = str(row['value'])
                        # 解析股本数量（可能包含单位如"万股"、"亿股"）
                        shares = self._parse_shares_from_string(shares_str)
                        if shares and shares > 0:
                            return price * shares

            return None

        except Exception:
            return None

    def _parse_shares_from_string(self, shares_str: str) -> Optional[float]:
        """
        解析股本字符串，转换为股数

        参数:
            shares_str: 股本字符串，如"1.23万股"、"2.45亿股"

        返回:
            Optional[float]: 股数，失败返回None
        """
        try:
            import re

            # 移除空格和特殊字符
            shares_str = shares_str.replace(' ', '').replace(',', '')

            # 提取数字和单位
            match = re.search(r'([\d.]+)(万|亿)?', shares_str)
            if match:
                number = float(match.group(1))
                unit = match.group(2)

                if unit == '万':
                    return number * 1e4
                elif unit == '亿':
                    return number * 1e8
                else:
                    return number

            return None

        except Exception:
            return None

    def _estimate_market_cap_improved(self, stock_code: str, price: float, date: str) -> Optional[float]:
        """
        基于历史数据和统计方法改进的市值估算

        参数:
            stock_code: 股票代码
            price: 当前价格
            date: 日期

        返回:
            Optional[float]: 估算的流通市值（元），失败返回None
        """
        try:
            # 获取历史数据用于估算
            end_date = pd.to_datetime(date)
            start_date = end_date - pd.Timedelta(days=30)

            data = self.data_manager.get_stock_data(
                stock_code,
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )

            if data is None or len(data) < 5:
                return None

            # 基于成交额和价格估算流通股本
            # 成交额 = 成交量 * 平均价格
            # 假设日均换手率在合理范围内（0.5%-5%）
            data = data.copy()
            data['avg_price'] = (data['high'] + data['low'] + data['close']) / 3
            data['turnover_amount'] = data['volume'] * data['avg_price']

            # 计算平均成交额
            avg_turnover = data['turnover_amount'].mean()

            # 基于创业板股票的典型换手率估算流通股本
            # 创业板平均换手率约2-3%
            estimated_turnover_rate = 0.025  # 2.5%
            estimated_shares = avg_turnover / (price * estimated_turnover_rate)

            # 合理性检查：创业板流通股本通常在0.5亿-10亿股之间
            if 0.5e8 <= estimated_shares <= 10e8:
                return price * estimated_shares

            return None

        except Exception:
            return None

    def calculate_volume_ratio(self, current_volume: float, avg_volume: float) -> float:
        """计算量比"""
        if avg_volume > 0:
            return current_volume / avg_volume
        return 0
    
    def calculate_big_order_net_ratio(self, stock_code: str, date: str) -> float:
        """
        计算大单净量比例

        基于真实数据计算大单净量，使用多种数据源和技术分析方法

        参数:
            stock_code: 股票代码
            date: 日期

        返回:
            float: 大单净量比例 (-1.0 到 1.0)
        """
        try:
            # 方法1: 尝试使用akshare获取资金流向数据
            big_order_ratio = self._get_money_flow_from_akshare(stock_code, date)
            if big_order_ratio is not None:
                return big_order_ratio

            # 方法2: 基于技术分析计算资金流向
            big_order_ratio = self._calculate_money_flow_technical(stock_code, date)
            if big_order_ratio is not None:
                return big_order_ratio

            # 方法3: 基于价量关系估算
            big_order_ratio = self._estimate_money_flow_from_price_volume(stock_code, date)
            if big_order_ratio is not None:
                return big_order_ratio

            # 如果所有方法都失败，返回中性值
            return 0.0

        except Exception as e:
            print(f"计算大单净量失败 {stock_code}: {e}")  # 暂时使用print，后续会统一日志
            return 0.0

    def _get_money_flow_from_akshare(self, stock_code: str, date: str) -> Optional[float]:
        """
        从akshare获取资金流向数据

        参数:
            stock_code: 股票代码
            date: 日期

        返回:
            Optional[float]: 大单净量比例，失败返回None
        """
        try:
            import akshare as ak

            # 获取个股资金流向数据
            # 注意：akshare的接口可能会变化，需要根据实际情况调整
            symbol = stock_code[:6]  # 提取6位数字代码

            # 尝试获取个股资金流向
            # 注意：akshare接口经常变化，这里使用通用的获取方法
            money_flow_data = ak.stock_individual_fund_flow_rank()

            if money_flow_data is not None and not money_flow_data.empty:
                # 查找对应股票的数据
                target_row = money_flow_data[money_flow_data['代码'] == symbol]
                if not target_row.empty:
                    # 计算大单净量比例
                    big_inflow = target_row['超大单净流入'].iloc[0] + target_row['大单净流入'].iloc[0]
                    total_amount = target_row['成交额'].iloc[0]

                    if total_amount > 0:
                        return big_inflow / total_amount

            return None

        except Exception as e:
            # akshare接口可能不稳定，失败时返回None
            return None

    def _calculate_money_flow_technical(self, stock_code: str, date: str) -> Optional[float]:
        """
        基于技术分析计算资金流向

        使用价量关系和技术指标估算资金流向

        参数:
            stock_code: 股票代码
            date: 日期

        返回:
            Optional[float]: 大单净量比例，失败返回None
        """
        try:
            # 获取最近几天的数据用于计算
            end_date = pd.to_datetime(date)
            start_date = end_date - pd.Timedelta(days=10)

            data = self.data_manager.get_stock_data(
                stock_code,
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )

            if data is None or len(data) < 5:
                return None

            # 计算资金流向指标
            # 方法：基于价格和成交量的关系
            data = data.copy()
            data['price_change'] = data['close'].pct_change()
            data['volume_change'] = data['volume'].pct_change()

            # 计算价量配合度
            # 上涨且放量 -> 正向资金流入
            # 下跌且放量 -> 负向资金流出
            data['money_flow_raw'] = data['price_change'] * data['volume_change']

            # 使用最近3天的平均值
            recent_flow = data['money_flow_raw'].tail(3).mean()

            # 标准化到 -1 到 1 的范围
            if pd.notna(recent_flow):
                # 使用tanh函数进行标准化
                normalized_flow = np.tanh(recent_flow * 10)  # 乘以10增加敏感度
                return float(normalized_flow)

            return None

        except Exception as e:
            return None

    def _estimate_money_flow_from_price_volume(self, stock_code: str, date: str) -> Optional[float]:
        """
        基于价量关系估算资金流向

        使用简化的价量分析方法

        参数:
            stock_code: 股票代码
            date: 日期

        返回:
            Optional[float]: 大单净量比例，失败返回None
        """
        try:
            # 获取当天和前一天的数据
            end_date = pd.to_datetime(date)
            start_date = end_date - pd.Timedelta(days=5)

            data = self.data_manager.get_stock_data(
                stock_code,
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )

            if data is None or len(data) < 2:
                return None

            # 获取最新两天的数据
            latest_data = data.tail(2)
            if len(latest_data) < 2:
                return None

            prev_day = latest_data.iloc[0]
            curr_day = latest_data.iloc[1]

            # 计算价格变化和成交量变化
            price_change = (curr_day['close'] - prev_day['close']) / prev_day['close']
            volume_ratio = curr_day['volume'] / prev_day['volume'] if prev_day['volume'] > 0 else 1

            # 基于价量关系估算资金流向
            if price_change > 0.02 and volume_ratio > 1.5:  # 涨幅>2%且放量>50%
                return min(0.6, price_change * volume_ratio * 0.5)  # 正向流入
            elif price_change < -0.02 and volume_ratio > 1.5:  # 跌幅>2%且放量>50%
                return max(-0.6, price_change * volume_ratio * 0.5)  # 负向流出
            else:
                # 温和变化，基于价格方向给出小幅流向
                return price_change * 0.3  # 温和的资金流向

        except Exception as e:
            return None

    def screen_stocks(self, date: str, stock_pool: List[str]) -> List[str]:
        """
        根据条件筛选股票
        
        参数:
            date: 筛选日期
            stock_pool: 股票池
            
        返回:
            List[str]: 符合条件的股票列表
        """
        selected_stocks = []
        
        print(f"\n📅 {date} 选股筛选...")
        print(f"候选股票池: {len(stock_pool)} 只")
        
        for i, stock_code in enumerate(stock_pool):
            try:
                # 获取股票数据（最近30天）
                end_date = pd.to_datetime(date)
                start_date = end_date - timedelta(days=60)
                
                data = self.data_manager.get_stock_data(
                    stock_code, 
                    start_date.strftime('%Y-%m-%d'), 
                    end_date.strftime('%Y-%m-%d')
                )
                
                if data is None or data.empty or len(data) < 20:
                    continue
                
                # 获取最新数据（使用最后一行）
                if len(data) < 20:  # 需要足够的历史数据
                    continue

                current_data = data.iloc[-1]  # 最新一行数据

                # 计算技术指标
                data_with_indicators = self.indicators.calculate_all_indicators(data)

                if len(data_with_indicators) == 0:
                    continue

                current_indicators = data_with_indicators.iloc[-1]  # 最新指标

                # 条件1: 流通市值检查
                try:
                    close_price = float(current_data['close'])
                    market_cap = self.calculate_market_cap(stock_code, close_price, date)
                    if market_cap is None:
                        continue

                    if not (self.selection_criteria['market_cap_min'] <= market_cap <= self.selection_criteria['market_cap_max']):
                        continue
                except (ValueError, KeyError):
                    continue

                # 条件2: WR值检查
                try:
                    if 'williams_r' not in data_with_indicators.columns or pd.isna(current_indicators['williams_r']):
                        continue

                    wr_value = float(current_indicators['williams_r'])
                    if wr_value <= self.selection_criteria['wr_threshold']:
                        continue
                except (ValueError, KeyError):
                    continue

                # 条件3: 量比检查
                try:
                    avg_volume = float(data['volume'].rolling(20).mean().iloc[-1])
                    current_volume = float(current_data['volume'])

                    if avg_volume <= 0:
                        continue

                    volume_ratio = self.calculate_volume_ratio(current_volume, avg_volume)

                    if volume_ratio <= self.selection_criteria['volume_ratio']:
                        continue
                except (ValueError, KeyError):
                    continue

                # 条件4: 大单净量检查（基于真实数据）
                try:
                    big_order_ratio = self.calculate_big_order_net_ratio(
                        stock_code, date
                    )
                except (ValueError, KeyError):
                    big_order_ratio = 0
                
                if big_order_ratio <= self.selection_criteria['big_order_net_ratio']:
                    continue
                
                # 所有条件都满足，加入选股列表
                selected_stocks.append(stock_code)
                
                print(f"  ✅ {stock_code}: 市值{market_cap/1e8:.1f}亿, WR{wr_value:.1f}, 量比{volume_ratio:.1f}, 大单{big_order_ratio:.2f}")
                
                # 限制选股数量
                if len(selected_stocks) >= self.max_positions:
                    break
                    
            except Exception as e:
                continue
        
        print(f"筛选结果: {len(selected_stocks)} 只股票")
        return selected_stocks
    
    def calculate_portfolio_return(self, selected_stocks: List[str], 
                                 start_date: str, end_date: str) -> float:
        """计算投资组合收益率"""
        if not selected_stocks:
            return 0.0
        
        try:
            returns = []
            
            for stock_code in selected_stocks:
                data = self.data_manager.get_stock_data(stock_code, start_date, end_date)
                
                if data is not None and len(data) >= 2:
                    # 计算期间收益率
                    start_price = data.iloc[0]['close']
                    end_price = data.iloc[-1]['close']
                    stock_return = (end_price - start_price) / start_price
                    returns.append(stock_return)
            
            if returns:
                # 等权重平均收益率
                portfolio_return = float(np.mean(returns))
                return portfolio_return
            else:
                return 0.0
                
        except Exception as e:
            print(f"计算组合收益率失败: {e}")
            return 0.0
    
    def backtest(self, start_date: str, end_date: str) -> Dict:
        """
        回测策略
        
        参数:
            start_date: 回测开始日期
            end_date: 回测结束日期
            
        返回:
            Dict: 回测结果
        """
        print(f"🚀 开始回测创业板选股策略")
        print(f"回测期间: {start_date} 到 {end_date}")
        
        # 获取创业板股票池
        stock_pool = self.get_chinext_stocks()
        
        # 生成交易日期
        date_range = pd.date_range(start=start_date, end=end_date, freq='B')  # 工作日
        
        portfolio_returns = []
        daily_selections = {}
        
        for i, current_date in enumerate(date_range[:-1]):  # 排除最后一天
            date_str = current_date.strftime('%Y-%m-%d')
            next_date_str = date_range[i + 1].strftime('%Y-%m-%d')
            
            # 选股
            selected_stocks = self.screen_stocks(date_str, stock_pool)
            daily_selections[date_str] = selected_stocks
            
            if selected_stocks:
                # 计算次日收益率
                daily_return = self.calculate_portfolio_return(
                    selected_stocks, date_str, next_date_str
                )
                portfolio_returns.append(daily_return)
            else:
                portfolio_returns.append(0.0)
        
        # 计算回测指标
        returns_series = pd.Series(portfolio_returns)
        
        # 基本统计
        try:
            returns_array = np.array(portfolio_returns, dtype=float)

            # 总收益率
            cumulative_return = np.prod(1 + returns_array)
            total_return = float(cumulative_return - 1)

            # 年化收益率
            if len(returns_array) > 0:
                annual_return = float((1 + total_return) ** (252 / len(returns_array)) - 1)
            else:
                annual_return = 0.0

            # 年化波动率
            volatility = float(np.std(returns_array) * np.sqrt(252))

            # 夏普比率
            sharpe_ratio = float(annual_return / volatility) if volatility > 0 else 0.0

        except Exception as e:
            print(f"计算统计指标失败: {e}")
            total_return = 0.0
            annual_return = 0.0
            volatility = 0.0
            sharpe_ratio = 0.0
        
        # 最大回撤
        cumulative_returns = (1 + returns_series).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # 胜率
        win_rate = (returns_series > 0).mean()
        
        # 选股统计
        selection_counts = [len(stocks) for stocks in daily_selections.values()]
        avg_selection_count = np.mean(selection_counts) if selection_counts else 0
        
        results = {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'avg_selection_count': avg_selection_count,
            'total_trading_days': len(returns_series),
            'daily_returns': returns_series,
            'daily_selections': daily_selections,
            'cumulative_returns': cumulative_returns
        }
        
        return results
    
    def print_results(self, results: Dict):
        """打印回测结果"""
        print(f"\n" + "="*60)
        print(f"📊 创业板选股策略回测结果")
        print(f"="*60)
        
        print(f"📈 收益指标:")
        print(f"  总收益率:     {results['total_return']:.2%}")
        print(f"  年化收益率:   {results['annual_return']:.2%}")
        print(f"  年化波动率:   {results['volatility']:.2%}")
        print(f"  夏普比率:     {results['sharpe_ratio']:.2f}")
        
        print(f"\n📉 风险指标:")
        print(f"  最大回撤:     {results['max_drawdown']:.2%}")
        print(f"  胜率:         {results['win_rate']:.2%}")
        
        print(f"\n📊 选股统计:")
        print(f"  平均选股数量: {results['avg_selection_count']:.1f} 只")
        print(f"  交易天数:     {results['total_trading_days']} 天")
        
        # 显示部分选股记录
        print(f"\n📋 部分选股记录:")
        selection_items = list(results['daily_selections'].items())
        for date, stocks in selection_items[:5]:
            print(f"  {date}: {len(stocks)} 只股票 {stocks[:3]}{'...' if len(stocks) > 3 else ''}")
        
        if len(selection_items) > 5:
            print(f"  ... 还有 {len(selection_items) - 5} 个交易日的记录")
    
    def cleanup(self):
        """清理资源"""
        if hasattr(self, 'data_manager'):
            self.data_manager.cleanup()

def main():
    """主函数"""
    strategy = ChiNextSelectionStrategy()
    
    try:
        # 设置回测参数
        end_date = date.today()
        start_date = end_date - timedelta(days=90)  # 最近3个月
        
        print(f"创业板选股策略回测")
        print(f"选股条件:")
        print(f"  - 创业板股票 (300开头)")
        print(f"  - 非ST股票")
        print(f"  - 流通市值: 15-300亿")
        print(f"  - WR值 > -20")
        print(f"  - 大单净量 > 0.4")
        print(f"  - 量比 > 2")
        
        # 执行回测
        results = strategy.backtest(
            start_date.strftime('%Y-%m-%d'),
            end_date.strftime('%Y-%m-%d')
        )
        
        # 打印结果
        strategy.print_results(results)
        
    except Exception as e:
        print(f"回测过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        strategy.cleanup()

if __name__ == "__main__":
    main()

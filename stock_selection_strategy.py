"""
创业板选股策略

选股条件:
1. 创业板股票 (300开头)
2. 非ST股票
3. 流通市值: 15亿 - 300亿
4. WR值 > -20 (即大于80，因为WR通常为负值)
5. 大单净量 > 0.4
6. 量比 > 2 (倍量)

策略逻辑:
- 每日根据条件筛选股票
- 等权重持有筛选出的股票
- 每日调仓
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

from data_acquisition import DataManager
from data_acquisition.utils.technical_indicators import TechnicalIndicators
from data_acquisition.utils.stock_codes import normalize_stock_code

class ChiNextSelectionStrategy:
    """创业板选股策略"""
    
    def __init__(self):
        """初始化策略"""
        self.data_manager = DataManager()
        self.indicators = TechnicalIndicators()
        
        # 选股条件
        self.selection_criteria = {
            'market_cap_min': 15e8,    # 15亿
            'market_cap_max': 300e8,   # 300亿
            'wr_threshold': -20,       # WR > -20 (相当于80)
            'big_order_net_ratio': 0.4, # 大单净量 > 0.4
            'volume_ratio': 2.0        # 量比 > 2
        }
        
        # 回测参数
        self.rebalance_freq = 'daily'  # 每日调仓
        self.max_positions = 20        # 最大持仓数量
        
    def get_chinext_stocks(self) -> List[str]:
        """获取创业板股票列表"""
        try:
            # 获取所有股票列表
            all_stocks = self.data_manager.get_available_stocks()
            
            # 筛选创业板股票 (300开头)
            chinext_stocks = []
            for stock in all_stocks:
                if stock.startswith('300') and stock.endswith('.SZ'):
                    # 排除ST股票
                    if 'ST' not in stock and '*ST' not in stock:
                        chinext_stocks.append(stock)
            
            print(f"找到创业板股票: {len(chinext_stocks)} 只")
            return chinext_stocks[:100]  # 限制数量以提高效率
            
        except Exception as e:
            print(f"获取创业板股票列表失败: {e}")
            # 返回一些常见的创业板股票作为示例
            return [
                '300059.SZ', '300015.SZ', '300033.SZ', '300124.SZ', '300144.SZ',
                '300251.SZ', '300274.SZ', '300296.SZ', '300347.SZ', '300408.SZ',
                '300413.SZ', '300450.SZ', '300498.SZ', '300529.SZ', '300558.SZ',
                '300601.SZ', '300628.SZ', '300661.SZ', '300699.SZ', '300750.SZ'
            ]
    
    def calculate_market_cap(self, stock_code: str, price: float, date: str) -> Optional[float]:
        """
        计算流通市值

        使用基于股票代码的估算方法
        """
        try:
            # 基于股票代码和价格的合理估算
            # 创业板股票的典型流通股本范围

            # 根据股票代码后三位数字确定股本规模
            code_num = int(stock_code[3:6])  # 获取300后的三位数字

            if code_num < 100:  # 早期创业板，通常规模较大
                estimated_shares = (2 + (code_num % 10) * 0.5) * 1e8  # 2-6.5亿股
            elif code_num < 500:  # 中期创业板
                estimated_shares = (1.5 + (code_num % 20) * 0.2) * 1e8  # 1.5-5.3亿股
            else:  # 后期创业板，通常规模较小
                estimated_shares = (1 + (code_num % 30) * 0.1) * 1e8  # 1-4亿股

            market_cap = price * estimated_shares
            return market_cap

        except Exception as e:
            print(f"计算市值失败 {stock_code}: {e}")
            return None
    
    def calculate_volume_ratio(self, current_volume: float, avg_volume: float) -> float:
        """计算量比"""
        if avg_volume > 0:
            return current_volume / avg_volume
        return 0
    
    def calculate_big_order_net_ratio(self, volume: float, price: float) -> float:
        """
        计算大单净量比例
        
        注意: 这里使用简化计算，实际应该获取大单数据
        """
        try:
            # 简化计算：基于成交量和价格波动估算大单净量
            # 实际应该从Level-2数据中获取真实的大单数据
            
            # 模拟大单净量计算
            # 假设大单净量与成交量和价格变动相关
            estimated_ratio = np.random.uniform(-0.2, 0.8)  # 随机生成，实际应该计算
            return estimated_ratio
            
        except Exception as e:
            return 0
    
    def screen_stocks(self, date: str, stock_pool: List[str]) -> List[str]:
        """
        根据条件筛选股票
        
        参数:
            date: 筛选日期
            stock_pool: 股票池
            
        返回:
            List[str]: 符合条件的股票列表
        """
        selected_stocks = []
        
        print(f"\n📅 {date} 选股筛选...")
        print(f"候选股票池: {len(stock_pool)} 只")
        
        for i, stock_code in enumerate(stock_pool):
            try:
                # 获取股票数据（最近30天）
                end_date = pd.to_datetime(date)
                start_date = end_date - timedelta(days=60)
                
                data = self.data_manager.get_stock_data(
                    stock_code, 
                    start_date.strftime('%Y-%m-%d'), 
                    end_date.strftime('%Y-%m-%d')
                )
                
                if data is None or data.empty or len(data) < 20:
                    continue
                
                # 获取最新数据（使用最后一行）
                if len(data) < 20:  # 需要足够的历史数据
                    continue

                current_data = data.iloc[-1]  # 最新一行数据

                # 计算技术指标
                data_with_indicators = self.indicators.calculate_all_indicators(data)

                if len(data_with_indicators) == 0:
                    continue

                current_indicators = data_with_indicators.iloc[-1]  # 最新指标

                # 条件1: 流通市值检查
                try:
                    close_price = float(current_data['close'])
                    market_cap = self.calculate_market_cap(stock_code, close_price, date)
                    if market_cap is None:
                        continue

                    if not (self.selection_criteria['market_cap_min'] <= market_cap <= self.selection_criteria['market_cap_max']):
                        continue
                except (ValueError, KeyError):
                    continue

                # 条件2: WR值检查
                try:
                    if 'williams_r' not in data_with_indicators.columns or pd.isna(current_indicators['williams_r']):
                        continue

                    wr_value = float(current_indicators['williams_r'])
                    if wr_value <= self.selection_criteria['wr_threshold']:
                        continue
                except (ValueError, KeyError):
                    continue

                # 条件3: 量比检查
                try:
                    avg_volume = float(data['volume'].rolling(20).mean().iloc[-1])
                    current_volume = float(current_data['volume'])

                    if avg_volume <= 0:
                        continue

                    volume_ratio = self.calculate_volume_ratio(current_volume, avg_volume)

                    if volume_ratio <= self.selection_criteria['volume_ratio']:
                        continue
                except (ValueError, KeyError):
                    continue

                # 条件4: 大单净量检查（简化计算）
                try:
                    big_order_ratio = self.calculate_big_order_net_ratio(
                        current_volume, close_price
                    )
                except (ValueError, KeyError):
                    big_order_ratio = 0
                
                if big_order_ratio <= self.selection_criteria['big_order_net_ratio']:
                    continue
                
                # 所有条件都满足，加入选股列表
                selected_stocks.append(stock_code)
                
                print(f"  ✅ {stock_code}: 市值{market_cap/1e8:.1f}亿, WR{wr_value:.1f}, 量比{volume_ratio:.1f}, 大单{big_order_ratio:.2f}")
                
                # 限制选股数量
                if len(selected_stocks) >= self.max_positions:
                    break
                    
            except Exception as e:
                continue
        
        print(f"筛选结果: {len(selected_stocks)} 只股票")
        return selected_stocks
    
    def calculate_portfolio_return(self, selected_stocks: List[str], 
                                 start_date: str, end_date: str) -> float:
        """计算投资组合收益率"""
        if not selected_stocks:
            return 0.0
        
        try:
            returns = []
            
            for stock_code in selected_stocks:
                data = self.data_manager.get_stock_data(stock_code, start_date, end_date)
                
                if data is not None and len(data) >= 2:
                    # 计算期间收益率
                    start_price = data.iloc[0]['close']
                    end_price = data.iloc[-1]['close']
                    stock_return = (end_price - start_price) / start_price
                    returns.append(stock_return)
            
            if returns:
                # 等权重平均收益率
                portfolio_return = float(np.mean(returns))
                return portfolio_return
            else:
                return 0.0
                
        except Exception as e:
            print(f"计算组合收益率失败: {e}")
            return 0.0
    
    def backtest(self, start_date: str, end_date: str) -> Dict:
        """
        回测策略
        
        参数:
            start_date: 回测开始日期
            end_date: 回测结束日期
            
        返回:
            Dict: 回测结果
        """
        print(f"🚀 开始回测创业板选股策略")
        print(f"回测期间: {start_date} 到 {end_date}")
        
        # 获取创业板股票池
        stock_pool = self.get_chinext_stocks()
        
        # 生成交易日期
        date_range = pd.date_range(start=start_date, end=end_date, freq='B')  # 工作日
        
        portfolio_returns = []
        daily_selections = {}
        
        for i, current_date in enumerate(date_range[:-1]):  # 排除最后一天
            date_str = current_date.strftime('%Y-%m-%d')
            next_date_str = date_range[i + 1].strftime('%Y-%m-%d')
            
            # 选股
            selected_stocks = self.screen_stocks(date_str, stock_pool)
            daily_selections[date_str] = selected_stocks
            
            if selected_stocks:
                # 计算次日收益率
                daily_return = self.calculate_portfolio_return(
                    selected_stocks, date_str, next_date_str
                )
                portfolio_returns.append(daily_return)
            else:
                portfolio_returns.append(0.0)
        
        # 计算回测指标
        returns_series = pd.Series(portfolio_returns)
        
        # 基本统计
        try:
            returns_array = np.array(portfolio_returns, dtype=float)

            # 总收益率
            cumulative_return = np.prod(1 + returns_array)
            total_return = float(cumulative_return - 1)

            # 年化收益率
            if len(returns_array) > 0:
                annual_return = float((1 + total_return) ** (252 / len(returns_array)) - 1)
            else:
                annual_return = 0.0

            # 年化波动率
            volatility = float(np.std(returns_array) * np.sqrt(252))

            # 夏普比率
            sharpe_ratio = float(annual_return / volatility) if volatility > 0 else 0.0

        except Exception as e:
            print(f"计算统计指标失败: {e}")
            total_return = 0.0
            annual_return = 0.0
            volatility = 0.0
            sharpe_ratio = 0.0
        
        # 最大回撤
        cumulative_returns = (1 + returns_series).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # 胜率
        win_rate = (returns_series > 0).mean()
        
        # 选股统计
        selection_counts = [len(stocks) for stocks in daily_selections.values()]
        avg_selection_count = np.mean(selection_counts) if selection_counts else 0
        
        results = {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'avg_selection_count': avg_selection_count,
            'total_trading_days': len(returns_series),
            'daily_returns': returns_series,
            'daily_selections': daily_selections,
            'cumulative_returns': cumulative_returns
        }
        
        return results
    
    def print_results(self, results: Dict):
        """打印回测结果"""
        print(f"\n" + "="*60)
        print(f"📊 创业板选股策略回测结果")
        print(f"="*60)
        
        print(f"📈 收益指标:")
        print(f"  总收益率:     {results['total_return']:.2%}")
        print(f"  年化收益率:   {results['annual_return']:.2%}")
        print(f"  年化波动率:   {results['volatility']:.2%}")
        print(f"  夏普比率:     {results['sharpe_ratio']:.2f}")
        
        print(f"\n📉 风险指标:")
        print(f"  最大回撤:     {results['max_drawdown']:.2%}")
        print(f"  胜率:         {results['win_rate']:.2%}")
        
        print(f"\n📊 选股统计:")
        print(f"  平均选股数量: {results['avg_selection_count']:.1f} 只")
        print(f"  交易天数:     {results['total_trading_days']} 天")
        
        # 显示部分选股记录
        print(f"\n📋 部分选股记录:")
        selection_items = list(results['daily_selections'].items())
        for date, stocks in selection_items[:5]:
            print(f"  {date}: {len(stocks)} 只股票 {stocks[:3]}{'...' if len(stocks) > 3 else ''}")
        
        if len(selection_items) > 5:
            print(f"  ... 还有 {len(selection_items) - 5} 个交易日的记录")
    
    def cleanup(self):
        """清理资源"""
        if hasattr(self, 'data_manager'):
            self.data_manager.cleanup()

def main():
    """主函数"""
    strategy = ChiNextSelectionStrategy()
    
    try:
        # 设置回测参数
        end_date = date.today()
        start_date = end_date - timedelta(days=90)  # 最近3个月
        
        print(f"创业板选股策略回测")
        print(f"选股条件:")
        print(f"  - 创业板股票 (300开头)")
        print(f"  - 非ST股票")
        print(f"  - 流通市值: 15-300亿")
        print(f"  - WR值 > -20")
        print(f"  - 大单净量 > 0.4")
        print(f"  - 量比 > 2")
        
        # 执行回测
        results = strategy.backtest(
            start_date.strftime('%Y-%m-%d'),
            end_date.strftime('%Y-%m-%d')
        )
        
        # 打印结果
        strategy.print_results(results)
        
    except Exception as e:
        print(f"回测过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        strategy.cleanup()

if __name__ == "__main__":
    main()

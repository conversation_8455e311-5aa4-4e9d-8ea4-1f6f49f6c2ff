#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多级缓存管理器

实现高效的多级缓存系统，包括内存缓存、磁盘缓存和缓存压缩。
"""

import os
import pickle
import gzip
import threading
import time
import hashlib
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union, List
from pathlib import Path
from collections import OrderedDict
import weakref

from quantization.utils.logger import get_logger


class LRUCache:
    """
    LRU（最近最少使用）内存缓存
    
    线程安全的LRU缓存实现，支持TTL（生存时间）。
    """
    
    def __init__(self, max_size: int = 1000, default_ttl: float = 3600):
        """
        初始化LRU缓存
        
        Args:
            max_size: 最大缓存条目数
            default_ttl: 默认生存时间（秒）
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        
        # 缓存数据和元数据
        self._cache = OrderedDict()
        self._timestamps = {}  # 存储时间戳
        self._ttls = {}        # 存储TTL
        self._access_counts = {}  # 访问计数
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 统计信息
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expired': 0
        }
        
        self.logger = get_logger(f"{self.__class__.__name__}")
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值，如果不存在或已过期则返回None
        """
        with self._lock:
            if key not in self._cache:
                self._stats['misses'] += 1
                return None
            
            # 检查是否过期
            if self._is_expired(key):
                self._remove_key(key)
                self._stats['expired'] += 1
                self._stats['misses'] += 1
                return None
            
            # 更新访问顺序（移到末尾）
            value = self._cache.pop(key)
            self._cache[key] = value
            
            # 更新访问计数
            self._access_counts[key] = self._access_counts.get(key, 0) + 1
            
            self._stats['hits'] += 1
            return value
    
    def put(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒），None使用默认TTL
        """
        with self._lock:
            current_time = time.time()
            ttl = ttl if ttl is not None else self.default_ttl
            
            # 如果键已存在，更新值
            if key in self._cache:
                self._cache.pop(key)
            
            # 检查是否需要清理空间
            while len(self._cache) >= self.max_size:
                self._evict_lru()
            
            # 添加新值
            self._cache[key] = value
            self._timestamps[key] = current_time
            self._ttls[key] = ttl
            self._access_counts[key] = 1
    
    def delete(self, key: str) -> bool:
        """
        删除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功删除
        """
        with self._lock:
            if key in self._cache:
                self._remove_key(key)
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._timestamps.clear()
            self._ttls.clear()
            self._access_counts.clear()
    
    def _is_expired(self, key: str) -> bool:
        """检查键是否过期"""
        if key not in self._timestamps:
            return True
        
        current_time = time.time()
        timestamp = self._timestamps[key]
        ttl = self._ttls.get(key, self.default_ttl)
        
        return current_time - timestamp > ttl
    
    def _remove_key(self, key: str) -> None:
        """移除键及其相关数据"""
        self._cache.pop(key, None)
        self._timestamps.pop(key, None)
        self._ttls.pop(key, None)
        self._access_counts.pop(key, None)
    
    def _evict_lru(self) -> None:
        """驱逐最近最少使用的项"""
        if not self._cache:
            return
        
        # 获取最少使用的键（OrderedDict的第一个键）
        lru_key = next(iter(self._cache))
        self._remove_key(lru_key)
        self._stats['evictions'] += 1
    
    def cleanup_expired(self) -> int:
        """
        清理过期项
        
        Returns:
            清理的项目数量
        """
        with self._lock:
            expired_keys = []
            
            for key in list(self._cache.keys()):
                if self._is_expired(key):
                    expired_keys.append(key)
            
            for key in expired_keys:
                self._remove_key(key)
            
            self._stats['expired'] += len(expired_keys)
            return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
            
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'hit_rate': hit_rate,
                'evictions': self._stats['evictions'],
                'expired': self._stats['expired']
            }


class DiskCache:
    """
    磁盘缓存
    
    支持压缩的磁盘缓存实现。
    """
    
    def __init__(
        self, 
        cache_dir: str, 
        max_size_mb: int = 1000,
        compress: bool = True,
        default_ttl: float = 86400  # 24小时
    ):
        """
        初始化磁盘缓存
        
        Args:
            cache_dir: 缓存目录
            max_size_mb: 最大缓存大小（MB）
            compress: 是否启用压缩
            default_ttl: 默认生存时间（秒）
        """
        self.cache_dir = Path(cache_dir)
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.compress = compress
        self.default_ttl = default_ttl
        
        # 创建缓存目录
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 统计信息
        self._stats = {
            'hits': 0,
            'misses': 0,
            'writes': 0,
            'deletes': 0,
            'size_bytes': 0
        }
        
        self.logger = get_logger(f"{self.__class__.__name__}")
        
        # 初始化时计算当前缓存大小
        self._update_cache_size()
    
    def _get_cache_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        # 使用MD5哈希避免文件名过长或包含特殊字符
        key_hash = hashlib.md5(key.encode()).hexdigest()
        extension = '.gz' if self.compress else '.pkl'
        return self.cache_dir / f"{key_hash}{extension}"
    
    def _get_metadata_path(self, key: str) -> Path:
        """获取元数据文件路径"""
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.meta"
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值，如果不存在或已过期则返回None
        """
        with self._lock:
            cache_path = self._get_cache_path(key)
            meta_path = self._get_metadata_path(key)
            
            if not cache_path.exists() or not meta_path.exists():
                self._stats['misses'] += 1
                return None
            
            try:
                # 读取元数据
                with open(meta_path, 'rb') as f:
                    metadata = pickle.load(f)
                
                # 检查是否过期
                if self._is_expired(metadata):
                    self._delete_cache_files(cache_path, meta_path)
                    self._stats['misses'] += 1
                    return None
                
                # 读取缓存数据
                if self.compress:
                    with gzip.open(cache_path, 'rb') as f:
                        value = pickle.load(f)
                else:
                    with open(cache_path, 'rb') as f:
                        value = pickle.load(f)
                
                self._stats['hits'] += 1
                return value
                
            except Exception as e:
                self.logger.warning(f"读取磁盘缓存失败: {str(e)}")
                self._delete_cache_files(cache_path, meta_path)
                self._stats['misses'] += 1
                return None
    
    def put(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒）
            
        Returns:
            是否成功设置
        """
        with self._lock:
            try:
                cache_path = self._get_cache_path(key)
                meta_path = self._get_metadata_path(key)
                
                # 准备元数据
                ttl = ttl if ttl is not None else self.default_ttl
                metadata = {
                    'key': key,
                    'created_at': time.time(),
                    'ttl': ttl,
                    'compressed': self.compress
                }
                
                # 写入缓存数据
                if self.compress:
                    with gzip.open(cache_path, 'wb') as f:
                        pickle.dump(value, f)
                else:
                    with open(cache_path, 'wb') as f:
                        pickle.dump(value, f)
                
                # 写入元数据
                with open(meta_path, 'wb') as f:
                    pickle.dump(metadata, f)
                
                self._stats['writes'] += 1
                self._update_cache_size()
                
                # 检查缓存大小限制
                self._cleanup_if_needed()
                
                return True
                
            except Exception as e:
                self.logger.error(f"写入磁盘缓存失败: {str(e)}")
                return False

    def delete(self, key: str) -> bool:
        """
        删除缓存项

        Args:
            key: 缓存键

        Returns:
            是否成功删除
        """
        with self._lock:
            cache_path = self._get_cache_path(key)
            meta_path = self._get_metadata_path(key)

            if cache_path.exists() or meta_path.exists():
                self._delete_cache_files(cache_path, meta_path)
                self._stats['deletes'] += 1
                self._update_cache_size()
                return True
            return False

    def _is_expired(self, metadata: Dict[str, Any]) -> bool:
        """检查缓存是否过期"""
        current_time = time.time()
        created_at = metadata.get('created_at', 0)
        ttl = metadata.get('ttl', self.default_ttl)

        return current_time - created_at > ttl

    def _delete_cache_files(self, cache_path: Path, meta_path: Path) -> None:
        """删除缓存文件和元数据文件"""
        try:
            if cache_path.exists():
                cache_path.unlink()
            if meta_path.exists():
                meta_path.unlink()
        except Exception as e:
            self.logger.warning(f"删除缓存文件失败: {str(e)}")

    def _update_cache_size(self) -> None:
        """更新缓存大小统计"""
        try:
            total_size = 0
            for file_path in self.cache_dir.iterdir():
                if file_path.is_file():
                    total_size += file_path.stat().st_size

            self._stats['size_bytes'] = total_size

        except Exception as e:
            self.logger.warning(f"更新缓存大小统计失败: {str(e)}")

    def _cleanup_if_needed(self) -> None:
        """如果需要则清理缓存"""
        if self._stats['size_bytes'] > self.max_size_bytes:
            self.cleanup_expired()

            # 如果仍然超过限制，删除最旧的文件
            if self._stats['size_bytes'] > self.max_size_bytes:
                self._cleanup_oldest_files()

    def _cleanup_oldest_files(self) -> None:
        """清理最旧的文件"""
        try:
            # 获取所有缓存文件及其修改时间
            cache_files = []
            for file_path in self.cache_dir.iterdir():
                if file_path.suffix in ['.pkl', '.gz']:
                    cache_files.append((file_path, file_path.stat().st_mtime))

            # 按修改时间排序
            cache_files.sort(key=lambda x: x[1])

            # 删除最旧的文件直到满足大小限制
            for file_path, _ in cache_files:
                if self._stats['size_bytes'] <= self.max_size_bytes * 0.8:  # 保留20%空间
                    break

                # 删除缓存文件和对应的元数据文件
                meta_path = file_path.with_suffix('.meta')
                self._delete_cache_files(file_path, meta_path)
                self._update_cache_size()

        except Exception as e:
            self.logger.error(f"清理最旧文件失败: {str(e)}")

    def cleanup_expired(self) -> int:
        """
        清理过期的缓存项

        Returns:
            清理的项目数量
        """
        with self._lock:
            cleaned_count = 0

            try:
                # 遍历所有元数据文件
                for meta_path in self.cache_dir.glob('*.meta'):
                    try:
                        with open(meta_path, 'rb') as f:
                            metadata = pickle.load(f)

                        if self._is_expired(metadata):
                            # 获取对应的缓存文件路径
                            key_hash = meta_path.stem
                            cache_path = None

                            # 查找对应的缓存文件
                            for ext in ['.pkl', '.gz']:
                                potential_path = self.cache_dir / f"{key_hash}{ext}"
                                if potential_path.exists():
                                    cache_path = potential_path
                                    break

                            # 删除过期文件
                            if cache_path:
                                self._delete_cache_files(cache_path, meta_path)
                                cleaned_count += 1
                            else:
                                # 只删除元数据文件
                                meta_path.unlink()
                                cleaned_count += 1

                    except Exception as e:
                        self.logger.warning(f"处理元数据文件 {meta_path} 失败: {str(e)}")
                        continue

                self._update_cache_size()

            except Exception as e:
                self.logger.error(f"清理过期缓存失败: {str(e)}")

            return cleaned_count

    def get_stats(self) -> Dict[str, Any]:
        """获取磁盘缓存统计信息"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0

            return {
                'size_bytes': self._stats['size_bytes'],
                'size_mb': self._stats['size_bytes'] / (1024 * 1024),
                'max_size_mb': self.max_size_bytes / (1024 * 1024),
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'hit_rate': hit_rate,
                'writes': self._stats['writes'],
                'deletes': self._stats['deletes'],
                'compress': self.compress
            }


class MultiLevelCache:
    """
    多级缓存管理器

    结合内存缓存和磁盘缓存，提供高效的多级缓存服务。
    """

    def __init__(
        self,
        memory_cache_size: int = 1000,
        memory_ttl: float = 3600,
        disk_cache_dir: str = "./cache",
        disk_cache_size_mb: int = 1000,
        disk_ttl: float = 86400,
        enable_compression: bool = True
    ):
        """
        初始化多级缓存

        Args:
            memory_cache_size: 内存缓存大小
            memory_ttl: 内存缓存TTL（秒）
            disk_cache_dir: 磁盘缓存目录
            disk_cache_size_mb: 磁盘缓存大小（MB）
            disk_ttl: 磁盘缓存TTL（秒）
            enable_compression: 是否启用压缩
        """
        self.memory_cache = LRUCache(memory_cache_size, memory_ttl)
        self.disk_cache = DiskCache(disk_cache_dir, disk_cache_size_mb, enable_compression, disk_ttl)

        self.logger = get_logger(self.__class__.__name__)

        # 启动清理线程
        self._start_cleanup_thread()

    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值（先查内存，再查磁盘）

        Args:
            key: 缓存键

        Returns:
            缓存值
        """
        # 先从内存缓存获取
        value = self.memory_cache.get(key)
        if value is not None:
            return value

        # 从磁盘缓存获取
        value = self.disk_cache.get(key)
        if value is not None:
            # 将磁盘缓存的值放入内存缓存
            self.memory_cache.put(key, value)
            return value

        return None

    def put(self, key: str, value: Any, memory_ttl: Optional[float] = None, disk_ttl: Optional[float] = None) -> None:
        """
        设置缓存值（同时设置内存和磁盘缓存）

        Args:
            key: 缓存键
            value: 缓存值
            memory_ttl: 内存缓存TTL
            disk_ttl: 磁盘缓存TTL
        """
        # 设置内存缓存
        self.memory_cache.put(key, value, memory_ttl)

        # 设置磁盘缓存
        self.disk_cache.put(key, value, disk_ttl)

    def delete(self, key: str) -> bool:
        """
        删除缓存项（同时删除内存和磁盘缓存）

        Args:
            key: 缓存键

        Returns:
            是否成功删除
        """
        memory_deleted = self.memory_cache.delete(key)
        disk_deleted = self.disk_cache.delete(key)

        return memory_deleted or disk_deleted

    def clear(self) -> None:
        """清空所有缓存"""
        self.memory_cache.clear()
        # 磁盘缓存清空需要删除所有文件
        try:
            for file_path in self.disk_cache.cache_dir.iterdir():
                if file_path.is_file():
                    file_path.unlink()
            self.disk_cache._update_cache_size()
        except Exception as e:
            self.logger.error(f"清空磁盘缓存失败: {str(e)}")

    def cleanup_expired(self) -> Dict[str, int]:
        """
        清理过期缓存

        Returns:
            清理统计信息
        """
        memory_cleaned = self.memory_cache.cleanup_expired()
        disk_cleaned = self.disk_cache.cleanup_expired()

        return {
            'memory_cleaned': memory_cleaned,
            'disk_cleaned': disk_cleaned,
            'total_cleaned': memory_cleaned + disk_cleaned
        }

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'memory_cache': self.memory_cache.get_stats(),
            'disk_cache': self.disk_cache.get_stats()
        }

    def _start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(300)  # 每5分钟清理一次
                    self.cleanup_expired()
                except Exception as e:
                    self.logger.error(f"清理线程异常: {str(e)}")

        cleanup_thread = threading.Thread(
            target=cleanup_worker,
            name="CacheCleanup",
            daemon=True
        )
        cleanup_thread.start()
        self.logger.info("缓存清理线程已启动")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数优化使用示例

演示如何使用参数优化系统进行策略参数调优。
"""

import numpy as np
import pandas as pd
from typing import Dict, Any
import time

from quantization.optimization.parameter_optimizer import (
    ParameterOptimizationManager,
    ParameterSpace,
    ParameterType,
    OptimizationMethod
)
from quantization.optimization.strategy_ensemble import (
    StrategyEnsembleManager,
    StrategyPerformance,
    EnsembleConfig,
    EnsembleMethod
)
from quantization.utils.logger import get_logger


class MockStrategy:
    """模拟策略类"""
    
    def __init__(self, strategy_id: str, params: Dict[str, Any]):
        self.strategy_id = strategy_id
        self.params = params
        self.logger = get_logger(f"MockStrategy_{strategy_id}")
    
    def backtest(self, data: pd.DataFrame) -> Dict[str, Any]:
        """模拟回测"""
        # 这里是一个简化的回测逻辑
        # 实际应用中应该替换为真实的策略回测代码
        
        np.random.seed(hash(str(self.params)) % 2**32)
        
        # 模拟收益率序列
        n_days = len(data)
        base_return = self.params.get('expected_return', 0.0001)
        volatility = self.params.get('volatility', 0.02)
        
        # 添加参数影响
        momentum_factor = self.params.get('momentum_window', 20) / 20.0
        mean_reversion_factor = self.params.get('mean_reversion_strength', 0.5)
        
        returns = []
        for i in range(n_days):
            # 基础收益
            daily_return = np.random.normal(base_return, volatility)
            
            # 动量效应
            if i > 0:
                daily_return += momentum_factor * returns[-1] * 0.1
            
            # 均值回归效应
            if len(returns) > 5:
                recent_avg = np.mean(returns[-5:])
                daily_return -= mean_reversion_factor * recent_avg * 0.2
            
            returns.append(daily_return)
        
        returns = np.array(returns)
        
        # 计算性能指标
        total_return = np.prod(1 + returns) - 1
        annualized_return = np.mean(returns) * 252
        volatility = np.std(returns) * np.sqrt(252)
        sharpe_ratio = annualized_return / max(volatility, 1e-8)
        
        # 计算最大回撤
        cumulative = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = np.min(drawdown)
        
        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'returns': returns.tolist()
        }


def create_sample_data(n_days: int = 252) -> pd.DataFrame:
    """创建样本数据"""
    dates = pd.date_range(start='2023-01-01', periods=n_days, freq='D')
    
    # 模拟股价数据
    np.random.seed(42)
    price = 100.0
    prices = []
    
    for _ in range(n_days):
        price *= (1 + np.random.normal(0.0005, 0.02))
        prices.append(price)
    
    return pd.DataFrame({
        'date': dates,
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, n_days)
    })


def objective_function(params: Dict[str, Any]) -> float:
    """目标函数：最大化夏普比率"""
    try:
        # 创建模拟策略
        strategy = MockStrategy("test", params)
        
        # 创建样本数据
        data = create_sample_data(252)
        
        # 执行回测
        result = strategy.backtest(data)
        
        # 返回夏普比率作为优化目标
        sharpe_ratio = result['sharpe_ratio']
        
        # 添加一些约束惩罚
        max_drawdown = result['max_drawdown']
        if max_drawdown < -0.2:  # 最大回撤超过20%的惩罚
            sharpe_ratio -= abs(max_drawdown) * 2
        
        return sharpe_ratio
        
    except Exception as e:
        print(f"目标函数评估失败: {e}")
        return -999.0


def demonstrate_parameter_optimization():
    """演示参数优化"""
    logger = get_logger("ParameterOptimizationDemo")
    logger.info("开始参数优化演示")
    
    # 定义参数空间
    parameter_spaces = [
        ParameterSpace(
            name="momentum_window",
            param_type=ParameterType.DISCRETE,
            choices=[5, 10, 15, 20, 25, 30],
            default=20,
            description="动量窗口期"
        ),
        ParameterSpace(
            name="mean_reversion_strength",
            param_type=ParameterType.CONTINUOUS,
            bounds=(0.0, 1.0),
            default=0.5,
            description="均值回归强度"
        ),
        ParameterSpace(
            name="expected_return",
            param_type=ParameterType.CONTINUOUS,
            bounds=(-0.001, 0.002),
            default=0.0001,
            description="期望日收益率"
        ),
        ParameterSpace(
            name="volatility",
            param_type=ParameterType.CONTINUOUS,
            bounds=(0.01, 0.05),
            default=0.02,
            description="波动率"
        )
    ]
    
    # 创建优化管理器
    optimizer = ParameterOptimizationManager()
    
    # 测试不同的优化方法
    methods = [
        OptimizationMethod.RANDOM_SEARCH,
        OptimizationMethod.GRID_SEARCH,
        OptimizationMethod.GENETIC
    ]
    
    results = {}
    
    for method in methods:
        logger.info(f"测试优化方法: {method.value}")
        start_time = time.time()
        
        try:
            result = optimizer.optimize_parameters(
                parameter_spaces=parameter_spaces,
                objective_function=objective_function,
                method=method,
                max_evaluations=50,  # 为了演示，使用较小的评估次数
                enable_sensitivity_analysis=True
            )
            
            results[method.value] = result
            
            logger.info(f"方法 {method.value} 完成:")
            logger.info(f"  最佳得分: {result.best_score:.4f}")
            logger.info(f"  最佳参数: {result.best_params}")
            logger.info(f"  评估次数: {result.total_evaluations}")
            logger.info(f"  优化时间: {result.optimization_time:.2f}秒")
            
            if result.sensitivity_analysis:
                logger.info("  参数敏感性:")
                for param, sensitivity in result.sensitivity_analysis.items():
                    logger.info(f"    {param}: {sensitivity:.4f}")
            
        except Exception as e:
            logger.error(f"优化方法 {method.value} 失败: {e}")
    
    # 自适应优化演示
    logger.info("测试自适应优化")
    try:
        adaptive_result = optimizer.adaptive_optimization(
            parameter_spaces=parameter_spaces,
            objective_function=objective_function,
            initial_budget=30,
            max_total_budget=100
        )
        
        logger.info("自适应优化完成:")
        logger.info(f"  最佳得分: {adaptive_result.best_score:.4f}")
        logger.info(f"  最佳参数: {adaptive_result.best_params}")
        logger.info(f"  总评估次数: {adaptive_result.total_evaluations}")
        logger.info(f"  优化阶段: {adaptive_result.convergence_info.get('stages', [])}")
        
    except Exception as e:
        logger.error(f"自适应优化失败: {e}")
    
    return results


def demonstrate_strategy_ensemble():
    """演示策略组合"""
    logger = get_logger("StrategyEnsembleDemo")
    logger.info("开始策略组合演示")
    
    # 创建组合配置
    config = EnsembleConfig(
        method=EnsembleMethod.ADAPTIVE,
        min_weight=0.05,
        max_weight=0.5,
        target_volatility=0.15
    )
    
    # 创建组合管理器
    ensemble = StrategyEnsembleManager(config)
    
    # 创建多个模拟策略
    strategies = {
        'momentum': MockStrategy('momentum', {
            'momentum_window': 20,
            'mean_reversion_strength': 0.2,
            'expected_return': 0.0008,
            'volatility': 0.025
        }),
        'mean_reversion': MockStrategy('mean_reversion', {
            'momentum_window': 5,
            'mean_reversion_strength': 0.8,
            'expected_return': 0.0005,
            'volatility': 0.018
        }),
        'trend_following': MockStrategy('trend_following', {
            'momentum_window': 30,
            'mean_reversion_strength': 0.1,
            'expected_return': 0.0006,
            'volatility': 0.022
        })
    }
    
    # 添加策略到组合
    for strategy_id, strategy in strategies.items():
        ensemble.add_strategy(strategy_id, strategy)
    
    # 模拟策略表现数据
    data = create_sample_data(252)
    
    for strategy_id, strategy in strategies.items():
        # 执行回测
        backtest_result = strategy.backtest(data)
        
        # 创建表现对象
        performance = StrategyPerformance(
            strategy_id=strategy_id,
            returns=backtest_result['returns'],
            volatility=backtest_result['volatility'],
            sharpe_ratio=backtest_result['sharpe_ratio'],
            max_drawdown=backtest_result['max_drawdown'],
            win_rate=np.mean(np.array(backtest_result['returns']) > 0)
        )
        
        # 更新表现数据
        ensemble.update_strategy_performance(strategy_id, performance)
    
    # 测试不同的组合方法
    methods = [
        EnsembleMethod.EQUAL_WEIGHT,
        EnsembleMethod.RISK_PARITY,
        EnsembleMethod.SHARPE_WEIGHTED,
        EnsembleMethod.ADAPTIVE
    ]
    
    for method in methods:
        logger.info(f"测试组合方法: {method.value}")
        weights = ensemble.calculate_ensemble_weights(method)
        logger.info(f"  权重分配: {weights}")
    
    # 执行再平衡
    current_time = pd.Timestamp.now()
    new_weights = ensemble.rebalance(current_time)
    logger.info(f"再平衡后权重: {new_weights}")
    
    # 获取组合表现
    ensemble_performance = ensemble.get_ensemble_performance()
    logger.info("组合整体表现:")
    for metric, value in ensemble_performance.items():
        if isinstance(value, (int, float)):
            logger.info(f"  {metric}: {value:.4f}")
        else:
            logger.info(f"  {metric}: {value}")
    
    return ensemble


def main():
    """主函数"""
    print("=" * 60)
    print("参数优化和策略组合演示")
    print("=" * 60)
    
    # 参数优化演示
    print("\n1. 参数优化演示")
    print("-" * 30)
    optimization_results = demonstrate_parameter_optimization()
    
    # 策略组合演示
    print("\n2. 策略组合演示")
    print("-" * 30)
    ensemble_manager = demonstrate_strategy_ensemble()
    
    print("\n演示完成！")


if __name__ == "__main__":
    main()

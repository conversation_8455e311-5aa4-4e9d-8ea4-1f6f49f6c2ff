"""
A股数据采集框架配置设置
"""

import os
from pathlib import Path
from typing import Dict, List, Optional

class Config:
    """数据采集框架的主配置类"""

    # 基础目录
    BASE_DIR = Path(__file__).parent.parent.parent
    DATA_DIR = BASE_DIR / "data"
    CACHE_DIR = DATA_DIR / "cache"
    LOG_DIR = BASE_DIR / "logs"
    
    # 数据库设置
    DATABASE_URL = os.getenv('DATABASE_URL', f'sqlite:///{DATA_DIR}/ashare_data.db')
    DATABASE_POOL_SIZE = int(os.getenv('DATABASE_POOL_SIZE', '10'))
    DATABASE_TIMEOUT = int(os.getenv('DATABASE_TIMEOUT', '30'))

    # 缓存设置
    CACHE_ENABLED = os.getenv('CACHE_ENABLED', 'true').lower() == 'true'
    CACHE_TTL_DAYS = int(os.getenv('CACHE_TTL_DAYS', '7'))  # 缓存有效期（天）
    CACHE_MAX_SIZE_MB = int(os.getenv('CACHE_MAX_SIZE_MB', '1000'))  # 最大缓存大小

    # API限流设置
    AKSHARE_RATE_LIMIT = float(os.getenv('AKSHARE_RATE_LIMIT', '0.5'))  # 请求间隔（秒）
    WEB_SCRAPER_RATE_LIMIT = float(os.getenv('WEB_SCRAPER_RATE_LIMIT', '1.0'))
    MAX_RETRIES = int(os.getenv('MAX_RETRIES', '3'))
    RETRY_DELAY = float(os.getenv('RETRY_DELAY', '2.0'))
    
    # 数据验证设置
    VALIDATE_DATA = os.getenv('VALIDATE_DATA', 'true').lower() == 'true'
    MIN_DATA_POINTS = int(os.getenv('MIN_DATA_POINTS', '10'))  # 验证的最小数据点数
    MAX_PRICE_CHANGE_PERCENT = float(os.getenv('MAX_PRICE_CHANGE_PERCENT', '50.0'))  # 最大日涨跌幅

    # 日志设置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE_MAX_SIZE = int(os.getenv('LOG_FILE_MAX_SIZE', '10485760'))  # 10MB
    LOG_BACKUP_COUNT = int(os.getenv('LOG_BACKUP_COUNT', '5'))

    # 股票市场设置
    TRADING_DAYS_PER_YEAR = 250
    MARKET_OPEN_TIME = "09:30"
    MARKET_CLOSE_TIME = "15:00"

    # 支持的交易所
    SUPPORTED_EXCHANGES = ['SZ', 'SH']  # 深圳、上海
    
    # 要收集的数据类型
    DEFAULT_DATA_TYPES = [
        'daily_price',      # OHLCV数据
        'volume',           # 成交量
        'market_cap',       # 市值
        'financial_data',   # 财务报表
        'index_data'        # 市场指数
    ]

    # 网络爬虫设置
    WEB_SCRAPER_TIMEOUT = int(os.getenv('WEB_SCRAPER_TIMEOUT', '30'))
    WEB_SCRAPER_USER_AGENT = os.getenv(
        'WEB_SCRAPER_USER_AGENT',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    )

    # 备用数据源
    FALLBACK_SOURCES = [
        'tencent',
        'eastmoney'
    ]

    # 数据更新设置
    AUTO_UPDATE_ENABLED = os.getenv('AUTO_UPDATE_ENABLED', 'true').lower() == 'true'
    UPDATE_FREQUENCY_HOURS = int(os.getenv('UPDATE_FREQUENCY_HOURS', '24'))
    
    @classmethod
    def create_directories(cls):
        """创建必要的目录（如果不存在）"""
        for directory in [cls.DATA_DIR, cls.CACHE_DIR, cls.LOG_DIR]:
            directory.mkdir(parents=True, exist_ok=True)

    @classmethod
    def get_database_config(cls) -> Dict:
        """获取数据库配置字典"""
        return {
            'url': cls.DATABASE_URL,
            'pool_size': cls.DATABASE_POOL_SIZE,
            'timeout': cls.DATABASE_TIMEOUT
        }

    @classmethod
    def get_cache_config(cls) -> Dict:
        """获取缓存配置字典"""
        return {
            'enabled': cls.CACHE_ENABLED,
            'ttl_days': cls.CACHE_TTL_DAYS,
            'max_size_mb': cls.CACHE_MAX_SIZE_MB,
            'cache_dir': cls.CACHE_DIR
        }
    
    @classmethod
    def get_rate_limit_config(cls) -> Dict:
        """获取限流配置"""
        return {
            'akshare_limit': cls.AKSHARE_RATE_LIMIT,
            'web_scraper_limit': cls.WEB_SCRAPER_RATE_LIMIT,
            'max_retries': cls.MAX_RETRIES,
            'retry_delay': cls.RETRY_DELAY
        }

    @classmethod
    def get_validation_config(cls) -> Dict:
        """获取数据验证配置"""
        return {
            'enabled': cls.VALIDATE_DATA,
            'min_data_points': cls.MIN_DATA_POINTS,
            'max_price_change_percent': cls.MAX_PRICE_CHANGE_PERCENT
        }

# 开发/测试配置
class DevConfig(Config):
    """开发配置，更详细的日志和更小的限制"""
    LOG_LEVEL = 'DEBUG'
    CACHE_TTL_DAYS = 1
    MAX_RETRIES = 2
    AKSHARE_RATE_LIMIT = 0.1  # 测试时更快

# 生产配置
class ProdConfig(Config):
    """生产配置，优化的设置"""
    LOG_LEVEL = 'WARNING'
    CACHE_TTL_DAYS = 30
    MAX_RETRIES = 5
    AKSHARE_RATE_LIMIT = 1.0  # 更保守

# 配置工厂
def get_config(env: Optional[str] = None) -> Config:
    """
    根据环境获取配置

    参数:
        env: 环境名称 ('dev', 'prod', 或 None 表示默认)

    返回:
        Config: 配置实例
    """
    env = env or os.getenv('ENVIRONMENT', 'default')

    if env.lower() == 'dev':
        return DevConfig()
    elif env.lower() == 'prod':
        return ProdConfig()
    else:
        return Config()

"""
Logging configuration for the A-Share Data Acquisition Framework
"""

import logging
import logging.handlers
from pathlib import Path
from typing import Optional
from ..config.settings import Config

class FrameworkLogger:
    """Centralized logging configuration for the framework"""
    
    _loggers = {}
    _configured = False
    
    @classmethod
    def setup_logging(cls, config: Optional[Config] = None):
        """
        Setup logging configuration for the framework
        
        Args:
            config: Configuration instance, uses default if None
        """
        if cls._configured:
            return
            
        config = config or Config()
        config.create_directories()
        
        # Configure root logger
        root_logger = logging.getLogger('data_acquisition')
        root_logger.setLevel(getattr(logging, config.LOG_LEVEL))
        
        # Remove existing handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, config.LOG_LEVEL))
        console_formatter = logging.Formatter(config.LOG_FORMAT)
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # File handler with rotation
        log_file = config.LOG_DIR / 'data_acquisition.log'
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=config.LOG_FILE_MAX_SIZE,
            backupCount=config.LOG_BACKUP_COUNT
        )
        file_handler.setLevel(getattr(logging, config.LOG_LEVEL))
        file_formatter = logging.Formatter(config.LOG_FORMAT)
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
        
        # Error file handler
        error_log_file = config.LOG_DIR / 'errors.log'
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=config.LOG_FILE_MAX_SIZE,
            backupCount=config.LOG_BACKUP_COUNT
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        root_logger.addHandler(error_handler)
        
        cls._configured = True
    
    @classmethod
    def get_logger(cls, name: str, config: Optional[Config] = None) -> logging.Logger:
        """
        Get a logger instance for a specific module
        
        Args:
            name: Logger name (usually module name)
            config: Configuration instance
            
        Returns:
            logging.Logger: Configured logger instance
        """
        if not cls._configured:
            cls.setup_logging(config)
        
        if name not in cls._loggers:
            logger = logging.getLogger(f'data_acquisition.{name}')
            cls._loggers[name] = logger
        
        return cls._loggers[name]

# Convenience function for getting loggers
def get_logger(name: str, config: Optional[Config] = None) -> logging.Logger:
    """
    Get a logger instance
    
    Args:
        name: Logger name
        config: Configuration instance
        
    Returns:
        logging.Logger: Configured logger
    """
    return FrameworkLogger.get_logger(name, config)

# Module-specific loggers
def get_data_manager_logger(config: Optional[Config] = None) -> logging.Logger:
    """Get logger for data manager"""
    return get_logger('data_manager', config)

def get_akshare_logger(config: Optional[Config] = None) -> logging.Logger:
    """Get logger for akshare provider"""
    return get_logger('akshare_provider', config)

def get_web_scraper_logger(config: Optional[Config] = None) -> logging.Logger:
    """Get logger for web scraper"""
    return get_logger('web_scraper', config)

def get_database_logger(config: Optional[Config] = None) -> logging.Logger:
    """Get logger for database operations"""
    return get_logger('database', config)

def get_cache_logger(config: Optional[Config] = None) -> logging.Logger:
    """Get logger for cache operations"""
    return get_logger('cache', config)

def get_validator_logger(config: Optional[Config] = None) -> logging.Logger:
    """Get logger for data validation"""
    return get_logger('validator', config)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接池管理

实现高效的数据库连接池，支持连接复用、超时处理、健康检查和自动重连。
"""

import sqlite3
import threading
import time
import queue
from contextlib import contextmanager
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Generator
from pathlib import Path

from quantization.utils.logger import get_logger


class ConnectionPool:
    """
    数据库连接池
    
    提供高效的数据库连接管理，包括：
    - 连接复用和池化管理
    - 连接健康检查和自动重连
    - 超时处理和资源清理
    - 线程安全的连接分配
    """
    
    def __init__(
        self,
        database_path: str,
        min_connections: int = 2,
        max_connections: int = 10,
        connection_timeout: float = 30.0,
        idle_timeout: float = 300.0,
        health_check_interval: float = 60.0
    ):
        """
        初始化连接池
        
        Args:
            database_path: 数据库文件路径
            min_connections: 最小连接数
            max_connections: 最大连接数
            connection_timeout: 连接超时时间（秒）
            idle_timeout: 空闲连接超时时间（秒）
            health_check_interval: 健康检查间隔（秒）
        """
        self.database_path = database_path
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.connection_timeout = connection_timeout
        self.idle_timeout = idle_timeout
        self.health_check_interval = health_check_interval
        
        # 连接池和管理
        self._pool = queue.Queue(maxsize=max_connections)
        self._active_connections = {}  # 活跃连接跟踪
        self._connection_stats = {}    # 连接统计信息
        self._pool_lock = threading.RLock()
        self._connection_counter = 0
        
        # 状态管理
        self._is_closed = False
        self._last_health_check = datetime.now()
        
        # 日志
        self.logger = get_logger(self.__class__.__name__)
        
        # 初始化连接池
        self._initialize_pool()
        
        # 启动健康检查线程
        self._start_health_check_thread()
    
    def _initialize_pool(self):
        """初始化连接池"""
        try:
            # 确保数据库目录存在
            Path(self.database_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 创建最小数量的连接
            for _ in range(self.min_connections):
                conn = self._create_connection()
                if conn:
                    self._pool.put(conn)
            
            self.logger.info(f"连接池初始化完成，初始连接数: {self.min_connections}")
            
        except Exception as e:
            self.logger.error(f"连接池初始化失败: {str(e)}")
            raise
    
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """
        创建新的数据库连接
        
        Returns:
            数据库连接对象
        """
        try:
            conn = sqlite3.connect(
                self.database_path,
                timeout=self.connection_timeout,
                check_same_thread=False
            )
            
            # 配置连接
            conn.row_factory = sqlite3.Row  # 支持字典式访问
            conn.execute("PRAGMA journal_mode=WAL")  # 启用WAL模式
            conn.execute("PRAGMA synchronous=NORMAL")  # 平衡性能和安全
            conn.execute("PRAGMA cache_size=10000")  # 增加缓存大小
            conn.execute("PRAGMA temp_store=MEMORY")  # 临时表存储在内存
            
            # 记录连接信息
            with self._pool_lock:
                self._connection_counter += 1
                conn_id = self._connection_counter
                
                self._connection_stats[conn_id] = {
                    'created_at': datetime.now(),
                    'last_used': datetime.now(),
                    'use_count': 0,
                    'is_healthy': True
                }
                
                # 为连接添加ID标识
                conn._pool_id = conn_id
            
            self.logger.debug(f"创建新连接: {conn_id}")
            return conn
            
        except Exception as e:
            self.logger.error(f"创建数据库连接失败: {str(e)}")
            return None
    
    def _validate_connection(self, conn: sqlite3.Connection) -> bool:
        """
        验证连接是否健康
        
        Args:
            conn: 数据库连接
            
        Returns:
            连接是否健康
        """
        try:
            # 执行简单查询测试连接
            cursor = conn.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            
            # 更新连接状态
            if hasattr(conn, '_pool_id'):
                conn_id = conn._pool_id
                if conn_id in self._connection_stats:
                    self._connection_stats[conn_id]['is_healthy'] = True
            
            return True
            
        except Exception as e:
            self.logger.warning(f"连接健康检查失败: {str(e)}")
            
            # 标记连接为不健康
            if hasattr(conn, '_pool_id'):
                conn_id = conn._pool_id
                if conn_id in self._connection_stats:
                    self._connection_stats[conn_id]['is_healthy'] = False
            
            return False
    
    @contextmanager
    def get_connection(self) -> Generator[sqlite3.Connection, None, None]:
        """
        获取数据库连接（上下文管理器）
        
        Yields:
            数据库连接对象
        """
        conn = None
        try:
            conn = self._acquire_connection()
            if conn is None:
                raise RuntimeError("无法获取数据库连接")
            
            yield conn
            
        except Exception as e:
            self.logger.error(f"数据库操作失败: {str(e)}")
            # 如果连接出现问题，标记为不健康
            if conn and hasattr(conn, '_pool_id'):
                conn_id = conn._pool_id
                if conn_id in self._connection_stats:
                    self._connection_stats[conn_id]['is_healthy'] = False
            raise
            
        finally:
            if conn:
                self._release_connection(conn)
    
    def _acquire_connection(self) -> Optional[sqlite3.Connection]:
        """
        获取连接
        
        Returns:
            数据库连接对象
        """
        if self._is_closed:
            raise RuntimeError("连接池已关闭")
        
        start_time = time.time()
        
        while time.time() - start_time < self.connection_timeout:
            try:
                # 尝试从池中获取连接
                conn = self._pool.get(timeout=1.0)
                
                # 验证连接健康状态
                if self._validate_connection(conn):
                    # 更新使用统计
                    if hasattr(conn, '_pool_id'):
                        conn_id = conn._pool_id
                        if conn_id in self._connection_stats:
                            stats = self._connection_stats[conn_id]
                            stats['last_used'] = datetime.now()
                            stats['use_count'] += 1
                    
                    # 记录活跃连接
                    with self._pool_lock:
                        self._active_connections[id(conn)] = conn
                    
                    self.logger.debug(f"获取连接: {getattr(conn, '_pool_id', 'unknown')}")
                    return conn
                else:
                    # 连接不健康，关闭并创建新连接
                    self._close_connection(conn)
                    conn = self._create_connection()
                    if conn:
                        with self._pool_lock:
                            self._active_connections[id(conn)] = conn
                        return conn
                    
            except queue.Empty:
                # 池中没有可用连接，尝试创建新连接
                if self._get_total_connections() < self.max_connections:
                    conn = self._create_connection()
                    if conn:
                        with self._pool_lock:
                            self._active_connections[id(conn)] = conn
                        return conn
                
                # 等待一段时间后重试
                time.sleep(0.1)
        
        self.logger.error("获取数据库连接超时")
        return None
    
    def _release_connection(self, conn: sqlite3.Connection):
        """
        释放连接回池中
        
        Args:
            conn: 数据库连接
        """
        if self._is_closed:
            self._close_connection(conn)
            return
        
        try:
            # 从活跃连接中移除
            with self._pool_lock:
                self._active_connections.pop(id(conn), None)
            
            # 检查连接是否仍然健康
            if self._validate_connection(conn):
                # 检查是否超过空闲超时
                if hasattr(conn, '_pool_id'):
                    conn_id = conn._pool_id
                    if conn_id in self._connection_stats:
                        stats = self._connection_stats[conn_id]
                        idle_time = datetime.now() - stats['last_used']
                        
                        if idle_time.total_seconds() > self.idle_timeout:
                            # 连接空闲时间过长，关闭连接
                            self._close_connection(conn)
                            return
                
                # 将连接放回池中
                try:
                    self._pool.put_nowait(conn)
                    self.logger.debug(f"释放连接: {getattr(conn, '_pool_id', 'unknown')}")
                except queue.Full:
                    # 池已满，关闭连接
                    self._close_connection(conn)
            else:
                # 连接不健康，关闭连接
                self._close_connection(conn)
                
        except Exception as e:
            self.logger.error(f"释放连接失败: {str(e)}")
            self._close_connection(conn)
    
    def _close_connection(self, conn: sqlite3.Connection):
        """
        关闭连接
        
        Args:
            conn: 数据库连接
        """
        try:
            if hasattr(conn, '_pool_id'):
                conn_id = conn._pool_id
                self.logger.debug(f"关闭连接: {conn_id}")
                
                # 清理连接统计
                with self._pool_lock:
                    self._connection_stats.pop(conn_id, None)
                    self._active_connections.pop(id(conn), None)
            
            conn.close()
            
        except Exception as e:
            self.logger.warning(f"关闭连接时出错: {str(e)}")
    
    def _get_total_connections(self) -> int:
        """获取总连接数"""
        with self._pool_lock:
            return self._pool.qsize() + len(self._active_connections)

    def _start_health_check_thread(self):
        """启动健康检查线程"""
        def health_check_worker():
            while not self._is_closed:
                try:
                    time.sleep(self.health_check_interval)
                    self._perform_health_check()
                except Exception as e:
                    self.logger.error(f"健康检查线程异常: {str(e)}")

        health_thread = threading.Thread(
            target=health_check_worker,
            name="ConnectionPool-HealthCheck",
            daemon=True
        )
        health_thread.start()
        self.logger.info("健康检查线程已启动")

    def _perform_health_check(self):
        """执行健康检查"""
        current_time = datetime.now()

        # 检查是否需要执行健康检查
        if (current_time - self._last_health_check).total_seconds() < self.health_check_interval:
            return

        self._last_health_check = current_time

        with self._pool_lock:
            # 统计信息
            total_connections = self._get_total_connections()
            healthy_connections = sum(
                1 for stats in self._connection_stats.values()
                if stats['is_healthy']
            )

            self.logger.debug(
                f"健康检查 - 总连接数: {total_connections}, "
                f"健康连接数: {healthy_connections}, "
                f"活跃连接数: {len(self._active_connections)}"
            )

            # 清理过期的不健康连接
            expired_connections = []
            for conn_id, stats in self._connection_stats.items():
                if not stats['is_healthy']:
                    idle_time = current_time - stats['last_used']
                    if idle_time.total_seconds() > self.idle_timeout:
                        expired_connections.append(conn_id)

            # 移除过期连接的统计信息
            for conn_id in expired_connections:
                self._connection_stats.pop(conn_id, None)

            # 确保最小连接数
            if self._pool.qsize() < self.min_connections:
                needed_connections = self.min_connections - self._pool.qsize()
                for _ in range(needed_connections):
                    if self._get_total_connections() < self.max_connections:
                        conn = self._create_connection()
                        if conn:
                            try:
                                self._pool.put_nowait(conn)
                            except queue.Full:
                                self._close_connection(conn)
                                break

    def get_pool_stats(self) -> Dict[str, Any]:
        """
        获取连接池统计信息

        Returns:
            连接池统计信息字典
        """
        with self._pool_lock:
            total_connections = self._get_total_connections()
            available_connections = self._pool.qsize()
            active_connections = len(self._active_connections)

            # 计算连接使用统计
            total_uses = sum(stats['use_count'] for stats in self._connection_stats.values())
            healthy_connections = sum(
                1 for stats in self._connection_stats.values()
                if stats['is_healthy']
            )

            return {
                'total_connections': total_connections,
                'available_connections': available_connections,
                'active_connections': active_connections,
                'healthy_connections': healthy_connections,
                'total_uses': total_uses,
                'min_connections': self.min_connections,
                'max_connections': self.max_connections,
                'is_closed': self._is_closed,
                'last_health_check': self._last_health_check.isoformat()
            }

    def close(self):
        """关闭连接池"""
        if self._is_closed:
            return

        self.logger.info("正在关闭连接池...")
        self._is_closed = True

        # 关闭所有池中的连接
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                self._close_connection(conn)
            except queue.Empty:
                break

        # 关闭所有活跃连接
        with self._pool_lock:
            for conn in list(self._active_connections.values()):
                self._close_connection(conn)

            self._active_connections.clear()
            self._connection_stats.clear()

        self.logger.info("连接池已关闭")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()


class DatabaseManager:
    """
    数据库管理器

    提供高级数据库操作接口，基于连接池实现。
    """

    def __init__(self, database_path: str, **pool_kwargs):
        """
        初始化数据库管理器

        Args:
            database_path: 数据库文件路径
            **pool_kwargs: 连接池配置参数
        """
        self.database_path = database_path
        self.pool = ConnectionPool(database_path, **pool_kwargs)
        self.logger = get_logger(self.__class__.__name__)

        # 初始化数据库表结构
        self._initialize_database()

    def _initialize_database(self):
        """初始化数据库表结构"""
        try:
            with self.pool.get_connection() as conn:
                # 创建股票数据表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS stock_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        stock_code TEXT NOT NULL,
                        date DATE NOT NULL,
                        open REAL,
                        high REAL,
                        low REAL,
                        close REAL,
                        volume INTEGER,
                        amount REAL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(stock_code, date)
                    )
                """)

                # 创建股票基本信息表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS stock_info (
                        stock_code TEXT PRIMARY KEY,
                        name TEXT,
                        market_cap REAL,
                        circulation_market_cap REAL,
                        pe_ratio REAL,
                        pb_ratio REAL,
                        industry TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 创建技术指标表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS technical_indicators (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        stock_code TEXT NOT NULL,
                        date DATE NOT NULL,
                        williams_r REAL,
                        volume_ratio REAL,
                        big_order_net_ratio REAL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(stock_code, date)
                    )
                """)

                # 创建索引
                conn.execute("CREATE INDEX IF NOT EXISTS idx_stock_data_code_date ON stock_data(stock_code, date)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_technical_indicators_code_date ON technical_indicators(stock_code, date)")

                conn.commit()

            self.logger.info("数据库表结构初始化完成")

        except Exception as e:
            self.logger.error(f"数据库初始化失败: {str(e)}")
            raise

    def execute_query(self, query: str, params: tuple = ()) -> list:
        """
        执行查询语句

        Args:
            query: SQL查询语句
            params: 查询参数

        Returns:
            查询结果列表
        """
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.execute(query, params)
                results = cursor.fetchall()
                cursor.close()
                return [dict(row) for row in results]

        except Exception as e:
            self.logger.error(f"执行查询失败: {str(e)}")
            raise

    def execute_update(self, query: str, params: tuple = ()) -> int:
        """
        执行更新语句

        Args:
            query: SQL更新语句
            params: 更新参数

        Returns:
            影响的行数
        """
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.execute(query, params)
                affected_rows = cursor.rowcount
                conn.commit()
                cursor.close()
                return affected_rows

        except Exception as e:
            self.logger.error(f"执行更新失败: {str(e)}")
            raise

    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        return self.pool.get_pool_stats()

    def close(self):
        """关闭数据库管理器"""
        self.pool.close()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略参数优化系统

实现动态参数调优、多策略组合、参数敏感性分析和自适应策略调整。
支持遗传算法、贝叶斯优化、网格搜索等多种优化方法。
"""

import asyncio
import time
import warnings
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
import pandas as pd
from collections import defaultdict, deque
import threading
import json
from pathlib import Path
import itertools
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp

from quantization.utils.logger import get_logger

# 可选依赖
try:
    from scipy import optimize
    from scipy.stats import norm
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False

try:
    from sklearn.gaussian_process import GaussianProcessRegressor
    from sklearn.gaussian_process.kernels import Matern
    from sklearn.model_selection import ParameterGrid
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


class OptimizationMethod(Enum):
    """优化方法"""
    GRID_SEARCH = "grid_search"           # 网格搜索
    RANDOM_SEARCH = "random_search"       # 随机搜索
    BAYESIAN = "bayesian"                 # 贝叶斯优化
    GENETIC = "genetic"                   # 遗传算法
    PARTICLE_SWARM = "particle_swarm"     # 粒子群优化
    SIMULATED_ANNEALING = "simulated_annealing"  # 模拟退火


class ParameterType(Enum):
    """参数类型"""
    CONTINUOUS = "continuous"    # 连续参数
    DISCRETE = "discrete"        # 离散参数
    CATEGORICAL = "categorical"  # 分类参数
    BOOLEAN = "boolean"          # 布尔参数


@dataclass
class ParameterSpace:
    """参数空间定义"""
    name: str
    param_type: ParameterType
    bounds: Optional[Tuple[float, float]] = None  # 连续参数的范围
    choices: Optional[List[Any]] = None           # 离散/分类参数的选择
    default: Any = None
    description: str = ""
    
    def sample(self) -> Any:
        """随机采样参数值"""
        if self.param_type == ParameterType.CONTINUOUS and self.bounds:
            return np.random.uniform(self.bounds[0], self.bounds[1])
        elif self.param_type == ParameterType.DISCRETE and self.choices:
            return np.random.choice(self.choices)
        elif self.param_type == ParameterType.CATEGORICAL and self.choices:
            return np.random.choice(self.choices)
        elif self.param_type == ParameterType.BOOLEAN:
            return np.random.choice([True, False])
        else:
            return self.default
    
    def validate(self, value: Any) -> bool:
        """验证参数值是否有效"""
        try:
            if self.param_type == ParameterType.CONTINUOUS and self.bounds:
                return self.bounds[0] <= value <= self.bounds[1]
            elif self.param_type in [ParameterType.DISCRETE, ParameterType.CATEGORICAL] and self.choices:
                return value in self.choices
            elif self.param_type == ParameterType.BOOLEAN:
                return isinstance(value, bool)
            return True
        except Exception:
            return False


@dataclass
class OptimizationResult:
    """优化结果"""
    best_params: Dict[str, Any]
    best_score: float
    optimization_history: List[Tuple[Dict[str, Any], float]]
    total_evaluations: int
    optimization_time: float
    convergence_info: Dict[str, Any] = field(default_factory=dict)
    sensitivity_analysis: Optional[Dict[str, float]] = None


class GridSearchOptimizer:
    """
    网格搜索优化器
    
    对参数空间进行穷举搜索。
    """
    
    def __init__(self, max_workers: Optional[int] = None):
        self.logger = get_logger(self.__class__.__name__)
        self.max_workers = max_workers or min(8, mp.cpu_count())
    
    def optimize(self, 
                 parameter_spaces: List[ParameterSpace],
                 objective_function: Callable[[Dict[str, Any]], float],
                 max_evaluations: int = 1000) -> OptimizationResult:
        """
        执行网格搜索优化
        
        Args:
            parameter_spaces: 参数空间列表
            objective_function: 目标函数
            max_evaluations: 最大评估次数
            
        Returns:
            优化结果
        """
        start_time = time.time()
        
        # 构建参数网格
        param_grid = self._build_parameter_grid(parameter_spaces, max_evaluations)
        
        best_params = None
        best_score = float('-inf')
        optimization_history = []
        
        # 并行评估参数组合
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            
            for params in param_grid:
                future = executor.submit(self._evaluate_params, params, objective_function)
                futures.append((params, future))
            
            # 收集结果
            for params, future in futures:
                try:
                    score = future.result(timeout=300)  # 5分钟超时
                    optimization_history.append((params.copy(), score))
                    
                    if score > best_score:
                        best_score = score
                        best_params = params.copy()
                        
                except Exception as e:
                    self.logger.warning(f"参数评估失败 {params}: {e}")
        
        optimization_time = time.time() - start_time
        
        return OptimizationResult(
            best_params=best_params or {},
            best_score=best_score,
            optimization_history=optimization_history,
            total_evaluations=len(optimization_history),
            optimization_time=optimization_time
        )
    
    def _build_parameter_grid(self, parameter_spaces: List[ParameterSpace], 
                            max_evaluations: int) -> List[Dict[str, Any]]:
        """构建参数网格"""
        param_dict = {}
        
        for space in parameter_spaces:
            if space.param_type == ParameterType.CONTINUOUS and space.bounds:
                # 对连续参数进行离散化
                n_points = min(10, int(max_evaluations ** (1/len(parameter_spaces))))
                param_dict[space.name] = np.linspace(space.bounds[0], space.bounds[1], n_points)
            elif space.choices:
                param_dict[space.name] = space.choices
            else:
                param_dict[space.name] = [space.default] if space.default is not None else [0]
        
        # 生成参数组合
        if SKLEARN_AVAILABLE:
            param_grid = list(ParameterGrid(param_dict))
        else:
            # 手动生成参数组合
            keys = list(param_dict.keys())
            values = list(param_dict.values())
            param_grid = []
            
            for combination in itertools.product(*values):
                param_grid.append(dict(zip(keys, combination)))
        
        # 限制评估次数
        if len(param_grid) > max_evaluations:
            import random
            random.shuffle(param_grid)
            param_grid = param_grid[:max_evaluations]
        
        return param_grid
    
    def _evaluate_params(self, params: Dict[str, Any], 
                        objective_function: Callable[[Dict[str, Any]], float]) -> float:
        """评估参数组合"""
        try:
            return objective_function(params)
        except Exception as e:
            self.logger.error(f"目标函数评估失败: {e}")
            return float('-inf')


class RandomSearchOptimizer:
    """
    随机搜索优化器
    
    随机采样参数空间进行优化。
    """
    
    def __init__(self, max_workers: Optional[int] = None):
        self.logger = get_logger(self.__class__.__name__)
        self.max_workers = max_workers or min(8, mp.cpu_count())
    
    def optimize(self,
                 parameter_spaces: List[ParameterSpace],
                 objective_function: Callable[[Dict[str, Any]], float],
                 max_evaluations: int = 1000) -> OptimizationResult:
        """
        执行随机搜索优化
        
        Args:
            parameter_spaces: 参数空间列表
            objective_function: 目标函数
            max_evaluations: 最大评估次数
            
        Returns:
            优化结果
        """
        start_time = time.time()
        
        best_params = None
        best_score = float('-inf')
        optimization_history = []
        
        # 随机采样参数组合
        param_combinations = []
        for _ in range(max_evaluations):
            params = {}
            for space in parameter_spaces:
                params[space.name] = space.sample()
            param_combinations.append(params)
        
        # 并行评估参数组合
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            
            for params in param_combinations:
                future = executor.submit(self._evaluate_params, params, objective_function)
                futures.append((params, future))
            
            # 收集结果
            for params, future in futures:
                try:
                    score = future.result(timeout=300)
                    optimization_history.append((params.copy(), score))
                    
                    if score > best_score:
                        best_score = score
                        best_params = params.copy()
                        
                except Exception as e:
                    self.logger.warning(f"参数评估失败 {params}: {e}")
        
        optimization_time = time.time() - start_time
        
        return OptimizationResult(
            best_params=best_params or {},
            best_score=best_score,
            optimization_history=optimization_history,
            total_evaluations=len(optimization_history),
            optimization_time=optimization_time
        )
    
    def _evaluate_params(self, params: Dict[str, Any],
                        objective_function: Callable[[Dict[str, Any]], float]) -> float:
        """评估参数组合"""
        try:
            return objective_function(params)
        except Exception as e:
            self.logger.error(f"目标函数评估失败: {e}")
            return float('-inf')


class GeneticOptimizer:
    """
    遗传算法优化器

    使用遗传算法进行全局参数优化。
    """

    def __init__(self, population_size: int = 50, mutation_rate: float = 0.1,
                 crossover_rate: float = 0.8, elitism_rate: float = 0.1):
        self.logger = get_logger(self.__class__.__name__)
        self.population_size = population_size
        self.mutation_rate = mutation_rate
        self.crossover_rate = crossover_rate
        self.elitism_rate = elitism_rate

    def optimize(self,
                 parameter_spaces: List[ParameterSpace],
                 objective_function: Callable[[Dict[str, Any]], float],
                 max_evaluations: int = 1000) -> OptimizationResult:
        """
        执行遗传算法优化

        Args:
            parameter_spaces: 参数空间列表
            objective_function: 目标函数
            max_evaluations: 最大评估次数

        Returns:
            优化结果
        """
        start_time = time.time()

        # 初始化种群
        population = self._initialize_population(parameter_spaces)
        fitness_scores = [self._evaluate_params(individual, objective_function)
                         for individual in population]

        optimization_history = []
        for individual, score in zip(population, fitness_scores):
            optimization_history.append((individual.copy(), score))

        generations = max_evaluations // self.population_size

        for generation in range(generations):
            # 选择
            selected_population = self._selection(population, fitness_scores)

            # 交叉
            offspring = self._crossover(selected_population, parameter_spaces)

            # 变异
            offspring = self._mutation(offspring, parameter_spaces)

            # 评估新个体
            offspring_fitness = [self._evaluate_params(individual, objective_function)
                               for individual in offspring]

            # 记录历史
            for individual, score in zip(offspring, offspring_fitness):
                optimization_history.append((individual.copy(), score))

            # 精英保留
            population, fitness_scores = self._elitism(
                population + offspring, fitness_scores + offspring_fitness
            )

            best_score = max(fitness_scores)
            self.logger.info(f"遗传算法第 {generation} 代: 最佳得分 {best_score:.4f}")

        # 找到最佳结果
        best_idx = np.argmax(fitness_scores)
        best_params = population[best_idx]
        best_score = fitness_scores[best_idx]

        optimization_time = time.time() - start_time

        return OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            optimization_history=optimization_history,
            total_evaluations=len(optimization_history),
            optimization_time=optimization_time,
            convergence_info={'final_generation': generation, 'population_diversity': self._calculate_diversity(population)}
        )

    def _initialize_population(self, parameter_spaces: List[ParameterSpace]) -> List[Dict[str, Any]]:
        """初始化种群"""
        population = []
        for _ in range(self.population_size):
            individual = {}
            for space in parameter_spaces:
                individual[space.name] = space.sample()
            population.append(individual)
        return population

    def _selection(self, population: List[Dict[str, Any]],
                  fitness_scores: List[float]) -> List[Dict[str, Any]]:
        """锦标赛选择"""
        selected = []
        tournament_size = 3

        for _ in range(self.population_size):
            # 锦标赛选择
            tournament_indices = np.random.choice(len(population), tournament_size, replace=False)
            tournament_fitness = [fitness_scores[i] for i in tournament_indices]
            winner_idx = tournament_indices[np.argmax(tournament_fitness)]
            selected.append(population[winner_idx].copy())

        return selected

    def _crossover(self, population: List[Dict[str, Any]],
                  parameter_spaces: List[ParameterSpace]) -> List[Dict[str, Any]]:
        """交叉操作"""
        offspring = []

        for i in range(0, len(population), 2):
            parent1 = population[i]
            parent2 = population[i + 1] if i + 1 < len(population) else population[0]

            if np.random.random() < self.crossover_rate:
                child1, child2 = self._single_point_crossover(parent1, parent2, parameter_spaces)
                offspring.extend([child1, child2])
            else:
                offspring.extend([parent1.copy(), parent2.copy()])

        return offspring[:self.population_size]

    def _single_point_crossover(self, parent1: Dict[str, Any], parent2: Dict[str, Any],
                               parameter_spaces: List[ParameterSpace]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """单点交叉"""
        child1 = parent1.copy()
        child2 = parent2.copy()

        # 随机选择交叉点
        crossover_point = np.random.randint(1, len(parameter_spaces))

        for i, space in enumerate(parameter_spaces):
            if i >= crossover_point:
                child1[space.name] = parent2[space.name]
                child2[space.name] = parent1[space.name]

        return child1, child2

    def _mutation(self, population: List[Dict[str, Any]],
                 parameter_spaces: List[ParameterSpace]) -> List[Dict[str, Any]]:
        """变异操作"""
        mutated_population = []

        for individual in population:
            mutated_individual = individual.copy()

            for space in parameter_spaces:
                if np.random.random() < self.mutation_rate:
                    if space.param_type == ParameterType.CONTINUOUS and space.bounds:
                        # 高斯变异
                        current_value = mutated_individual[space.name]
                        mutation_strength = (space.bounds[1] - space.bounds[0]) * 0.1
                        new_value = current_value + np.random.normal(0, mutation_strength)
                        new_value = np.clip(new_value, space.bounds[0], space.bounds[1])
                        mutated_individual[space.name] = new_value
                    else:
                        # 重新采样
                        mutated_individual[space.name] = space.sample()

            mutated_population.append(mutated_individual)

        return mutated_population

    def _elitism(self, combined_population: List[Dict[str, Any]],
                combined_fitness: List[float]) -> Tuple[List[Dict[str, Any]], List[float]]:
        """精英保留"""
        # 按适应度排序
        sorted_indices = np.argsort(combined_fitness)[::-1]

        # 保留最优个体
        elite_count = int(self.population_size * self.elitism_rate)
        selected_indices = sorted_indices[:self.population_size]

        new_population = [combined_population[i] for i in selected_indices]
        new_fitness = [combined_fitness[i] for i in selected_indices]

        return new_population, new_fitness

    def _calculate_diversity(self, population: List[Dict[str, Any]]) -> float:
        """计算种群多样性"""
        if len(population) < 2:
            return 0.0

        # 简单的多样性度量：参数值的标准差平均值
        diversities = []

        for param_name in population[0].keys():
            values = [individual[param_name] for individual in population]
            if all(isinstance(v, (int, float)) for v in values):
                diversity = np.std(values)
                diversities.append(diversity)

        return float(np.mean(diversities)) if diversities else 0.0

    def _evaluate_params(self, params: Dict[str, Any],
                        objective_function: Callable[[Dict[str, Any]], float]) -> float:
        """评估参数组合"""
        try:
            return objective_function(params)
        except Exception as e:
            self.logger.error(f"目标函数评估失败: {e}")
            return float('-inf')


class SensitivityAnalyzer:
    """
    参数敏感性分析器

    分析参数对目标函数的敏感性。
    """

    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)

    def analyze_sensitivity(self,
                          parameter_spaces: List[ParameterSpace],
                          objective_function: Callable[[Dict[str, Any]], float],
                          base_params: Dict[str, Any],
                          perturbation_ratio: float = 0.1,
                          n_samples: int = 100) -> Dict[str, float]:
        """
        分析参数敏感性

        Args:
            parameter_spaces: 参数空间列表
            objective_function: 目标函数
            base_params: 基准参数
            perturbation_ratio: 扰动比例
            n_samples: 采样数量

        Returns:
            参数敏感性字典
        """
        sensitivity_scores = {}
        base_score = objective_function(base_params)

        for space in parameter_spaces:
            param_name = space.name

            if space.param_type == ParameterType.CONTINUOUS and space.bounds:
                sensitivity = self._analyze_continuous_sensitivity(
                    param_name, space, base_params, base_score,
                    objective_function, perturbation_ratio, n_samples
                )
            else:
                sensitivity = self._analyze_discrete_sensitivity(
                    param_name, space, base_params, base_score,
                    objective_function, n_samples
                )

            sensitivity_scores[param_name] = sensitivity
            self.logger.info(f"参数 {param_name} 的敏感性: {sensitivity:.4f}")

        return sensitivity_scores

    def _analyze_continuous_sensitivity(self,
                                      param_name: str,
                                      space: ParameterSpace,
                                      base_params: Dict[str, Any],
                                      base_score: float,
                                      objective_function: Callable[[Dict[str, Any]], float],
                                      perturbation_ratio: float,
                                      n_samples: int) -> float:
        """分析连续参数敏感性"""
        if not space.bounds:
            return 0.0

        base_value = base_params[param_name]
        param_range = space.bounds[1] - space.bounds[0]
        perturbation = param_range * perturbation_ratio

        score_changes = []

        # 正向扰动
        for _ in range(n_samples // 2):
            perturbed_params = base_params.copy()
            perturbation_value = np.random.uniform(0, perturbation)
            new_value = np.clip(base_value + perturbation_value, space.bounds[0], space.bounds[1])
            perturbed_params[param_name] = new_value

            try:
                perturbed_score = objective_function(perturbed_params)
                score_change = abs(perturbed_score - base_score)
                score_changes.append(score_change)
            except Exception:
                pass

        # 负向扰动
        for _ in range(n_samples // 2):
            perturbed_params = base_params.copy()
            perturbation_value = np.random.uniform(0, perturbation)
            new_value = np.clip(base_value - perturbation_value, space.bounds[0], space.bounds[1])
            perturbed_params[param_name] = new_value

            try:
                perturbed_score = objective_function(perturbed_params)
                score_change = abs(perturbed_score - base_score)
                score_changes.append(score_change)
            except Exception:
                pass

        return float(np.mean(score_changes)) if score_changes else 0.0

    def _analyze_discrete_sensitivity(self,
                                    param_name: str,
                                    space: ParameterSpace,
                                    base_params: Dict[str, Any],
                                    base_score: float,
                                    objective_function: Callable[[Dict[str, Any]], float],
                                    n_samples: int) -> float:
        """分析离散参数敏感性"""
        if not space.choices:
            return 0.0

        score_changes = []
        base_value = base_params[param_name]

        # 尝试其他选择
        other_choices = [choice for choice in space.choices if choice != base_value]

        for _ in range(min(n_samples, len(other_choices) * 10)):
            perturbed_params = base_params.copy()
            new_value = np.random.choice(other_choices)
            perturbed_params[param_name] = new_value

            try:
                perturbed_score = objective_function(perturbed_params)
                score_change = abs(perturbed_score - base_score)
                score_changes.append(score_change)
            except Exception:
                pass

        return float(np.mean(score_changes)) if score_changes else 0.0


class ParameterOptimizationManager:
    """
    参数优化管理器

    统一管理多种优化方法，提供参数优化的完整解决方案。
    """

    def __init__(self, cache_dir: str = "optimization_cache"):
        self.logger = get_logger(self.__class__.__name__)
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)

        # 初始化优化器
        self.optimizers = {
            OptimizationMethod.GRID_SEARCH: GridSearchOptimizer(),
            OptimizationMethod.RANDOM_SEARCH: RandomSearchOptimizer(),
            OptimizationMethod.GENETIC: GeneticOptimizer(),
        }

        # 如果有sklearn，添加贝叶斯优化
        if SKLEARN_AVAILABLE:
            self.optimizers[OptimizationMethod.BAYESIAN] = BayesianOptimizer()

        # 敏感性分析器
        self.sensitivity_analyzer = SensitivityAnalyzer()

        # 优化历史
        self.optimization_history: List[OptimizationResult] = []

    def optimize_parameters(self,
                          parameter_spaces: List[ParameterSpace],
                          objective_function: Callable[[Dict[str, Any]], float],
                          method: OptimizationMethod = OptimizationMethod.BAYESIAN,
                          max_evaluations: int = 100,
                          enable_sensitivity_analysis: bool = True,
                          cache_results: bool = True) -> OptimizationResult:
        """
        执行参数优化

        Args:
            parameter_spaces: 参数空间列表
            objective_function: 目标函数
            method: 优化方法
            max_evaluations: 最大评估次数
            enable_sensitivity_analysis: 是否启用敏感性分析
            cache_results: 是否缓存结果

        Returns:
            优化结果
        """
        self.logger.info(f"开始参数优化，方法: {method.value}, 最大评估次数: {max_evaluations}")

        # 检查缓存
        cache_key = self._generate_cache_key(parameter_spaces, method, max_evaluations)
        if cache_results:
            cached_result = self._load_cached_result(cache_key)
            if cached_result:
                self.logger.info("使用缓存的优化结果")
                return cached_result

        # 选择优化器
        if method not in self.optimizers:
            self.logger.warning(f"不支持的优化方法 {method.value}，使用随机搜索")
            method = OptimizationMethod.RANDOM_SEARCH

        optimizer = self.optimizers[method]

        # 执行优化
        try:
            result = optimizer.optimize(parameter_spaces, objective_function, max_evaluations)

            # 敏感性分析
            if enable_sensitivity_analysis and result.best_params:
                self.logger.info("执行参数敏感性分析")
                sensitivity_scores = self.sensitivity_analyzer.analyze_sensitivity(
                    parameter_spaces, objective_function, result.best_params
                )
                result.sensitivity_analysis = sensitivity_scores

            # 缓存结果
            if cache_results:
                self._cache_result(cache_key, result)

            # 记录历史
            self.optimization_history.append(result)

            self.logger.info(f"参数优化完成，最佳得分: {result.best_score:.4f}")
            return result

        except Exception as e:
            self.logger.error(f"参数优化失败: {e}")
            raise

    def multi_objective_optimization(self,
                                   parameter_spaces: List[ParameterSpace],
                                   objective_functions: List[Callable[[Dict[str, Any]], float]],
                                   weights: Optional[List[float]] = None,
                                   method: OptimizationMethod = OptimizationMethod.GENETIC,
                                   max_evaluations: int = 200) -> OptimizationResult:
        """
        多目标优化

        Args:
            parameter_spaces: 参数空间列表
            objective_functions: 目标函数列表
            weights: 目标权重
            method: 优化方法
            max_evaluations: 最大评估次数

        Returns:
            优化结果
        """
        if not objective_functions:
            raise ValueError("至少需要一个目标函数")

        if weights is None:
            weights = [1.0] * len(objective_functions)
        elif len(weights) != len(objective_functions):
            raise ValueError("权重数量必须与目标函数数量相同")

        # 标准化权重
        total_weight = sum(weights)
        normalized_weights = [w / total_weight for w in weights]

        # 组合目标函数
        def combined_objective(params: Dict[str, Any]) -> float:
            scores = []
            for obj_func in objective_functions:
                try:
                    score = obj_func(params)
                    scores.append(score)
                except Exception as e:
                    self.logger.warning(f"目标函数评估失败: {e}")
                    scores.append(float('-inf'))

            # 加权组合
            combined_score = sum(score * weight for score, weight in zip(scores, normalized_weights))
            return combined_score

        return self.optimize_parameters(
            parameter_spaces, combined_objective, method, max_evaluations
        )

    def adaptive_optimization(self,
                            parameter_spaces: List[ParameterSpace],
                            objective_function: Callable[[Dict[str, Any]], float],
                            initial_budget: int = 50,
                            max_total_budget: int = 500,
                            improvement_threshold: float = 0.01) -> OptimizationResult:
        """
        自适应优化

        根据优化进展自动调整策略和预算分配。

        Args:
            parameter_spaces: 参数空间列表
            objective_function: 目标函数
            initial_budget: 初始预算
            max_total_budget: 最大总预算
            improvement_threshold: 改进阈值

        Returns:
            优化结果
        """
        self.logger.info("开始自适应参数优化")

        total_evaluations = 0
        best_result = None
        optimization_stages = []

        # 阶段1: 随机搜索探索
        self.logger.info("阶段1: 随机搜索探索")
        stage1_budget = min(initial_budget, max_total_budget)
        result1 = self.optimize_parameters(
            parameter_spaces, objective_function,
            OptimizationMethod.RANDOM_SEARCH, stage1_budget, False, False
        )
        optimization_stages.append(("random_search", result1))
        total_evaluations += result1.total_evaluations
        best_result = result1

        if total_evaluations >= max_total_budget:
            return best_result

        # 阶段2: 贝叶斯优化精化（如果可用）
        if SKLEARN_AVAILABLE and total_evaluations < max_total_budget:
            self.logger.info("阶段2: 贝叶斯优化精化")
            stage2_budget = min(initial_budget, max_total_budget - total_evaluations)
            result2 = self.optimize_parameters(
                parameter_spaces, objective_function,
                OptimizationMethod.BAYESIAN, stage2_budget, False, False
            )
            optimization_stages.append(("bayesian", result2))
            total_evaluations += result2.total_evaluations

            if result2.best_score > best_result.best_score + improvement_threshold:
                best_result = result2

        # 阶段3: 遗传算法全局搜索（如果还有改进空间）
        if total_evaluations < max_total_budget:
            recent_improvement = (best_result.best_score -
                                optimization_stages[0][1].best_score) / max(1, abs(optimization_stages[0][1].best_score))

            if recent_improvement < improvement_threshold * 2:
                self.logger.info("阶段3: 遗传算法全局搜索")
                stage3_budget = min(initial_budget * 2, max_total_budget - total_evaluations)
                result3 = self.optimize_parameters(
                    parameter_spaces, objective_function,
                    OptimizationMethod.GENETIC, stage3_budget, False, False
                )
                optimization_stages.append(("genetic", result3))
                total_evaluations += result3.total_evaluations

                if result3.best_score > best_result.best_score:
                    best_result = result3

        # 最终敏感性分析
        if best_result and best_result.best_params:
            self.logger.info("执行最终敏感性分析")
            sensitivity_scores = self.sensitivity_analyzer.analyze_sensitivity(
                parameter_spaces, objective_function, best_result.best_params
            )
            best_result.sensitivity_analysis = sensitivity_scores

        # 合并优化历史
        combined_history = []
        for stage_name, stage_result in optimization_stages:
            combined_history.extend(stage_result.optimization_history)

        # 创建最终结果
        final_result = OptimizationResult(
            best_params=best_result.best_params,
            best_score=best_result.best_score,
            optimization_history=combined_history,
            total_evaluations=total_evaluations,
            optimization_time=sum(stage[1].optimization_time for stage in optimization_stages),
            convergence_info={
                'stages': [(name, result.best_score) for name, result in optimization_stages],
                'adaptive_strategy': 'multi_stage'
            },
            sensitivity_analysis=best_result.sensitivity_analysis
        )

        self.optimization_history.append(final_result)
        self.logger.info(f"自适应优化完成，最佳得分: {final_result.best_score:.4f}")

        return final_result

    def _generate_cache_key(self, parameter_spaces: List[ParameterSpace],
                          method: OptimizationMethod, max_evaluations: int) -> str:
        """生成缓存键"""
        import hashlib

        # 创建参数空间的哈希
        spaces_str = ""
        for space in parameter_spaces:
            spaces_str += f"{space.name}_{space.param_type.value}_{space.bounds}_{space.choices}"

        cache_content = f"{spaces_str}_{method.value}_{max_evaluations}"
        return hashlib.md5(cache_content.encode()).hexdigest()

    def _load_cached_result(self, cache_key: str) -> Optional[OptimizationResult]:
        """加载缓存结果"""
        cache_file = self.cache_dir / f"{cache_key}.json"

        if not cache_file.exists():
            return None

        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 重构OptimizationResult对象
            result = OptimizationResult(
                best_params=data['best_params'],
                best_score=data['best_score'],
                optimization_history=[(params, score) for params, score in data['optimization_history']],
                total_evaluations=data['total_evaluations'],
                optimization_time=data['optimization_time'],
                convergence_info=data.get('convergence_info', {}),
                sensitivity_analysis=data.get('sensitivity_analysis')
            )

            return result

        except Exception as e:
            self.logger.warning(f"加载缓存结果失败: {e}")
            return None

    def _cache_result(self, cache_key: str, result: OptimizationResult):
        """缓存优化结果"""
        cache_file = self.cache_dir / f"{cache_key}.json"

        try:
            data = {
                'best_params': result.best_params,
                'best_score': result.best_score,
                'optimization_history': result.optimization_history,
                'total_evaluations': result.total_evaluations,
                'optimization_time': result.optimization_time,
                'convergence_info': result.convergence_info,
                'sensitivity_analysis': result.sensitivity_analysis
            }

            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.warning(f"缓存优化结果失败: {e}")

    def get_optimization_summary(self) -> Dict[str, Any]:
        """获取优化历史摘要"""
        if not self.optimization_history:
            return {}

        best_result = max(self.optimization_history, key=lambda x: x.best_score)

        return {
            'total_optimizations': len(self.optimization_history),
            'best_score': best_result.best_score,
            'best_params': best_result.best_params,
            'average_score': np.mean([r.best_score for r in self.optimization_history]),
            'total_evaluations': sum(r.total_evaluations for r in self.optimization_history),
            'total_time': sum(r.optimization_time for r in self.optimization_history),
            'sensitivity_analysis': best_result.sensitivity_analysis
        }


class BayesianOptimizer:
    """
    贝叶斯优化器
    
    使用高斯过程进行智能参数优化。
    """
    
    def __init__(self, acquisition_function: str = 'ei', max_workers: Optional[int] = None):
        self.logger = get_logger(self.__class__.__name__)
        self.acquisition_function = acquisition_function  # 'ei', 'pi', 'ucb'
        self.max_workers = max_workers or min(4, mp.cpu_count())
        
        if not SKLEARN_AVAILABLE:
            raise ImportError("贝叶斯优化需要安装scikit-learn")
    
    def optimize(self,
                 parameter_spaces: List[ParameterSpace],
                 objective_function: Callable[[Dict[str, Any]], float],
                 max_evaluations: int = 100,
                 n_initial_points: int = 10) -> OptimizationResult:
        """
        执行贝叶斯优化
        
        Args:
            parameter_spaces: 参数空间列表
            objective_function: 目标函数
            max_evaluations: 最大评估次数
            n_initial_points: 初始随机点数量
            
        Returns:
            优化结果
        """
        start_time = time.time()
        
        # 只支持连续参数的贝叶斯优化
        continuous_spaces = [s for s in parameter_spaces 
                           if s.param_type == ParameterType.CONTINUOUS and s.bounds]
        
        if not continuous_spaces:
            self.logger.warning("贝叶斯优化需要至少一个连续参数，回退到随机搜索")
            random_optimizer = RandomSearchOptimizer(self.max_workers)
            return random_optimizer.optimize(parameter_spaces, objective_function, max_evaluations)
        
        # 初始化
        X_observed = []
        y_observed = []
        optimization_history = []
        
        # 随机初始化点
        for _ in range(n_initial_points):
            params = self._sample_continuous_params(continuous_spaces)
            score = self._evaluate_params(params, objective_function)
            
            X_observed.append(self._params_to_array(params, continuous_spaces))
            y_observed.append(score)
            optimization_history.append((params.copy(), score))
        
        # 贝叶斯优化循环
        for iteration in range(n_initial_points, max_evaluations):
            try:
                # 训练高斯过程
                gp = GaussianProcessRegressor(
                    kernel=Matern(length_scale=1.0, nu=2.5),
                    alpha=1e-6,
                    normalize_y=True,
                    n_restarts_optimizer=5,
                    random_state=42
                )
                
                X_array = np.array(X_observed)
                y_array = np.array(y_observed)
                gp.fit(X_array, y_array)
                
                # 寻找下一个评估点
                next_params = self._find_next_point(gp, continuous_spaces)
                next_score = self._evaluate_params(next_params, objective_function)
                
                # 更新观测数据
                X_observed.append(self._params_to_array(next_params, continuous_spaces))
                y_observed.append(next_score)
                optimization_history.append((next_params.copy(), next_score))
                
                self.logger.info(f"贝叶斯优化迭代 {iteration}: 得分 {next_score:.4f}")
                
            except Exception as e:
                self.logger.error(f"贝叶斯优化迭代 {iteration} 失败: {e}")
                break
        
        # 找到最佳结果
        best_idx = np.argmax(y_observed)
        best_score = y_observed[best_idx]
        best_params = optimization_history[best_idx][0]
        
        optimization_time = time.time() - start_time
        
        return OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            optimization_history=optimization_history,
            total_evaluations=len(optimization_history),
            optimization_time=optimization_time,
            convergence_info={'gp_log_likelihood': gp.log_marginal_likelihood_value_}
        )
    
    def _sample_continuous_params(self, continuous_spaces: List[ParameterSpace]) -> Dict[str, Any]:
        """采样连续参数"""
        params = {}
        for space in continuous_spaces:
            params[space.name] = space.sample()
        return params
    
    def _params_to_array(self, params: Dict[str, Any], 
                        continuous_spaces: List[ParameterSpace]) -> np.ndarray:
        """将参数字典转换为数组"""
        return np.array([params[space.name] for space in continuous_spaces])
    
    def _array_to_params(self, x: np.ndarray, 
                        continuous_spaces: List[ParameterSpace]) -> Dict[str, Any]:
        """将数组转换为参数字典"""
        params = {}
        for i, space in enumerate(continuous_spaces):
            # 确保参数在边界内
            if space.bounds:
                value = np.clip(x[i], space.bounds[0], space.bounds[1])
                params[space.name] = value
        return params
    
    def _find_next_point(self, gp: 'GaussianProcessRegressor', 
                        continuous_spaces: List[ParameterSpace]) -> Dict[str, Any]:
        """寻找下一个评估点"""
        # 定义采集函数
        def acquisition(x):
            x = x.reshape(1, -1)
            prediction = gp.predict(x, return_std=True)
            if isinstance(prediction, tuple) and len(prediction) == 2:
                mu, sigma = prediction
            else:
                mu = prediction
                sigma = np.ones_like(mu) * 0.1
            
            if self.acquisition_function == 'ei':
                # Expected Improvement
                best_y = np.max(gp.y_train_)
                z = (mu - best_y) / (sigma + 1e-9)
                return -(mu - best_y) * norm.cdf(z) - sigma * norm.pdf(z)
            elif self.acquisition_function == 'pi':
                # Probability of Improvement
                best_y = np.max(gp.y_train_)
                z = (mu - best_y) / (sigma + 1e-9)
                return -norm.cdf(z)
            else:  # UCB
                # Upper Confidence Bound
                kappa = 2.576  # 99% confidence
                return -(mu + kappa * sigma)
        
        # 优化采集函数
        bounds = [(space.bounds[0], space.bounds[1]) for space in continuous_spaces if space.bounds]
        
        best_x = None
        best_acq = float('inf')
        
        # 多次随机初始化优化
        for _ in range(10):
            x0 = np.array([space.sample() for space in continuous_spaces])
            
            try:
                result = optimize.minimize(
                    acquisition, x0, bounds=bounds, method='L-BFGS-B'
                )
                
                if result.success and result.fun < best_acq:
                    best_acq = result.fun
                    best_x = result.x
            except Exception as e:
                self.logger.warning(f"采集函数优化失败: {e}")
        
        if best_x is None:
            # 回退到随机采样
            best_x = np.array([space.sample() for space in continuous_spaces])
        
        return self._array_to_params(best_x, continuous_spaces)
    
    def _evaluate_params(self, params: Dict[str, Any],
                        objective_function: Callable[[Dict[str, Any]], float]) -> float:
        """评估参数组合"""
        try:
            return objective_function(params)
        except Exception as e:
            self.logger.error(f"目标函数评估失败: {e}")
            return float('-inf')

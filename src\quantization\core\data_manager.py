#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据管理器

统一管理各种数据源，提供一致的数据访问接口。
"""

import pandas as pd
from datetime import datetime, date
from typing import Dict, List, Optional, Union, Any

from quantization.core.interfaces.data_interface import DataInterface
from quantization.core.providers.akshare_provider import AkshareProvider
from quantization.core.providers.web_scraper_provider import WebScraperProvider
from quantization.utils.logger import get_logger
from quantization.config.settings import get_config


class DataManager:
    """
    数据管理器
    
    管理多个数据提供商，提供统一的数据访问接口。
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化数据管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = get_config(config_file)
        self.logger = get_logger("DataManager")
        
        # 数据提供商
        self.providers: Dict[str, DataInterface] = {}
        
        # 初始化数据提供商
        self._initialize_providers()
        
        # 当前活跃的提供商
        self.active_provider = None
        
        self.logger.info("数据管理器初始化完成")
    
    def _initialize_providers(self) -> None:
        """初始化数据提供商"""
        try:
            # 初始化akshare提供商
            akshare_config = self.config.get('data_sources.akshare', {})
            if akshare_config.get('enabled', True):
                self.providers['akshare'] = AkshareProvider(
                    rate_limit=akshare_config.get('rate_limit', 0.5),
                    timeout=akshare_config.get('timeout', 30),
                    max_retries=akshare_config.get('max_retries', 3)
                )
                self.logger.info("Akshare数据提供商初始化成功")
            
            # 初始化网络爬虫提供商
            scraper_config = self.config.get('data_sources.web_scraper', {})
            if scraper_config.get('enabled', True):
                self.providers['web_scraper'] = WebScraperProvider(
                    rate_limit=scraper_config.get('rate_limit', 1.0),
                    timeout=scraper_config.get('timeout', 30),
                    max_retries=scraper_config.get('max_retries', 3),
                    user_agent=scraper_config.get('user_agent', '')
                )
                self.logger.info("网络爬虫数据提供商初始化成功")
            
            # 设置默认活跃提供商
            if 'akshare' in self.providers:
                self.active_provider = 'akshare'
            elif 'web_scraper' in self.providers:
                self.active_provider = 'web_scraper'
            
            if not self.providers:
                raise RuntimeError("没有可用的数据提供商")
                
        except Exception as e:
            self.logger.error(f"初始化数据提供商失败: {str(e)}")
            raise
    
    def _get_provider(self, provider_name: Optional[str] = None) -> DataInterface:
        """
        获取数据提供商
        
        Args:
            provider_name: 提供商名称，None表示使用活跃提供商
            
        Returns:
            数据提供商实例
        """
        if provider_name is None:
            provider_name = self.active_provider
        
        if provider_name not in self.providers:
            raise ValueError(f"未知的数据提供商: {provider_name}")
        
        return self.providers[provider_name]
    
    def _try_providers(self, method_name: str, *args, **kwargs) -> Any:
        """
        尝试多个数据提供商执行方法
        
        Args:
            method_name: 方法名
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            方法执行结果
        """
        # 优先级顺序
        provider_order = ['akshare', 'web_scraper']
        
        last_error = None
        
        for provider_name in provider_order:
            if provider_name not in self.providers:
                continue
            
            try:
                provider = self.providers[provider_name]
                method = getattr(provider, method_name)
                result = method(*args, **kwargs)
                
                # 如果成功，更新活跃提供商
                self.active_provider = provider_name
                return result
                
            except Exception as e:
                last_error = e
                self.logger.warning(f"提供商 {provider_name} 执行 {method_name} 失败: {str(e)}")
                continue
        
        # 所有提供商都失败
        if last_error:
            raise last_error
        else:
            raise RuntimeError(f"没有可用的数据提供商执行 {method_name}")
    
    def get_stock_data(
        self, 
        stock_code: str, 
        start_date: Optional[Union[str, date, datetime]] = None,
        end_date: Optional[Union[str, date, datetime]] = None,
        fields: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        获取股票基础数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            fields: 需要的字段列表
            
        Returns:
            股票数据DataFrame
        """
        return self._try_providers('get_stock_data', stock_code, start_date, end_date, fields)
    
    def get_stock_info(self, stock_code: str) -> Dict[str, Any]:
        """
        获取股票基本信息
        
        Args:
            stock_code: 股票代码
            
        Returns:
            股票基本信息字典
        """
        return self._try_providers('get_stock_info', stock_code)
    
    def get_market_cap(self, stock_code: str) -> Optional[float]:
        """
        获取股票市值
        
        Args:
            stock_code: 股票代码
            
        Returns:
            市值（亿元）
        """
        return self._try_providers('get_market_cap', stock_code)
    
    def get_big_order_net_ratio(self, stock_code: str) -> Optional[float]:
        """
        获取大单净量比
        
        Args:
            stock_code: 股票代码
            
        Returns:
            大单净量比
        """
        return self._try_providers('get_big_order_net_ratio', stock_code)
    
    def get_williams_r(self, stock_code: str, period: int = 14) -> Optional[float]:
        """
        获取威廉指标
        
        Args:
            stock_code: 股票代码
            period: 计算周期
            
        Returns:
            威廉指标值
        """
        return self._try_providers('get_williams_r', stock_code, period)
    
    def get_volume_ratio(self, stock_code: str) -> Optional[float]:
        """
        获取量比
        
        Args:
            stock_code: 股票代码
            
        Returns:
            量比值
        """
        return self._try_providers('get_volume_ratio', stock_code)
    
    def get_chinext_stocks(self) -> List[str]:
        """
        获取创业板股票列表
        
        Returns:
            创业板股票代码列表
        """
        return self._try_providers('get_chinext_stocks')
    
    def is_st_stock(self, stock_code: str) -> bool:
        """
        判断是否为ST股票
        
        Args:
            stock_code: 股票代码
            
        Returns:
            是否为ST股票
        """
        return self._try_providers('is_st_stock', stock_code)
    
    def validate_connection(self) -> bool:
        """
        验证数据源连接
        
        Returns:
            连接是否正常
        """
        for provider_name, provider in self.providers.items():
            try:
                if provider.validate_connection():
                    self.logger.info(f"数据提供商 {provider_name} 连接正常")
                    return True
            except Exception as e:
                self.logger.warning(f"数据提供商 {provider_name} 连接失败: {str(e)}")
        
        return False
    
    def get_provider_stats(self) -> Dict[str, Any]:
        """
        获取所有提供商的统计信息
        
        Returns:
            统计信息字典
        """
        stats = {}
        
        for provider_name, provider in self.providers.items():
            try:
                if hasattr(provider, 'get_stats'):
                    stats[provider_name] = provider.get_stats()
                else:
                    stats[provider_name] = {'status': 'available'}
            except Exception as e:
                stats[provider_name] = {'status': 'error', 'error': str(e)}
        
        return stats
    
    def switch_provider(self, provider_name: str) -> None:
        """
        切换活跃数据提供商
        
        Args:
            provider_name: 提供商名称
        """
        if provider_name not in self.providers:
            raise ValueError(f"未知的数据提供商: {provider_name}")
        
        self.active_provider = provider_name
        self.logger.info(f"已切换到数据提供商: {provider_name}")
    
    def get_manager_info(self) -> Dict[str, Any]:
        """
        获取数据管理器信息
        
        Returns:
            管理器信息字典
        """
        return {
            'active_provider': self.active_provider,
            'available_providers': list(self.providers.keys()),
            'provider_count': len(self.providers),
            'stats': self.get_provider_stats()
        }

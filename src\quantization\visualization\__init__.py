#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化模块

提供回测结果、策略表现、风险指标等的可视化展示。
"""

try:
    from .result_visualizer import (
        ResultVisualizer,
        BacktestVisualizer,
        PortfolioVisualizer,
        VisualizationConfig,
        BaseVisualizer
    )
    VISUALIZATION_AVAILABLE = True
except ImportError:
    VISUALIZATION_AVAILABLE = False

__all__ = []

if VISUALIZATION_AVAILABLE:
    __all__.extend([
        'ResultVisualizer',
        'BacktestVisualizer',
        'PortfolioVisualizer',
        'VisualizationConfig',
        'BaseVisualizer'
    ])

__version__ = "1.0.0"
__author__ = "Quantization Team"
__description__ = "量化交易可视化模块"

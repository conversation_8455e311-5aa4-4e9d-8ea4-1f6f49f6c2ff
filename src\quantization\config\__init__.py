#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块

提供统一的配置管理、环境适配、配置验证和热更新功能。
"""

try:
    from .config_manager import (
        ConfigManager,
        ConfigValidator,
        ConfigSchema,
        ConfigChangeEvent,
        ConfigFormat,
        ConfigScope,
        config_manager
    )
    CONFIG_MANAGER_AVAILABLE = True
except ImportError:
    CONFIG_MANAGER_AVAILABLE = False

try:
    from quantization.config.settings import Config
    SETTINGS_AVAILABLE = True
except ImportError:
    SETTINGS_AVAILABLE = False

__all__ = []

if CONFIG_MANAGER_AVAILABLE:
    __all__.extend([
        'ConfigManager',
        'ConfigValidator',
        'ConfigSchema',
        'ConfigChangeEvent',
        'ConfigFormat',
        'ConfigScope',
        'config_manager'
    ])

if SETTINGS_AVAILABLE:
    __all__.append('Config')

__version__ = "1.0.0"
__author__ = "Quantization Team"
__description__ = "量化交易配置管理模块"

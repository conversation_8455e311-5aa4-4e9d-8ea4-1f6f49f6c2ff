#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创业板动态因子策略TICK回测系统 - 完整历史数据下载和验证功能集成测试
测试整个历史数据下载、清洗、验证、存储和可视化的完整流程
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.quantization.data_sources.historical_data_downloader import HistoricalDataDownloader
from src.quantization.data_sources.data_cleaner import DataCleaner
from src.quantization.data_sources.data_validator import DataValidator
from src.quantization.data_sources.data_storage import DataStorage
from src.quantization.data_sources.data_visualizer import DataVisualizer
from src.quantization.data_sources.smart_data_downloader import SmartDataDownloader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_historical_data_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class HistoricalDataSystemTester:
    """历史数据系统集成测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.logger = logging.getLogger(__name__)
        
        # 初始化各个组件
        self.downloader = HistoricalDataDownloader()
        self.cleaner = DataCleaner()
        self.validator = DataValidator()
        self.storage = DataStorage()
        self.visualizer = DataVisualizer(self.storage, self.validator)
        self.smart_downloader = SmartDataDownloader()
        
        # 测试配置
        self.test_config = {
            'start_date': '2024-05-01',  # 测试2个月的数据
            'end_date': '2024-06-30',
            'test_stock_count': 20,      # 测试20只股票
            'data_types': ['minute', 'daily']
        }
        
        self.logger.info("历史数据系统集成测试器初始化完成")
    
    def run_complete_test(self) -> Dict[str, Any]:
        """
        运行完整的系统集成测试
        
        Returns:
            测试结果
        """
        self.logger.info("=" * 80)
        self.logger.info("开始创业板动态因子策略TICK回测系统 - 完整历史数据下载和验证功能集成测试")
        self.logger.info("=" * 80)
        
        test_results = {
            'start_time': datetime.now().isoformat(),
            'test_config': self.test_config,
            'test_phases': {},
            'overall_success': False,
            'summary': {}
        }
        
        try:
            # 阶段1：准备测试数据
            self.logger.info("\n" + "=" * 50)
            self.logger.info("阶段1：准备测试数据")
            self.logger.info("=" * 50)
            
            stock_codes = self._prepare_test_data()
            test_results['test_phases']['data_preparation'] = {
                'success': len(stock_codes) > 0,
                'stock_count': len(stock_codes),
                'stock_codes': stock_codes[:10]  # 只记录前10个
            }
            
            if not stock_codes:
                raise Exception("无法获取测试股票代码")
            
            # 阶段2：历史数据批量下载测试
            self.logger.info("\n" + "=" * 50)
            self.logger.info("阶段2：历史数据批量下载测试")
            self.logger.info("=" * 50)
            
            download_results = self._test_historical_download(stock_codes)
            test_results['test_phases']['historical_download'] = download_results
            
            # 阶段3：数据清洗和整合测试
            self.logger.info("\n" + "=" * 50)
            self.logger.info("阶段3：数据清洗和整合测试")
            self.logger.info("=" * 50)
            
            cleaning_results = self._test_data_cleaning(stock_codes)
            test_results['test_phases']['data_cleaning'] = cleaning_results
            
            # 阶段4：数据真实性验证测试
            self.logger.info("\n" + "=" * 50)
            self.logger.info("阶段4：数据真实性验证测试")
            self.logger.info("=" * 50)
            
            validation_results = self._test_data_validation(stock_codes)
            test_results['test_phases']['data_validation'] = validation_results
            
            # 阶段5：数据存储和查询测试
            self.logger.info("\n" + "=" * 50)
            self.logger.info("阶段5：数据存储和查询测试")
            self.logger.info("=" * 50)
            
            storage_results = self._test_data_storage(stock_codes)
            test_results['test_phases']['data_storage'] = storage_results
            
            # 阶段6：数据统计和可视化测试
            self.logger.info("\n" + "=" * 50)
            self.logger.info("阶段6：数据统计和可视化测试")
            self.logger.info("=" * 50)
            
            visualization_results = self._test_data_visualization(stock_codes)
            test_results['test_phases']['data_visualization'] = visualization_results
            
            # 计算整体成功率
            phase_success_count = sum(1 for phase in test_results['test_phases'].values() 
                                    if phase.get('success', False))
            total_phases = len(test_results['test_phases'])
            
            test_results['overall_success'] = phase_success_count == total_phases
            test_results['success_rate'] = phase_success_count / total_phases if total_phases > 0 else 0
            
            # 生成测试摘要
            test_results['summary'] = self._generate_test_summary(test_results)
            
        except Exception as e:
            self.logger.error(f"集成测试失败: {e}")
            test_results['error'] = str(e)
        
        finally:
            test_results['end_time'] = datetime.now().isoformat()
            test_results['duration'] = self._calculate_duration(
                test_results['start_time'], test_results['end_time']
            )
        
        # 输出测试结果
        self._output_test_results(test_results)
        
        return test_results
    
    def _prepare_test_data(self) -> List[str]:
        """准备测试数据"""
        try:
            # 获取创业板股票列表
            stock_codes = self.smart_downloader.get_all_chinext_stocks()
            
            if not stock_codes:
                self.logger.warning("无法获取创业板股票列表，使用默认测试股票")
                stock_codes = [
                    '300015.SZ', '300059.SZ', '300124.SZ', '300144.SZ', '300274.SZ',
                    '300347.SZ', '300408.SZ', '300450.SZ', '300498.SZ', '300601.SZ',
                    '300750.SZ', '300760.SZ', '300896.SZ', '300919.SZ', '301029.SZ',
                    '301236.SZ', '301269.SZ', '301308.SZ', '301319.SZ', '301528.SZ'
                ]
            
            # 限制测试股票数量
            test_stocks = stock_codes[:self.test_config['test_stock_count']]
            
            self.logger.info(f"准备测试数据完成: {len(test_stocks)}只股票")
            self.logger.info(f"测试股票: {test_stocks[:5]}...")
            
            return test_stocks
            
        except Exception as e:
            self.logger.error(f"准备测试数据失败: {e}")
            return []
    
    def _test_historical_download(self, stock_codes: List[str]) -> Dict[str, Any]:
        """测试历史数据批量下载"""
        try:
            self.logger.info("开始测试历史数据批量下载...")
            
            # 执行批量下载
            download_results = self.downloader.batch_download(
                stock_codes=stock_codes,
                start_date=self.test_config['start_date'],
                end_date=self.test_config['end_date'],
                data_types=self.test_config['data_types'],
                resume=True
            )
            
            success = download_results.get('success_rate', 0) > 0.8  # 80%成功率
            
            self.logger.info(f"历史数据下载测试完成: 成功率 {download_results.get('success_rate', 0):.2%}")
            
            return {
                'success': success,
                'download_results': download_results,
                'success_rate': download_results.get('success_rate', 0),
                'total_tasks': download_results.get('total_tasks', 0),
                'completed_tasks': download_results.get('completed_tasks', 0)
            }
            
        except Exception as e:
            self.logger.error(f"历史数据下载测试失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _test_data_cleaning(self, stock_codes: List[str]) -> Dict[str, Any]:
        """测试数据清洗和整合"""
        try:
            self.logger.info("开始测试数据清洗和整合...")
            
            # 测试分时数据清洗
            minute_results = self.cleaner.batch_clean_data(stock_codes, 'minute')
            
            # 测试日线数据清洗
            daily_results = self.cleaner.batch_clean_data(stock_codes, 'daily')
            
            # 生成清洗报告
            minute_report = self.cleaner.generate_cleaning_report(
                minute_results, "data/minute_cleaning_report.json"
            )
            daily_report = self.cleaner.generate_cleaning_report(
                daily_results, "data/daily_cleaning_report.json"
            )
            
            success = (minute_results.get('processed_stocks', 0) > 0 or 
                      daily_results.get('processed_stocks', 0) > 0)
            
            self.logger.info(f"数据清洗测试完成: 分时数据 {minute_results.get('processed_stocks', 0)}只, "
                           f"日线数据 {daily_results.get('processed_stocks', 0)}只")
            
            return {
                'success': success,
                'minute_results': minute_results,
                'daily_results': daily_results,
                'reports': [minute_report, daily_report]
            }
            
        except Exception as e:
            self.logger.error(f"数据清洗测试失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _test_data_validation(self, stock_codes: List[str]) -> Dict[str, Any]:
        """测试数据真实性验证"""
        try:
            self.logger.info("开始测试数据真实性验证...")
            
            # 获取测试日期
            trading_days = self.smart_downloader.get_trading_days_range(
                self.test_config['start_date'], self.test_config['end_date']
            )
            test_dates = trading_days[:10]  # 测试前10个交易日
            
            # 批量验证数据
            validation_results = self.validator.batch_validate_data(
                stock_codes[:10], test_dates, 'minute', max_workers=2
            )
            
            # 生成验证报告
            report_path = self.validator.generate_validation_report(
                validation_results, "data/validation_report.json"
            )
            
            # 验证数据完整性
            integrity_results = self.validator.validate_data_integrity(
                stock_codes[:10], self.test_config['start_date'], self.test_config['end_date']
            )
            
            success = validation_results.get('success_rate', 0) > 0.5  # 50%成功率
            
            self.logger.info(f"数据验证测试完成: 验证 {validation_results.get('total_validations', 0)}次, "
                           f"成功率 {validation_results.get('success_rate', 0):.2%}")
            
            return {
                'success': success,
                'validation_results': validation_results,
                'integrity_results': integrity_results,
                'report_path': report_path
            }
            
        except Exception as e:
            self.logger.error(f"数据验证测试失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _test_data_storage(self, stock_codes: List[str]) -> Dict[str, Any]:
        """测试数据存储和查询"""
        try:
            self.logger.info("开始测试数据存储和查询...")
            
            # 测试数据存储
            test_stock = stock_codes[0] if stock_codes else '300015.SZ'
            test_date = self.test_config['start_date']
            
            # 模拟保存数据（实际应该从下载器获取）
            import pandas as pd
            import numpy as np
            
            # 创建模拟分时数据
            minute_data = pd.DataFrame({
                'datetime': pd.date_range(f'{test_date} 09:30:00', f'{test_date} 15:00:00', freq='1min'),
                'open': np.random.uniform(10, 20, 241),
                'high': np.random.uniform(10, 20, 241),
                'low': np.random.uniform(10, 20, 241),
                'close': np.random.uniform(10, 20, 241),
                'volume': np.random.randint(1000, 10000, 241)
            })
            
            # 测试保存分时数据
            save_success = self.storage.save_minute_data(test_stock, test_date, minute_data)
            
            # 测试查询数据
            retrieved_data = self.storage.get_minute_data(test_stock, test_date)
            
            # 测试批量查询
            batch_data = self.storage.batch_get_data(
                stock_codes[:3], test_date, test_date, 'minute', max_workers=2
            )
            
            # 获取存储信息
            storage_info = self.storage.get_storage_info()
            
            success = save_success and not retrieved_data.empty
            
            self.logger.info(f"数据存储测试完成: 保存成功 {save_success}, "
                           f"查询数据 {len(retrieved_data)}条, 批量查询 {len(batch_data)}只股票")
            
            return {
                'success': success,
                'save_success': save_success,
                'retrieved_records': len(retrieved_data),
                'batch_query_count': len(batch_data),
                'storage_info': storage_info
            }
            
        except Exception as e:
            self.logger.error(f"数据存储测试失败: {e}")
            return {'success': False, 'error': str(e)}

    def _test_data_visualization(self, stock_codes: List[str]) -> Dict[str, Any]:
        """测试数据统计和可视化"""
        try:
            self.logger.info("开始测试数据统计和可视化...")

            # 生成综合统计报告
            report = self.visualizer.generate_comprehensive_report(
                self.test_config['start_date'],
                self.test_config['end_date'],
                stock_codes
            )

            # 创建各种图表
            chart_paths = {}

            # 数据质量分布图
            chart_paths['quality_distribution'] = self.visualizer.create_quality_distribution_chart(report)

            # 数据完整性趋势图
            chart_paths['completeness_trend'] = self.visualizer.create_completeness_trend_chart(
                self.test_config['start_date'], self.test_config['end_date']
            )

            # 股票质量排名图
            chart_paths['stock_ranking'] = self.visualizer.create_stock_quality_ranking(
                stock_codes, self.test_config['start_date'], self.test_config['end_date']
            )

            # 数据量统计图
            chart_paths['data_volume'] = self.visualizer.create_data_volume_statistics(
                self.test_config['start_date'], self.test_config['end_date']
            )

            # 生成HTML报告
            html_report = self.visualizer.generate_html_report(
                report, stock_codes, self.test_config['start_date'], self.test_config['end_date']
            )

            # 导出Excel摘要
            excel_summary = self.visualizer.export_data_summary(
                self.test_config['start_date'], self.test_config['end_date']
            )

            # 创建仪表板摘要
            self.visualizer.create_dashboard_summary(
                report, stock_codes, self.test_config['start_date'], self.test_config['end_date']
            )

            # 检查生成的文件
            generated_files = [path for path in chart_paths.values() if path and os.path.exists(path)]
            generated_files.extend([html_report, excel_summary] if html_report and excel_summary else [])

            success = len(generated_files) > 0

            self.logger.info(f"数据可视化测试完成: 生成 {len(generated_files)}个文件")

            return {
                'success': success,
                'report': {
                    'total_stocks': report.total_stocks,
                    'total_trading_days': report.total_trading_days,
                    'data_completeness': report.data_completeness,
                    'average_quality_score': report.average_quality_score
                },
                'chart_paths': chart_paths,
                'html_report': html_report,
                'excel_summary': excel_summary,
                'generated_files_count': len(generated_files)
            }

        except Exception as e:
            self.logger.error(f"数据可视化测试失败: {e}")
            return {'success': False, 'error': str(e)}

    def _generate_test_summary(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成测试摘要"""
        summary = {
            'test_duration': test_results.get('duration', 'Unknown'),
            'overall_success': test_results.get('overall_success', False),
            'success_rate': test_results.get('success_rate', 0),
            'phase_results': {},
            'recommendations': []
        }

        # 分析各阶段结果
        for phase_name, phase_result in test_results.get('test_phases', {}).items():
            summary['phase_results'][phase_name] = {
                'success': phase_result.get('success', False),
                'key_metrics': self._extract_key_metrics(phase_name, phase_result)
            }

            # 生成建议
            if not phase_result.get('success', False):
                summary['recommendations'].append(f"需要修复{phase_name}阶段的问题")

        # 整体建议
        if summary['success_rate'] < 1.0:
            summary['recommendations'].append("建议进行进一步的系统优化和错误修复")
        else:
            summary['recommendations'].append("系统运行良好，可以投入生产使用")

        return summary

    def _extract_key_metrics(self, phase_name: str, phase_result: Dict[str, Any]) -> Dict[str, Any]:
        """提取关键指标"""
        metrics = {}

        if phase_name == 'data_preparation':
            metrics['stock_count'] = phase_result.get('stock_count', 0)
        elif phase_name == 'historical_download':
            metrics['success_rate'] = phase_result.get('success_rate', 0)
            metrics['total_tasks'] = phase_result.get('total_tasks', 0)
        elif phase_name == 'data_cleaning':
            minute_results = phase_result.get('minute_results', {})
            daily_results = phase_result.get('daily_results', {})
            metrics['minute_processed'] = minute_results.get('processed_stocks', 0)
            metrics['daily_processed'] = daily_results.get('processed_stocks', 0)
        elif phase_name == 'data_validation':
            metrics['success_rate'] = phase_result.get('validation_results', {}).get('success_rate', 0)
        elif phase_name == 'data_storage':
            metrics['retrieved_records'] = phase_result.get('retrieved_records', 0)
            metrics['batch_query_count'] = phase_result.get('batch_query_count', 0)
        elif phase_name == 'data_visualization':
            metrics['generated_files'] = phase_result.get('generated_files_count', 0)

        return metrics

    def _calculate_duration(self, start_time: str, end_time: str) -> str:
        """计算测试持续时间"""
        try:
            start = datetime.fromisoformat(start_time)
            end = datetime.fromisoformat(end_time)
            duration = end - start

            hours = duration.seconds // 3600
            minutes = (duration.seconds % 3600) // 60
            seconds = duration.seconds % 60

            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        except:
            return "Unknown"

    def _output_test_results(self, test_results: Dict[str, Any]):
        """输出测试结果"""
        self.logger.info("\n" + "=" * 80)
        self.logger.info("测试结果摘要")
        self.logger.info("=" * 80)

        # 基本信息
        self.logger.info(f"测试开始时间: {test_results.get('start_time', 'Unknown')}")
        self.logger.info(f"测试结束时间: {test_results.get('end_time', 'Unknown')}")
        self.logger.info(f"测试持续时间: {test_results.get('duration', 'Unknown')}")
        self.logger.info(f"整体成功: {'是' if test_results.get('overall_success', False) else '否'}")
        self.logger.info(f"成功率: {test_results.get('success_rate', 0):.2%}")

        # 各阶段结果
        self.logger.info("\n各阶段测试结果:")
        for phase_name, phase_result in test_results.get('test_phases', {}).items():
            status = "✓" if phase_result.get('success', False) else "✗"
            self.logger.info(f"  {status} {phase_name}: {'成功' if phase_result.get('success', False) else '失败'}")

            if 'error' in phase_result:
                self.logger.info(f"    错误: {phase_result['error']}")

        # 建议
        summary = test_results.get('summary', {})
        recommendations = summary.get('recommendations', [])
        if recommendations:
            self.logger.info("\n建议:")
            for i, rec in enumerate(recommendations, 1):
                self.logger.info(f"  {i}. {rec}")

        # 保存详细结果到文件
        import json
        result_file = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            # 转换不可序列化的对象
            serializable_results = self._make_serializable(test_results)
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, ensure_ascii=False, indent=2)
            self.logger.info(f"\n详细测试结果已保存到: {result_file}")
        except Exception as e:
            self.logger.error(f"保存测试结果失败: {e}")

    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            return self._make_serializable(obj.__dict__)
        else:
            try:
                import json
                json.dumps(obj)
                return obj
            except:
                return str(obj)

def main():
    """主函数"""
    print("创业板动态因子策略TICK回测系统 - 完整历史数据下载和验证功能集成测试")
    print("=" * 80)

    # 创建测试器
    tester = HistoricalDataSystemTester()

    # 运行完整测试
    results = tester.run_complete_test()

    # 输出最终结果
    print("\n" + "=" * 80)
    print("测试完成!")
    print(f"整体成功: {'是' if results.get('overall_success', False) else '否'}")
    print(f"成功率: {results.get('success_rate', 0):.2%}")
    print("=" * 80)

    return results

if __name__ == "__main__":
    main()

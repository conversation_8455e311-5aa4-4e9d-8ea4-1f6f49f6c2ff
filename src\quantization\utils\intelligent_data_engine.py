#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能数据获取引擎

实现智能数据源选择、自适应数据获取策略和数据源健康监控。
基于机器学习算法优化数据获取效率和可靠性。
"""

import asyncio
import time
import random
from typing import Dict, List, Optional, Any, Tuple, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
import pandas as pd
from collections import defaultdict, deque
import threading
import json
import pickle
from pathlib import Path

from quantization.utils.logger import get_logger
from quantization.utils.exceptions import DataAcquisitionError


class DataSourceType(Enum):
    """数据源类型"""
    PRIMARY = "primary"      # 主要数据源 (akshare)
    SECONDARY = "secondary"  # 次要数据源 (web scraping)
    TERTIARY = "tertiary"    # 备用数据源 (technical analysis)
    CACHE = "cache"          # 缓存数据源


class DataSourceStatus(Enum):
    """数据源状态"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    OFFLINE = "offline"


@dataclass
class DataSourceMetrics:
    """数据源指标"""
    source_id: str
    source_type: DataSourceType
    success_rate: float = 0.0
    average_response_time: float = 0.0
    error_count: int = 0
    total_requests: int = 0
    last_success_time: Optional[float] = None
    last_error_time: Optional[float] = None
    status: DataSourceStatus = DataSourceStatus.HEALTHY
    quality_score: float = 1.0
    reliability_score: float = 1.0


@dataclass
class DataRequest:
    """数据请求"""
    request_id: str
    data_type: str
    parameters: Dict[str, Any]
    priority: int = 1
    timeout: float = 30.0
    retry_count: int = 0
    max_retries: int = 3
    created_time: float = field(default_factory=time.time)
    preferred_sources: List[str] = field(default_factory=list)


class DataSourceHealthMonitor:
    """
    数据源健康监控器
    
    实时监控各数据源的健康状态和性能指标。
    """
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.metrics: Dict[str, DataSourceMetrics] = {}
        self.monitoring_enabled = False
        self.monitor_thread = None
        self._lock = threading.RLock()
        
        # 健康检查配置
        self.health_check_interval = 60.0  # 秒
        self.degraded_threshold = 0.8      # 成功率阈值
        self.unhealthy_threshold = 0.5     # 不健康阈值
        self.response_time_threshold = 10.0 # 响应时间阈值
    
    def register_source(self, source_id: str, source_type: DataSourceType):
        """注册数据源"""
        with self._lock:
            self.metrics[source_id] = DataSourceMetrics(
                source_id=source_id,
                source_type=source_type
            )
        self.logger.info(f"数据源已注册: {source_id} ({source_type.value})")
    
    def update_metrics(self, source_id: str, success: bool, 
                      response_time: float, error_msg: str = None):
        """更新数据源指标"""
        with self._lock:
            if source_id not in self.metrics:
                return
            
            metrics = self.metrics[source_id]
            metrics.total_requests += 1
            
            if success:
                metrics.last_success_time = time.time()
                # 更新平均响应时间
                if metrics.average_response_time == 0:
                    metrics.average_response_time = response_time
                else:
                    # 指数移动平均
                    alpha = 0.1
                    metrics.average_response_time = (
                        alpha * response_time + 
                        (1 - alpha) * metrics.average_response_time
                    )
            else:
                metrics.error_count += 1
                metrics.last_error_time = time.time()
            
            # 更新成功率
            metrics.success_rate = (
                (metrics.total_requests - metrics.error_count) / 
                metrics.total_requests
            )
            
            # 更新状态
            self._update_source_status(source_id)
    
    def _update_source_status(self, source_id: str):
        """更新数据源状态"""
        metrics = self.metrics[source_id]
        
        # 基于成功率和响应时间判断状态
        if metrics.success_rate >= self.degraded_threshold and \
           metrics.average_response_time <= self.response_time_threshold:
            metrics.status = DataSourceStatus.HEALTHY
        elif metrics.success_rate >= self.unhealthy_threshold:
            metrics.status = DataSourceStatus.DEGRADED
        else:
            metrics.status = DataSourceStatus.UNHEALTHY
        
        # 计算质量评分
        metrics.quality_score = self._calculate_quality_score(metrics)
        metrics.reliability_score = self._calculate_reliability_score(metrics)
    
    def _calculate_quality_score(self, metrics: DataSourceMetrics) -> float:
        """计算数据质量评分"""
        # 基于成功率、响应时间和错误频率计算
        success_score = metrics.success_rate
        
        # 响应时间评分 (越快越好)
        time_score = max(0, 1 - metrics.average_response_time / 30.0)
        
        # 综合评分
        quality_score = 0.6 * success_score + 0.4 * time_score
        return max(0.0, min(1.0, quality_score))
    
    def _calculate_reliability_score(self, metrics: DataSourceMetrics) -> float:
        """计算可靠性评分"""
        current_time = time.time()
        
        # 最近成功时间评分
        if metrics.last_success_time:
            time_since_success = current_time - metrics.last_success_time
            recency_score = max(0, 1 - time_since_success / 3600)  # 1小时内
        else:
            recency_score = 0
        
        # 错误频率评分
        if metrics.total_requests > 0:
            error_rate = metrics.error_count / metrics.total_requests
            error_score = 1 - error_rate
        else:
            error_score = 1
        
        # 综合可靠性评分
        reliability_score = 0.7 * error_score + 0.3 * recency_score
        return max(0.0, min(1.0, reliability_score))
    
    def get_best_sources(self, data_type: str, count: int = 3) -> List[str]:
        """获取最佳数据源"""
        with self._lock:
            # 过滤健康的数据源
            healthy_sources = [
                (source_id, metrics) for source_id, metrics in self.metrics.items()
                if metrics.status in [DataSourceStatus.HEALTHY, DataSourceStatus.DEGRADED]
            ]
            
            # 按质量评分排序
            healthy_sources.sort(
                key=lambda x: (x[1].quality_score, x[1].reliability_score),
                reverse=True
            )
            
            return [source_id for source_id, _ in healthy_sources[:count]]
    
    def start_monitoring(self):
        """启动健康监控"""
        if self.monitoring_enabled:
            return
        
        self.monitoring_enabled = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitor_thread.start()
        self.logger.info("数据源健康监控已启动")
    
    def stop_monitoring(self):
        """停止健康监控"""
        self.monitoring_enabled = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        self.logger.info("数据源健康监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_enabled:
            try:
                self._perform_health_checks()
                time.sleep(self.health_check_interval)
            except Exception as e:
                self.logger.error(f"健康检查失败: {e}")
                time.sleep(10)
    
    def _perform_health_checks(self):
        """执行健康检查"""
        current_time = time.time()
        
        with self._lock:
            for source_id, metrics in self.metrics.items():
                # 检查长时间无响应的数据源
                if metrics.last_success_time:
                    time_since_success = current_time - metrics.last_success_time
                    if time_since_success > 3600:  # 1小时无成功请求
                        if metrics.status != DataSourceStatus.OFFLINE:
                            metrics.status = DataSourceStatus.OFFLINE
                            self.logger.warning(f"数据源离线: {source_id}")
    
    def get_health_report(self) -> Dict[str, Any]:
        """获取健康报告"""
        with self._lock:
            report = {
                'total_sources': len(self.metrics),
                'healthy_sources': 0,
                'degraded_sources': 0,
                'unhealthy_sources': 0,
                'offline_sources': 0,
                'source_details': {}
            }
            
            for source_id, metrics in self.metrics.items():
                # 统计状态
                if metrics.status == DataSourceStatus.HEALTHY:
                    report['healthy_sources'] += 1
                elif metrics.status == DataSourceStatus.DEGRADED:
                    report['degraded_sources'] += 1
                elif metrics.status == DataSourceStatus.UNHEALTHY:
                    report['unhealthy_sources'] += 1
                else:
                    report['offline_sources'] += 1
                
                # 详细信息
                report['source_details'][source_id] = {
                    'type': metrics.source_type.value,
                    'status': metrics.status.value,
                    'success_rate': metrics.success_rate,
                    'average_response_time': metrics.average_response_time,
                    'quality_score': metrics.quality_score,
                    'reliability_score': metrics.reliability_score,
                    'total_requests': metrics.total_requests,
                    'error_count': metrics.error_count
                }
            
            return report


class IntelligentDataRouter:
    """
    智能数据路由器
    
    基于数据源健康状态和历史性能智能选择最佳数据源。
    """
    
    def __init__(self, health_monitor: DataSourceHealthMonitor):
        self.logger = get_logger(self.__class__.__name__)
        self.health_monitor = health_monitor
        self.routing_history = deque(maxlen=1000)
        self.source_weights = defaultdict(float)
        self._lock = threading.RLock()
        
        # 路由策略配置
        self.load_balancing_enabled = True
        self.failover_enabled = True
        self.adaptive_routing = True
    
    def route_request(self, request: DataRequest) -> List[str]:
        """
        路由数据请求到最佳数据源
        
        Args:
            request: 数据请求对象
            
        Returns:
            按优先级排序的数据源列表
        """
        with self._lock:
            # 获取候选数据源
            candidate_sources = self._get_candidate_sources(request)
            
            if not candidate_sources:
                raise DataAcquisitionError("没有可用的数据源")
            
            # 应用路由策略
            routed_sources = self._apply_routing_strategy(
                candidate_sources, request
            )
            
            # 记录路由历史
            self.routing_history.append({
                'request_id': request.request_id,
                'data_type': request.data_type,
                'selected_sources': routed_sources,
                'timestamp': time.time()
            })
            
            return routed_sources
    
    def _get_candidate_sources(self, request: DataRequest) -> List[str]:
        """获取候选数据源"""
        # 如果请求指定了首选数据源
        if request.preferred_sources:
            return request.preferred_sources
        
        # 获取健康的数据源
        return self.health_monitor.get_best_sources(
            request.data_type, count=5
        )
    
    def _apply_routing_strategy(self, sources: List[str], 
                              request: DataRequest) -> List[str]:
        """应用路由策略"""
        if not self.adaptive_routing:
            return sources
        
        # 获取数据源指标
        source_scores = []
        for source_id in sources:
            metrics = self.health_monitor.metrics.get(source_id)
            if not metrics:
                continue
            
            # 计算综合评分
            score = self._calculate_routing_score(metrics, request)
            source_scores.append((source_id, score))
        
        # 按评分排序
        source_scores.sort(key=lambda x: x[1], reverse=True)
        
        return [source_id for source_id, _ in source_scores]
    
    def _calculate_routing_score(self, metrics: DataSourceMetrics, 
                               request: DataRequest) -> float:
        """计算路由评分"""
        # 基础评分
        base_score = (
            0.4 * metrics.quality_score +
            0.3 * metrics.reliability_score +
            0.2 * (1 - metrics.average_response_time / 30.0) +
            0.1 * metrics.success_rate
        )
        
        # 优先级调整
        if metrics.source_type == DataSourceType.PRIMARY:
            priority_bonus = 0.2
        elif metrics.source_type == DataSourceType.SECONDARY:
            priority_bonus = 0.1
        else:
            priority_bonus = 0.0
        
        # 负载均衡调整
        load_penalty = self.source_weights.get(metrics.source_id, 0) * 0.1
        
        final_score = base_score + priority_bonus - load_penalty
        return max(0.0, min(1.0, final_score))
    
    def update_routing_feedback(self, request_id: str, source_id: str, 
                              success: bool, response_time: float):
        """更新路由反馈"""
        with self._lock:
            # 更新数据源权重
            if success:
                self.source_weights[source_id] = max(
                    0, self.source_weights[source_id] - 0.1
                )
            else:
                self.source_weights[source_id] += 0.2
            
            # 更新健康监控指标
            self.health_monitor.update_metrics(
                source_id, success, response_time
            )


class IntelligentDataEngine:
    """
    智能数据获取引擎
    
    整合健康监控、智能路由和自适应策略的数据获取引擎。
    """
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.health_monitor = DataSourceHealthMonitor()
        self.router = IntelligentDataRouter(self.health_monitor)
        self.request_queue = asyncio.Queue()
        self.active_requests: Dict[str, DataRequest] = {}
        self._lock = threading.RLock()
        
        # 注册默认数据源
        self._register_default_sources()
        
        # 启动健康监控
        self.health_monitor.start_monitoring()
    
    def _register_default_sources(self):
        """注册默认数据源"""
        default_sources = [
            ("akshare_api", DataSourceType.PRIMARY),
            ("web_scraper", DataSourceType.SECONDARY),
            ("technical_analysis", DataSourceType.TERTIARY),
            ("local_cache", DataSourceType.CACHE)
        ]
        
        for source_id, source_type in default_sources:
            self.health_monitor.register_source(source_id, source_type)
    
    async def get_data(self, data_type: str, parameters: Dict[str, Any],
                      priority: int = 1, timeout: float = 30.0) -> Any:
        """
        智能获取数据
        
        Args:
            data_type: 数据类型
            parameters: 请求参数
            priority: 优先级
            timeout: 超时时间
            
        Returns:
            获取的数据
        """
        request_id = f"{data_type}_{int(time.time() * 1000000)}"
        
        request = DataRequest(
            request_id=request_id,
            data_type=data_type,
            parameters=parameters,
            priority=priority,
            timeout=timeout
        )
        
        try:
            # 路由请求
            source_list = self.router.route_request(request)
            
            # 尝试从数据源获取数据
            for source_id in source_list:
                try:
                    start_time = time.time()
                    data = await self._fetch_from_source(
                        source_id, data_type, parameters, timeout
                    )
                    
                    response_time = time.time() - start_time
                    
                    # 更新路由反馈
                    self.router.update_routing_feedback(
                        request_id, source_id, True, response_time
                    )
                    
                    return data
                    
                except Exception as e:
                    response_time = time.time() - start_time
                    self.logger.warning(f"数据源 {source_id} 获取失败: {e}")
                    
                    # 更新路由反馈
                    self.router.update_routing_feedback(
                        request_id, source_id, False, response_time
                    )
                    
                    continue
            
            # 所有数据源都失败
            raise DataAcquisitionError(f"所有数据源获取 {data_type} 数据失败")
            
        except Exception as e:
            self.logger.error(f"智能数据获取失败: {e}")
            raise
    
    async def _fetch_from_source(self, source_id: str, data_type: str,
                               parameters: Dict[str, Any], 
                               timeout: float) -> Any:
        """从指定数据源获取数据"""
        # 这里应该根据source_id调用相应的数据获取方法
        # 为了演示，返回模拟数据
        await asyncio.sleep(random.uniform(0.1, 2.0))  # 模拟网络延迟
        
        # 模拟偶尔的失败
        if random.random() < 0.1:
            raise DataAcquisitionError(f"模拟 {source_id} 数据获取失败")
        
        return {
            'source': source_id,
            'data_type': data_type,
            'parameters': parameters,
            'timestamp': time.time(),
            'data': f"模拟数据来自 {source_id}"
        }
    
    def get_engine_status(self) -> Dict[str, Any]:
        """获取引擎状态"""
        return {
            'health_monitor': self.health_monitor.get_health_report(),
            'active_requests': len(self.active_requests),
            'routing_history_size': len(self.router.routing_history)
        }
    
    def shutdown(self):
        """关闭引擎"""
        self.health_monitor.stop_monitoring()
        self.logger.info("智能数据获取引擎已关闭")


# 全局实例
intelligent_data_engine = IntelligentDataEngine()

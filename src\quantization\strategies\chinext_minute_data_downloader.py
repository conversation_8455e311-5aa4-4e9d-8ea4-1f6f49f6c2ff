#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创业板股票分时数据批量下载系统
实现1分钟级别分时数据的批量下载、存储和管理功能
"""

import os
import sys
import sqlite3
import pandas as pd
import akshare as ak
import logging
from datetime import datetime, timedelta, date
from typing import List, Dict, Optional, Tuple, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import time
import json
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import random

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

class ChinextMinuteDataDownloader:
    """创业板分时数据下载器"""
    
    def __init__(self, db_path: str = "data/chinext_minute_data.db"):
        """
        初始化下载器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.logger = self._setup_logger()
        self._init_database()
        
        # 下载配置
        self.max_workers = 5  # 最大并发数
        self.request_delay = 0.5  # 请求间隔（秒）
        self.retry_times = 3  # 重试次数

        # 网络稳定性配置
        self.max_retries = 5  # 最大重试次数
        self.retry_delay = 1.0  # 基础重试延迟（秒）
        self.timeout = 30  # 请求超时时间（秒）
        self.backoff_factor = 2.0  # 退避因子
        self.jitter_range = 0.5  # 随机抖动范围

        # 缓存配置
        self.cache_enabled = True  # 启用缓存
        self.cache_expire_hours = 24  # 缓存过期时间（小时）
        self.memory_cache = {}  # 内存缓存
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'expired': 0
        }

        # 性能监控配置
        self.performance_stats = {
            'total_downloads': 0,
            'successful_downloads': 0,
            'failed_downloads': 0,
            'total_download_time': 0.0,
            'total_records_downloaded': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'network_requests': 0,
            'database_operations': 0,
            'memory_usage_mb': 0.0,
            'start_time': time.time()
        }

        # 配置网络会话
        self._setup_network_session()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(f"{__name__}.ChinextMinuteDataDownloader")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _setup_network_session(self):
        """配置网络会话，增强稳定性"""
        self.session = requests.Session()

        # 配置重试策略
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=self.backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )

        # 配置HTTP适配器
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })

        self.logger.info("网络会话配置完成")

    def _get_cache_key(self, stock_code: str, date: str) -> str:
        """生成缓存键"""
        return f"{stock_code}_{date}"

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if not self.cache_enabled or cache_key not in self.memory_cache:
            return False

        cache_entry = self.memory_cache[cache_key]
        cache_time = cache_entry.get('timestamp', 0)
        current_time = time.time()

        # 检查是否过期
        if current_time - cache_time > self.cache_expire_hours * 3600:
            del self.memory_cache[cache_key]
            self.cache_stats['expired'] += 1
            return False

        return True

    def _get_from_cache(self, stock_code: str, date: str) -> Optional[pd.DataFrame]:
        """从缓存获取数据"""
        cache_key = self._get_cache_key(stock_code, date)

        if self._is_cache_valid(cache_key):
            self.cache_stats['hits'] += 1
            return self.memory_cache[cache_key]['data']

        self.cache_stats['misses'] += 1
        return None

    def _save_to_cache(self, stock_code: str, date: str, data: pd.DataFrame):
        """保存数据到缓存"""
        if not self.cache_enabled:
            return

        cache_key = self._get_cache_key(stock_code, date)
        self.memory_cache[cache_key] = {
            'data': data.copy(),
            'timestamp': time.time()
        }

        # 限制缓存大小，避免内存溢出
        if len(self.memory_cache) > 1000:  # 最多缓存1000个条目
            # 删除最旧的缓存条目
            oldest_key = min(self.memory_cache.keys(),
                           key=lambda k: self.memory_cache[k]['timestamp'])
            del self.memory_cache[oldest_key]

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = self.cache_stats['hits'] / total_requests if total_requests > 0 else 0

        return {
            'hits': self.cache_stats['hits'],
            'misses': self.cache_stats['misses'],
            'expired': self.cache_stats['expired'],
            'total_requests': total_requests,
            'hit_rate': hit_rate,
            'cache_size': len(self.memory_cache)
        }

    def clear_cache(self):
        """清空缓存"""
        self.memory_cache.clear()
        self.cache_stats = {'hits': 0, 'misses': 0, 'expired': 0}
        self.logger.info("缓存已清空")

    def _update_performance_stats(self, operation: str, **kwargs):
        """更新性能统计"""
        if operation == 'download_start':
            self.performance_stats['total_downloads'] += 1
        elif operation == 'download_success':
            self.performance_stats['successful_downloads'] += 1
            self.performance_stats['total_download_time'] += kwargs.get('time', 0)
            self.performance_stats['total_records_downloaded'] += kwargs.get('records', 0)
        elif operation == 'download_failed':
            self.performance_stats['failed_downloads'] += 1
        elif operation == 'cache_hit':
            self.performance_stats['cache_hits'] += 1
        elif operation == 'cache_miss':
            self.performance_stats['cache_misses'] += 1
        elif operation == 'network_request':
            self.performance_stats['network_requests'] += 1
        elif operation == 'database_operation':
            self.performance_stats['database_operations'] += 1

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        current_time = time.time()
        uptime = current_time - self.performance_stats['start_time']

        # 计算内存使用情况
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            self.performance_stats['memory_usage_mb'] = memory_mb
        except:
            memory_mb = 0.0

        stats = self.performance_stats.copy()

        # 计算派生指标
        total_downloads = stats['total_downloads']
        if total_downloads > 0:
            stats['success_rate'] = stats['successful_downloads'] / total_downloads
            stats['failure_rate'] = stats['failed_downloads'] / total_downloads
            stats['avg_download_time'] = stats['total_download_time'] / stats['successful_downloads'] if stats['successful_downloads'] > 0 else 0
            stats['avg_records_per_download'] = stats['total_records_downloaded'] / stats['successful_downloads'] if stats['successful_downloads'] > 0 else 0
        else:
            stats['success_rate'] = 0.0
            stats['failure_rate'] = 0.0
            stats['avg_download_time'] = 0.0
            stats['avg_records_per_download'] = 0.0

        # 缓存效率
        total_cache_requests = stats['cache_hits'] + stats['cache_misses']
        if total_cache_requests > 0:
            stats['cache_hit_rate'] = stats['cache_hits'] / total_cache_requests
        else:
            stats['cache_hit_rate'] = 0.0

        # 系统指标
        stats['uptime_seconds'] = uptime
        stats['downloads_per_second'] = total_downloads / uptime if uptime > 0 else 0
        stats['records_per_second'] = stats['total_records_downloaded'] / uptime if uptime > 0 else 0

        return stats

    def print_performance_report(self):
        """打印性能报告"""
        stats = self.get_performance_stats()

        print("=" * 60)
        print("性能监控报告")
        print("=" * 60)

        print(f"运行时间: {stats['uptime_seconds']:.1f}秒")
        print(f"内存使用: {stats['memory_usage_mb']:.1f}MB")
        print()

        print("下载统计:")
        print(f"  总下载数: {stats['total_downloads']}")
        print(f"  成功数: {stats['successful_downloads']}")
        print(f"  失败数: {stats['failed_downloads']}")
        print(f"  成功率: {stats['success_rate']:.1%}")
        print(f"  总记录数: {stats['total_records_downloaded']}")
        print()

        print("性能指标:")
        print(f"  平均下载时间: {stats['avg_download_time']:.3f}秒")
        print(f"  平均记录数/下载: {stats['avg_records_per_download']:.0f}")
        print(f"  下载速度: {stats['downloads_per_second']:.2f}次/秒")
        print(f"  记录处理速度: {stats['records_per_second']:.0f}条/秒")
        print()

        print("缓存统计:")
        print(f"  缓存命中: {stats['cache_hits']}")
        print(f"  缓存未命中: {stats['cache_misses']}")
        print(f"  缓存命中率: {stats['cache_hit_rate']:.1%}")
        print()

        print("系统操作:")
        print(f"  网络请求: {stats['network_requests']}")
        print(f"  数据库操作: {stats['database_operations']}")
        print("=" * 60)

    def _load_data_from_db(self, stock_code: str, date: str) -> Optional[pd.DataFrame]:
        """从数据库加载数据"""
        try:
            conn = sqlite3.connect(self.db_path)

            query = """
                SELECT time, open, high, low, close, volume, amount
                FROM minute_data
                WHERE stock_code = ? AND date = ?
                ORDER BY time
            """

            df = pd.read_sql_query(query, conn, params=(stock_code, date))
            conn.close()

            if df.empty:
                return None

            # 转换数据类型
            # 将time字段转换为datetime（结合date）
            df['datetime'] = pd.to_datetime(date + ' ' + df['time'])
            for col in ['open', 'high', 'low', 'close', 'volume', 'amount']:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            return df

        except Exception as e:
            self.logger.error(f"从数据库加载数据失败 {stock_code} {date}: {e}")
            return None
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建分时数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS minute_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    date TEXT NOT NULL,
                    time TEXT NOT NULL,
                    open REAL NOT NULL,
                    high REAL NOT NULL,
                    low REAL NOT NULL,
                    close REAL NOT NULL,
                    volume INTEGER NOT NULL,
                    amount REAL NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, date, time)
                )
            """)
            
            # 创建股票信息表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS stock_info (
                    stock_code TEXT PRIMARY KEY,
                    name TEXT,
                    market TEXT DEFAULT 'CHINEXT',
                    status TEXT DEFAULT 'ACTIVE',
                    listing_date TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建下载记录表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS download_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    date TEXT NOT NULL,
                    status TEXT NOT NULL,
                    record_count INTEGER DEFAULT 0,
                    error_message TEXT,
                    download_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, date)
                )
            """)
            
            # 创建索引
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_minute_stock_date 
                ON minute_data(stock_code, date)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_minute_time 
                ON minute_data(date, time)
            """)
            
            conn.commit()
            conn.close()
            
            self.logger.info("数据库初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def get_chinext_stocks(self) -> List[str]:
        """
        获取创业板股票列表
        
        Returns:
            创业板股票代码列表
        """
        try:
            # 获取A股股票列表
            stock_list = ak.stock_info_a_code_name()
            
            if stock_list.empty:
                self.logger.warning("未获取到股票列表")
                return []
            
            # 筛选创业板股票（300开头）
            chinext_stocks = []
            for _, row in stock_list.iterrows():
                code = row['code']
                if code.startswith('300'):
                    chinext_stocks.append(f"{code}.SZ")
            
            self.logger.info(f"获取到创业板股票 {len(chinext_stocks)} 只")
            return chinext_stocks
            
        except Exception as e:
            self.logger.error(f"获取创业板股票列表失败: {e}")
            return []
    
    def get_trading_dates(self, days: int = 30) -> List[str]:
        """
        获取最近N个交易日
        
        Args:
            days: 天数
            
        Returns:
            交易日期列表
        """
        try:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days*2)  # 多取一些天数以确保有足够的交易日
            
            # 获取交易日历
            trade_dates = ak.tool_trade_date_hist_sina()
            
            if trade_dates.empty:
                self.logger.warning("未获取到交易日历")
                return []
            
            # 转换日期格式并筛选
            trade_dates['trade_date'] = pd.to_datetime(trade_dates['trade_date'])
            recent_dates = trade_dates[
                (trade_dates['trade_date'] >= pd.to_datetime(start_date)) &
                (trade_dates['trade_date'] <= pd.to_datetime(end_date))
            ]['trade_date'].dt.strftime('%Y-%m-%d').tolist()
            
            # 取最近的N个交易日
            recent_dates = sorted(recent_dates)[-days:]
            
            self.logger.info(f"获取到最近 {len(recent_dates)} 个交易日")
            return recent_dates
            
        except Exception as e:
            self.logger.error(f"获取交易日期失败: {e}")
            return []
    
    def download_stock_minute_data(self, stock_code: str, date: str) -> Tuple[bool, int, str]:
        """
        下载单只股票的分时数据（带重试机制和缓存）

        Args:
            stock_code: 股票代码
            date: 日期

        Returns:
            (是否成功, 记录数, 错误信息)
        """
        start_time = time.time()
        self._update_performance_stats('download_start')
        # 首先检查内存缓存
        cached_data = self._get_from_cache(stock_code, date)
        if cached_data is not None:
            self._update_performance_stats('cache_hit')
            # 从缓存获取到数据，保存到数据库（如果还没有的话）
            if not self._is_data_exists(stock_code, date):
                saved_count = self._save_minute_data(stock_code, date, cached_data)
                elapsed_time = time.time() - start_time
                self._update_performance_stats('download_success', time=elapsed_time, records=saved_count)
                return True, saved_count, "从缓存恢复数据"
            else:
                elapsed_time = time.time() - start_time
                self._update_performance_stats('download_success', time=elapsed_time, records=len(cached_data))
                return True, len(cached_data), "数据已存在（缓存命中）"

        # 检查数据库中是否已存在
        if self._is_data_exists(stock_code, date):
            self._update_performance_stats('database_operation')
            # 数据库中有数据，加载到缓存中
            db_data = self._load_data_from_db(stock_code, date)
            if db_data is not None and not db_data.empty:
                self._save_to_cache(stock_code, date, db_data)
                elapsed_time = time.time() - start_time
                self._update_performance_stats('download_success', time=elapsed_time, records=len(db_data))
                return True, len(db_data), "数据已存在（已加载到缓存）"

        # 带重试的下载
        for attempt in range(self.max_retries):
            try:
                # 添加随机延迟，避免请求过于集中
                if attempt > 0:
                    delay = self.retry_delay * (self.backoff_factor ** (attempt - 1))
                    jitter = random.uniform(-self.jitter_range, self.jitter_range)
                    time.sleep(delay + jitter)
                    self.logger.info(f"重试下载 {stock_code} {date} (第{attempt+1}次)")

                # 获取分时数据
                symbol = stock_code.replace('.SZ', '')

                # 使用akshare获取分时数据
                self._update_performance_stats('network_request')
                minute_data = ak.stock_zh_a_hist_min_em(
                    symbol=symbol,
                    start_date=f"{date} 09:30:00",
                    end_date=f"{date} 15:00:00",
                    period="1",
                    adjust="qfq"
                )

                if minute_data.empty:
                    error_msg = f"未获取到分时数据: {stock_code} {date}"
                    if attempt == self.max_retries - 1:  # 最后一次尝试
                        self.logger.warning(error_msg)
                        self._log_download_result(stock_code, date, "FAILED", 0, error_msg)
                        self._update_performance_stats('download_failed')
                        return False, 0, error_msg
                    else:
                        continue  # 继续重试

                # 保存数据到数据库
                saved_count = self._save_minute_data(stock_code, date, minute_data)

                # 同时保存到缓存
                self._save_to_cache(stock_code, date, minute_data)

                if saved_count > 0:
                    self._log_download_result(stock_code, date, "SUCCESS", saved_count, "")
                    elapsed_time = time.time() - start_time
                    self._update_performance_stats('download_success', time=elapsed_time, records=saved_count)
                    if attempt > 0:
                        self.logger.info(f"重试成功 {stock_code} {date} (第{attempt+1}次)")
                    return True, saved_count, ""
                else:
                    error_msg = "保存数据失败"
                    if attempt == self.max_retries - 1:  # 最后一次尝试
                        self._log_download_result(stock_code, date, "FAILED", 0, error_msg)
                        self._update_performance_stats('download_failed')
                        return False, 0, error_msg
                    else:
                        continue  # 继续重试

            except Exception as e:
                error_msg = f"下载失败: {str(e)}"
                if attempt == self.max_retries - 1:  # 最后一次尝试
                    self.logger.warning(f"下载分时数据失败 {stock_code} {date}: {error_msg}")
                    self._log_download_result(stock_code, date, "FAILED", 0, error_msg)
                    self._update_performance_stats('download_failed')
                    return False, 0, error_msg
                else:
                    self.logger.debug(f"下载尝试失败 {stock_code} {date} (第{attempt+1}次): {error_msg}")
                    continue  # 继续重试

        # 理论上不会到达这里
        self._update_performance_stats('download_failed')
        return False, 0, "所有重试均失败"
    
    def _is_data_exists(self, stock_code: str, date: str) -> bool:
        """检查数据是否已存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT COUNT(*) FROM minute_data 
                WHERE stock_code = ? AND date = ?
            """, (stock_code, date))
            
            count = cursor.fetchone()[0]
            conn.close()
            
            return count > 0
            
        except Exception:
            return False
    
    def _save_minute_data(self, stock_code: str, date: str, data: pd.DataFrame) -> int:
        """保存分时数据到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            saved_count = 0
            
            # 标准化列名
            column_mapping = {
                '时间': 'datetime',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount'
            }
            
            data = data.rename(columns=column_mapping)
            
            # 插入数据
            for idx, row in data.iterrows():
                try:
                    # 解析时间
                    if 'datetime' in row:
                        dt = pd.to_datetime(row['datetime'])
                        time_str = dt.strftime('%H:%M:%S')
                    else:
                        time_str = pd.to_datetime(idx).strftime('%H:%M:%S')
                    
                    cursor.execute("""
                        INSERT OR REPLACE INTO minute_data 
                        (stock_code, date, time, open, high, low, close, volume, amount)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        stock_code,
                        date,
                        time_str,
                        float(row.get('open', 0)),
                        float(row.get('high', 0)),
                        float(row.get('low', 0)),
                        float(row.get('close', 0)),
                        int(row.get('volume', 0)),
                        float(row.get('amount', 0))
                    ))
                    
                    saved_count += 1
                    
                except Exception as e:
                    self.logger.debug(f"保存单条记录失败: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            return saved_count
            
        except Exception as e:
            self.logger.error(f"保存分时数据失败: {e}")
            return 0
    
    def _log_download_result(self, stock_code: str, date: str, status: str, 
                           record_count: int, error_message: str):
        """记录下载结果"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO download_log
                (stock_code, date, status, record_count, error_message)
                VALUES (?, ?, ?, ?, ?)
            """, (stock_code, date, status, record_count, error_message))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.debug(f"记录下载日志失败: {e}")

    def batch_download_minute_data(self, stock_codes: List[str], dates: List[str]) -> Dict[str, int]:
        """
        批量下载分时数据

        Args:
            stock_codes: 股票代码列表
            dates: 日期列表

        Returns:
            下载统计结果
        """
        total_tasks = len(stock_codes) * len(dates)
        success_count = 0
        failed_count = 0

        self.logger.info(f"开始批量下载分时数据，任务数: {total_tasks}")

        # 创建任务列表
        tasks = []
        for stock_code in stock_codes:
            for date in dates:
                tasks.append((stock_code, date))

        # 使用线程池并行下载
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_task = {
                executor.submit(self._download_with_retry, stock_code, date): (stock_code, date)
                for stock_code, date in tasks
            }

            # 显示进度条
            with tqdm(total=total_tasks, desc="下载分时数据") as pbar:
                for future in as_completed(future_to_task):
                    stock_code, date = future_to_task[future]
                    try:
                        success, count, error = future.result()
                        if success:
                            success_count += 1
                        else:
                            failed_count += 1

                        pbar.set_postfix({
                            'Success': success_count,
                            'Failed': failed_count
                        })

                    except Exception as e:
                        self.logger.error(f"任务执行异常 {stock_code} {date}: {e}")
                        failed_count += 1

                    pbar.update(1)

                    # 添加延迟避免请求过快
                    time.sleep(self.request_delay)

        result = {
            'total': total_tasks,
            'success': success_count,
            'failed': failed_count,
            'success_rate': success_count / total_tasks if total_tasks > 0 else 0
        }

        self.logger.info(f"批量下载完成 - 总计: {total_tasks}, 成功: {success_count}, 失败: {failed_count}")
        return result

    def _download_with_retry(self, stock_code: str, date: str) -> Tuple[bool, int, str]:
        """带重试的下载"""
        for attempt in range(self.retry_times):
            try:
                success, count, error = self.download_stock_minute_data(stock_code, date)
                if success:
                    return success, count, error

                # 如果失败且不是最后一次尝试，等待后重试
                if attempt < self.retry_times - 1:
                    time.sleep(1 * (attempt + 1))  # 递增等待时间

            except Exception as e:
                error = f"重试 {attempt + 1} 失败: {str(e)}"
                if attempt == self.retry_times - 1:
                    return False, 0, error

        return False, 0, "重试次数用尽"

    def download_all_chinext_data(self, days: int = 30) -> Dict[str, int]:
        """
        下载所有创业板股票的分时数据

        Args:
            days: 最近天数

        Returns:
            下载统计结果
        """
        self.logger.info(f"开始下载所有创业板股票最近 {days} 天的分时数据")

        # 获取创业板股票列表
        stock_codes = self.get_chinext_stocks()
        if not stock_codes:
            self.logger.error("未获取到创业板股票列表")
            return {'total': 0, 'success': 0, 'failed': 0, 'success_rate': 0}

        # 获取交易日期
        dates = self.get_trading_dates(days)
        if not dates:
            self.logger.error("未获取到交易日期")
            return {'total': 0, 'success': 0, 'failed': 0, 'success_rate': 0}

        # 批量下载
        return self.batch_download_minute_data(stock_codes, dates)

    def get_download_statistics(self) -> Dict[str, any]:
        """获取下载统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 总体统计
            cursor.execute("""
                SELECT
                    COUNT(*) as total_downloads,
                    SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
                    SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_count,
                    SUM(record_count) as total_records
                FROM download_log
            """)

            total_stats = cursor.fetchone()

            # 按股票统计
            cursor.execute("""
                SELECT stock_code, COUNT(*) as download_count,
                       SUM(record_count) as total_records
                FROM download_log
                WHERE status = 'SUCCESS'
                GROUP BY stock_code
                ORDER BY total_records DESC
                LIMIT 10
            """)

            top_stocks = cursor.fetchall()

            # 按日期统计
            cursor.execute("""
                SELECT date, COUNT(*) as stock_count,
                       SUM(record_count) as total_records
                FROM download_log
                WHERE status = 'SUCCESS'
                GROUP BY date
                ORDER BY date DESC
                LIMIT 10
            """)

            recent_dates = cursor.fetchall()

            conn.close()

            return {
                'total_downloads': total_stats[0],
                'success_count': total_stats[1],
                'failed_count': total_stats[2],
                'total_records': total_stats[3],
                'success_rate': total_stats[1] / total_stats[0] if total_stats[0] > 0 else 0,
                'top_stocks': [{'code': row[0], 'downloads': row[1], 'records': row[2]} for row in top_stocks],
                'recent_dates': [{'date': row[0], 'stocks': row[1], 'records': row[2]} for row in recent_dates]
            }

        except Exception as e:
            self.logger.error(f"获取下载统计失败: {e}")
            return {}

    def cleanup_old_data(self, days: int = 90):
        """清理旧数据"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 删除旧的分时数据
            cursor.execute("DELETE FROM minute_data WHERE date < ?", (cutoff_date,))
            deleted_minute = cursor.rowcount

            # 删除旧的下载日志
            cursor.execute("DELETE FROM download_log WHERE date < ?", (cutoff_date,))
            deleted_log = cursor.rowcount

            conn.commit()
            conn.close()

            self.logger.info(f"清理完成 - 删除分时数据: {deleted_minute} 条, 删除日志: {deleted_log} 条")

        except Exception as e:
            self.logger.error(f"清理旧数据失败: {e}")


def main():
    """主函数 - 演示用法"""
    try:
        # 创建下载器
        downloader = ChinextMinuteDataDownloader()

        print("=" * 60)
        print("创业板分时数据批量下载系统")
        print("=" * 60)

        # 下载最近5天的数据（测试用）
        result = downloader.download_all_chinext_data(days=5)

        print(f"\n下载结果:")
        print(f"总任务数: {result['total']}")
        print(f"成功: {result['success']}")
        print(f"失败: {result['failed']}")
        print(f"成功率: {result['success_rate']:.2%}")

        # 显示统计信息
        stats = downloader.get_download_statistics()
        if stats:
            print(f"\n数据库统计:")
            print(f"总下载次数: {stats['total_downloads']}")
            print(f"总记录数: {stats['total_records']}")
            print(f"整体成功率: {stats['success_rate']:.2%}")

    except Exception as e:
        print(f"执行失败: {e}")


if __name__ == "__main__":
    main()

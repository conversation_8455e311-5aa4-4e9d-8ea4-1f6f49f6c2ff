#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创业板股票分时数据批量下载系统
实现1分钟级别分时数据的批量下载、存储和管理功能
"""

import os
import sys
import sqlite3
import pandas as pd
import akshare as ak
import logging
from datetime import datetime, timedelta, date
from typing import List, Dict, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import time
import json

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

class ChinextMinuteDataDownloader:
    """创业板分时数据下载器"""
    
    def __init__(self, db_path: str = "data/chinext_minute_data.db"):
        """
        初始化下载器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.logger = self._setup_logger()
        self._init_database()
        
        # 下载配置
        self.max_workers = 5  # 最大并发数
        self.request_delay = 0.5  # 请求间隔（秒）
        self.retry_times = 3  # 重试次数
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(f"{__name__}.ChinextMinuteDataDownloader")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建分时数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS minute_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    date TEXT NOT NULL,
                    time TEXT NOT NULL,
                    open REAL NOT NULL,
                    high REAL NOT NULL,
                    low REAL NOT NULL,
                    close REAL NOT NULL,
                    volume INTEGER NOT NULL,
                    amount REAL NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, date, time)
                )
            """)
            
            # 创建股票信息表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS stock_info (
                    stock_code TEXT PRIMARY KEY,
                    name TEXT,
                    market TEXT DEFAULT 'CHINEXT',
                    status TEXT DEFAULT 'ACTIVE',
                    listing_date TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建下载记录表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS download_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    date TEXT NOT NULL,
                    status TEXT NOT NULL,
                    record_count INTEGER DEFAULT 0,
                    error_message TEXT,
                    download_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, date)
                )
            """)
            
            # 创建索引
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_minute_stock_date 
                ON minute_data(stock_code, date)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_minute_time 
                ON minute_data(date, time)
            """)
            
            conn.commit()
            conn.close()
            
            self.logger.info("数据库初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def get_chinext_stocks(self) -> List[str]:
        """
        获取创业板股票列表
        
        Returns:
            创业板股票代码列表
        """
        try:
            # 获取A股股票列表
            stock_list = ak.stock_info_a_code_name()
            
            if stock_list.empty:
                self.logger.warning("未获取到股票列表")
                return []
            
            # 筛选创业板股票（300开头）
            chinext_stocks = []
            for _, row in stock_list.iterrows():
                code = row['code']
                if code.startswith('300'):
                    chinext_stocks.append(f"{code}.SZ")
            
            self.logger.info(f"获取到创业板股票 {len(chinext_stocks)} 只")
            return chinext_stocks
            
        except Exception as e:
            self.logger.error(f"获取创业板股票列表失败: {e}")
            return []
    
    def get_trading_dates(self, days: int = 30) -> List[str]:
        """
        获取最近N个交易日
        
        Args:
            days: 天数
            
        Returns:
            交易日期列表
        """
        try:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days*2)  # 多取一些天数以确保有足够的交易日
            
            # 获取交易日历
            trade_dates = ak.tool_trade_date_hist_sina()
            
            if trade_dates.empty:
                self.logger.warning("未获取到交易日历")
                return []
            
            # 转换日期格式并筛选
            trade_dates['trade_date'] = pd.to_datetime(trade_dates['trade_date'])
            recent_dates = trade_dates[
                (trade_dates['trade_date'] >= pd.to_datetime(start_date)) &
                (trade_dates['trade_date'] <= pd.to_datetime(end_date))
            ]['trade_date'].dt.strftime('%Y-%m-%d').tolist()
            
            # 取最近的N个交易日
            recent_dates = sorted(recent_dates)[-days:]
            
            self.logger.info(f"获取到最近 {len(recent_dates)} 个交易日")
            return recent_dates
            
        except Exception as e:
            self.logger.error(f"获取交易日期失败: {e}")
            return []
    
    def download_stock_minute_data(self, stock_code: str, date: str) -> Tuple[bool, int, str]:
        """
        下载单只股票的分时数据
        
        Args:
            stock_code: 股票代码
            date: 日期
            
        Returns:
            (是否成功, 记录数, 错误信息)
        """
        try:
            # 检查是否已下载
            if self._is_data_exists(stock_code, date):
                return True, 0, "数据已存在"
            
            # 获取分时数据
            symbol = stock_code.replace('.SZ', '')
            
            # 使用akshare获取分时数据
            minute_data = ak.stock_zh_a_hist_min_em(
                symbol=symbol,
                start_date=f"{date} 09:30:00",
                end_date=f"{date} 15:00:00",
                period="1",
                adjust="qfq"
            )
            
            if minute_data.empty:
                error_msg = f"未获取到分时数据: {stock_code} {date}"
                self.logger.warning(error_msg)
                self._log_download_result(stock_code, date, "FAILED", 0, error_msg)
                return False, 0, error_msg
            
            # 保存数据
            saved_count = self._save_minute_data(stock_code, date, minute_data)
            
            if saved_count > 0:
                self._log_download_result(stock_code, date, "SUCCESS", saved_count, "")
                return True, saved_count, ""
            else:
                error_msg = "保存数据失败"
                self._log_download_result(stock_code, date, "FAILED", 0, error_msg)
                return False, 0, error_msg
                
        except Exception as e:
            error_msg = f"下载失败: {str(e)}"
            self.logger.warning(f"下载分时数据失败 {stock_code} {date}: {error_msg}")
            self._log_download_result(stock_code, date, "FAILED", 0, error_msg)
            return False, 0, error_msg
    
    def _is_data_exists(self, stock_code: str, date: str) -> bool:
        """检查数据是否已存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT COUNT(*) FROM minute_data 
                WHERE stock_code = ? AND date = ?
            """, (stock_code, date))
            
            count = cursor.fetchone()[0]
            conn.close()
            
            return count > 0
            
        except Exception:
            return False
    
    def _save_minute_data(self, stock_code: str, date: str, data: pd.DataFrame) -> int:
        """保存分时数据到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            saved_count = 0
            
            # 标准化列名
            column_mapping = {
                '时间': 'datetime',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount'
            }
            
            data = data.rename(columns=column_mapping)
            
            # 插入数据
            for idx, row in data.iterrows():
                try:
                    # 解析时间
                    if 'datetime' in row:
                        dt = pd.to_datetime(row['datetime'])
                        time_str = dt.strftime('%H:%M:%S')
                    else:
                        time_str = pd.to_datetime(idx).strftime('%H:%M:%S')
                    
                    cursor.execute("""
                        INSERT OR REPLACE INTO minute_data 
                        (stock_code, date, time, open, high, low, close, volume, amount)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        stock_code,
                        date,
                        time_str,
                        float(row.get('open', 0)),
                        float(row.get('high', 0)),
                        float(row.get('low', 0)),
                        float(row.get('close', 0)),
                        int(row.get('volume', 0)),
                        float(row.get('amount', 0))
                    ))
                    
                    saved_count += 1
                    
                except Exception as e:
                    self.logger.debug(f"保存单条记录失败: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            return saved_count
            
        except Exception as e:
            self.logger.error(f"保存分时数据失败: {e}")
            return 0
    
    def _log_download_result(self, stock_code: str, date: str, status: str, 
                           record_count: int, error_message: str):
        """记录下载结果"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO download_log
                (stock_code, date, status, record_count, error_message)
                VALUES (?, ?, ?, ?, ?)
            """, (stock_code, date, status, record_count, error_message))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.debug(f"记录下载日志失败: {e}")

    def batch_download_minute_data(self, stock_codes: List[str], dates: List[str]) -> Dict[str, int]:
        """
        批量下载分时数据

        Args:
            stock_codes: 股票代码列表
            dates: 日期列表

        Returns:
            下载统计结果
        """
        total_tasks = len(stock_codes) * len(dates)
        success_count = 0
        failed_count = 0

        self.logger.info(f"开始批量下载分时数据，任务数: {total_tasks}")

        # 创建任务列表
        tasks = []
        for stock_code in stock_codes:
            for date in dates:
                tasks.append((stock_code, date))

        # 使用线程池并行下载
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_task = {
                executor.submit(self._download_with_retry, stock_code, date): (stock_code, date)
                for stock_code, date in tasks
            }

            # 显示进度条
            with tqdm(total=total_tasks, desc="下载分时数据") as pbar:
                for future in as_completed(future_to_task):
                    stock_code, date = future_to_task[future]
                    try:
                        success, count, error = future.result()
                        if success:
                            success_count += 1
                        else:
                            failed_count += 1

                        pbar.set_postfix({
                            'Success': success_count,
                            'Failed': failed_count
                        })

                    except Exception as e:
                        self.logger.error(f"任务执行异常 {stock_code} {date}: {e}")
                        failed_count += 1

                    pbar.update(1)

                    # 添加延迟避免请求过快
                    time.sleep(self.request_delay)

        result = {
            'total': total_tasks,
            'success': success_count,
            'failed': failed_count,
            'success_rate': success_count / total_tasks if total_tasks > 0 else 0
        }

        self.logger.info(f"批量下载完成 - 总计: {total_tasks}, 成功: {success_count}, 失败: {failed_count}")
        return result

    def _download_with_retry(self, stock_code: str, date: str) -> Tuple[bool, int, str]:
        """带重试的下载"""
        for attempt in range(self.retry_times):
            try:
                success, count, error = self.download_stock_minute_data(stock_code, date)
                if success:
                    return success, count, error

                # 如果失败且不是最后一次尝试，等待后重试
                if attempt < self.retry_times - 1:
                    time.sleep(1 * (attempt + 1))  # 递增等待时间

            except Exception as e:
                error = f"重试 {attempt + 1} 失败: {str(e)}"
                if attempt == self.retry_times - 1:
                    return False, 0, error

        return False, 0, "重试次数用尽"

    def download_all_chinext_data(self, days: int = 30) -> Dict[str, int]:
        """
        下载所有创业板股票的分时数据

        Args:
            days: 最近天数

        Returns:
            下载统计结果
        """
        self.logger.info(f"开始下载所有创业板股票最近 {days} 天的分时数据")

        # 获取创业板股票列表
        stock_codes = self.get_chinext_stocks()
        if not stock_codes:
            self.logger.error("未获取到创业板股票列表")
            return {'total': 0, 'success': 0, 'failed': 0, 'success_rate': 0}

        # 获取交易日期
        dates = self.get_trading_dates(days)
        if not dates:
            self.logger.error("未获取到交易日期")
            return {'total': 0, 'success': 0, 'failed': 0, 'success_rate': 0}

        # 批量下载
        return self.batch_download_minute_data(stock_codes, dates)

    def get_download_statistics(self) -> Dict[str, any]:
        """获取下载统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 总体统计
            cursor.execute("""
                SELECT
                    COUNT(*) as total_downloads,
                    SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
                    SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_count,
                    SUM(record_count) as total_records
                FROM download_log
            """)

            total_stats = cursor.fetchone()

            # 按股票统计
            cursor.execute("""
                SELECT stock_code, COUNT(*) as download_count,
                       SUM(record_count) as total_records
                FROM download_log
                WHERE status = 'SUCCESS'
                GROUP BY stock_code
                ORDER BY total_records DESC
                LIMIT 10
            """)

            top_stocks = cursor.fetchall()

            # 按日期统计
            cursor.execute("""
                SELECT date, COUNT(*) as stock_count,
                       SUM(record_count) as total_records
                FROM download_log
                WHERE status = 'SUCCESS'
                GROUP BY date
                ORDER BY date DESC
                LIMIT 10
            """)

            recent_dates = cursor.fetchall()

            conn.close()

            return {
                'total_downloads': total_stats[0],
                'success_count': total_stats[1],
                'failed_count': total_stats[2],
                'total_records': total_stats[3],
                'success_rate': total_stats[1] / total_stats[0] if total_stats[0] > 0 else 0,
                'top_stocks': [{'code': row[0], 'downloads': row[1], 'records': row[2]} for row in top_stocks],
                'recent_dates': [{'date': row[0], 'stocks': row[1], 'records': row[2]} for row in recent_dates]
            }

        except Exception as e:
            self.logger.error(f"获取下载统计失败: {e}")
            return {}

    def cleanup_old_data(self, days: int = 90):
        """清理旧数据"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 删除旧的分时数据
            cursor.execute("DELETE FROM minute_data WHERE date < ?", (cutoff_date,))
            deleted_minute = cursor.rowcount

            # 删除旧的下载日志
            cursor.execute("DELETE FROM download_log WHERE date < ?", (cutoff_date,))
            deleted_log = cursor.rowcount

            conn.commit()
            conn.close()

            self.logger.info(f"清理完成 - 删除分时数据: {deleted_minute} 条, 删除日志: {deleted_log} 条")

        except Exception as e:
            self.logger.error(f"清理旧数据失败: {e}")


def main():
    """主函数 - 演示用法"""
    try:
        # 创建下载器
        downloader = ChinextMinuteDataDownloader()

        print("=" * 60)
        print("创业板分时数据批量下载系统")
        print("=" * 60)

        # 下载最近5天的数据（测试用）
        result = downloader.download_all_chinext_data(days=5)

        print(f"\n下载结果:")
        print(f"总任务数: {result['total']}")
        print(f"成功: {result['success']}")
        print(f"失败: {result['failed']}")
        print(f"成功率: {result['success_rate']:.2%}")

        # 显示统计信息
        stats = downloader.get_download_statistics()
        if stats:
            print(f"\n数据库统计:")
            print(f"总下载次数: {stats['total_downloads']}")
            print(f"总记录数: {stats['total_records']}")
            print(f"整体成功率: {stats['success_rate']:.2%}")

    except Exception as e:
        print(f"执行失败: {e}")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略基类

定义交易策略的通用功能和接口实现。
"""

import json
from abc import ABC
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date
import pandas as pd

from quantization.core.interfaces.data_interface import StrategyInterface
from quantization.utils.logger import get_logger


class BaseStrategy(StrategyInterface, ABC):
    """
    交易策略基类
    
    提供策略的通用功能，包括：
    - 参数管理
    - 日志记录
    - 性能统计
    - 配置验证
    """
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """
        初始化策略
        
        Args:
            name: 策略名称
            config: 策略配置参数
        """
        self.name = name
        self.config = config or {}
        self.logger = get_logger(f"Strategy.{self.name}")
        
        # 策略状态
        self.is_initialized = False
        self.last_update_time = None
        
        # 性能统计
        self.stats = {
            'total_selections': 0,
            'total_signals': 0,
            'successful_selections': 0,
            'failed_selections': 0,
            'last_selection_time': None,
            'last_signal_time': None
        }
        
        # 默认参数
        self.default_params = self._get_default_parameters()
        
        # 合并配置参数
        self.params = {**self.default_params, **self.config}
        
        self.logger.info(f"初始化策略: {self.name}")
    
    def _get_default_parameters(self) -> Dict[str, Any]:
        """
        获取默认参数
        
        子类应重写此方法以提供特定的默认参数
        
        Returns:
            默认参数字典
        """
        return {
            'max_stocks': 10,
            'min_market_cap': 15,  # 亿元
            'max_market_cap': 300,  # 亿元
            'min_volume_ratio': 1.5,
            'williams_r_threshold': 80,
            'big_order_threshold': 0.3
        }
    
    def initialize(self, config: Dict[str, Any]) -> None:
        """
        初始化策略
        
        Args:
            config: 策略配置参数
        """
        try:
            # 更新配置
            self.config.update(config)
            self.params.update(config)
            
            # 验证参数
            self._validate_parameters()
            
            # 执行子类特定的初始化
            self._initialize_strategy()
            
            self.is_initialized = True
            self.last_update_time = datetime.now()
            
            self.logger.info(f"策略初始化成功: {self.name}")
            self.logger.debug(f"策略参数: {json.dumps(self.params, indent=2, ensure_ascii=False)}")
            
        except Exception as e:
            self.logger.error(f"策略初始化失败: {str(e)}")
            raise
    
    def _initialize_strategy(self) -> None:
        """
        子类特定的初始化逻辑
        
        子类可重写此方法以实现特定的初始化逻辑
        """
        pass
    
    def _validate_parameters(self) -> None:
        """
        验证策略参数
        
        子类可重写此方法以实现特定的参数验证
        """
        required_params = ['max_stocks']
        
        for param in required_params:
            if param not in self.params:
                raise ValueError(f"缺少必要参数: {param}")
        
        # 基本数值验证
        if self.params.get('max_stocks', 0) <= 0:
            raise ValueError("max_stocks 必须大于0")
        
        if self.params.get('min_market_cap', 0) < 0:
            raise ValueError("min_market_cap 不能为负数")
        
        if self.params.get('max_market_cap', 0) <= self.params.get('min_market_cap', 0):
            raise ValueError("max_market_cap 必须大于 min_market_cap")
    
    def select_stocks(self, date: Union[str, date, datetime]) -> List[str]:
        """
        选股逻辑
        
        Args:
            date: 选股日期
            
        Returns:
            选中的股票代码列表
        """
        if not self.is_initialized:
            raise RuntimeError("策略未初始化，请先调用 initialize() 方法")
        
        try:
            self.stats['total_selections'] += 1
            self.stats['last_selection_time'] = datetime.now()
            
            # 执行选股逻辑
            selected_stocks = self._execute_selection(date)
            
            # 限制选股数量
            max_stocks = self.params.get('max_stocks', 10)
            if len(selected_stocks) > max_stocks:
                selected_stocks = selected_stocks[:max_stocks]
            
            self.stats['successful_selections'] += 1
            
            self.logger.info(
                f"选股完成: 日期={date}, "
                f"选中股票数={len(selected_stocks)}, "
                f"股票列表={selected_stocks}"
            )
            
            return selected_stocks
            
        except Exception as e:
            self.stats['failed_selections'] += 1
            self.logger.error(f"选股失败: 日期={date}, 错误={str(e)}")
            raise
    
    def _execute_selection(self, date: Union[str, date, datetime]) -> List[str]:
        """
        执行选股逻辑
        
        子类必须实现此方法
        
        Args:
            date: 选股日期
            
        Returns:
            选中的股票代码列表
        """
        raise NotImplementedError("子类必须实现 _execute_selection 方法")
    
    def generate_signals(
        self, 
        stock_code: str, 
        data: pd.DataFrame
    ) -> Dict[str, Any]:
        """
        生成交易信号
        
        Args:
            stock_code: 股票代码
            data: 股票数据
            
        Returns:
            交易信号字典
        """
        if not self.is_initialized:
            raise RuntimeError("策略未初始化，请先调用 initialize() 方法")
        
        try:
            self.stats['total_signals'] += 1
            self.stats['last_signal_time'] = datetime.now()
            
            # 执行信号生成逻辑
            signals = self._generate_signals(stock_code, data)
            
            self.logger.debug(f"生成交易信号: 股票={stock_code}, 信号={signals}")
            
            return signals
            
        except Exception as e:
            self.logger.error(f"信号生成失败: 股票={stock_code}, 错误={str(e)}")
            raise
    
    def _generate_signals(
        self, 
        stock_code: str, 
        data: pd.DataFrame
    ) -> Dict[str, Any]:
        """
        执行信号生成逻辑
        
        子类可重写此方法以实现特定的信号生成逻辑
        
        Args:
            stock_code: 股票代码
            data: 股票数据
            
        Returns:
            交易信号字典
        """
        return {
            'action': 'hold',
            'confidence': 0.5,
            'timestamp': datetime.now(),
            'stock_code': stock_code
        }
    
    def get_strategy_name(self) -> str:
        """
        获取策略名称
        
        Returns:
            策略名称
        """
        return self.name
    
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取策略参数
        
        Returns:
            策略参数字典
        """
        return self.params.copy()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取策略统计信息
        
        Returns:
            统计信息字典
        """
        stats = self.stats.copy()
        
        # 计算成功率
        if stats['total_selections'] > 0:
            stats['selection_success_rate'] = (
                stats['successful_selections'] / stats['total_selections']
            )
        else:
            stats['selection_success_rate'] = 0.0
        
        return stats
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.stats = {
            'total_selections': 0,
            'total_signals': 0,
            'successful_selections': 0,
            'failed_selections': 0,
            'last_selection_time': None,
            'last_signal_time': None
        }
        self.logger.info(f"已重置策略 {self.name} 的统计信息")
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            策略信息字典
        """
        return {
            'name': self.name,
            'class': self.__class__.__name__,
            'is_initialized': self.is_initialized,
            'last_update_time': self.last_update_time,
            'parameters': self.get_parameters(),
            'stats': self.get_stats()
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}(name='{self.name}')"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (
            f"{self.__class__.__name__}("
            f"name='{self.name}', "
            f"initialized={self.is_initialized}"
            f")"
        )

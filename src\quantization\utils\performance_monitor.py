#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控模块

提供系统性能监控，包括API响应时间、数据库查询性能、内存使用监控和告警机制。
"""

import time
import threading
import psutil
import functools
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, asdict
from contextlib import contextmanager
from collections import defaultdict, deque
from enum import Enum
import statistics
import json

from quantization.utils.logger import get_logger


class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"          # 计数器
    GAUGE = "gauge"             # 仪表盘
    HISTOGRAM = "histogram"     # 直方图
    TIMER = "timer"             # 计时器


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class MetricData:
    """指标数据"""
    name: str
    value: float
    timestamp: float
    metric_type: MetricType
    tags: Dict[str, str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = {}


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    metric_name: str
    condition: str  # >, <, >=, <=, ==, !=
    threshold: float
    level: AlertLevel
    duration: float = 0  # 持续时间（秒）
    enabled: bool = True


@dataclass
class Alert:
    """告警信息"""
    rule_name: str
    metric_name: str
    current_value: float
    threshold: float
    level: AlertLevel
    timestamp: float
    message: str


class MetricCollector:
    """指标收集器"""
    
    def __init__(self, max_history: int = 10000):
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        self.lock = threading.RLock()
        self.logger = get_logger(self.__class__.__name__)
    
    def record_metric(self, metric: MetricData):
        """记录指标"""
        with self.lock:
            self.metrics[metric.name].append(metric)
    
    def get_metric_history(self, name: str, limit: int = 100) -> List[MetricData]:
        """获取指标历史"""
        with self.lock:
            history = list(self.metrics[name])
            return history[-limit:] if limit > 0 else history
    
    def get_metric_stats(self, name: str, duration: float = 3600) -> Dict[str, Any]:
        """获取指标统计信息"""
        current_time = time.time()
        cutoff_time = current_time - duration
        
        with self.lock:
            recent_metrics = [
                m for m in self.metrics[name]
                if m.timestamp >= cutoff_time
            ]
        
        if not recent_metrics:
            return {'count': 0}
        
        values = [m.value for m in recent_metrics]
        
        return {
            'count': len(values),
            'min': min(values),
            'max': max(values),
            'mean': statistics.mean(values),
            'median': statistics.median(values),
            'std_dev': statistics.stdev(values) if len(values) > 1 else 0,
            'p95': statistics.quantiles(values, n=20)[18] if len(values) >= 20 else max(values),
            'p99': statistics.quantiles(values, n=100)[98] if len(values) >= 100 else max(values)
        }
    
    def clear_metrics(self, name: str = None):
        """清空指标数据"""
        with self.lock:
            if name:
                if name in self.metrics:
                    self.metrics[name].clear()
            else:
                self.metrics.clear()


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, collector: MetricCollector):
        self.collector = collector
        self.logger = get_logger(self.__class__.__name__)
        self._monitoring = False
        self._monitor_thread = None
        self._monitor_interval = 5.0  # 5秒
    
    def start_monitoring(self, interval: float = 5.0):
        """开始系统监控"""
        if self._monitoring:
            return
        
        self._monitor_interval = interval
        self._monitoring = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        
        self.logger.info(f"系统监控已启动，监控间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止系统监控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=10)
        
        self.logger.info("系统监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self._monitoring:
            try:
                self._collect_system_metrics()
                time.sleep(self._monitor_interval)
            except Exception as e:
                self.logger.error(f"系统监控异常: {str(e)}")
                time.sleep(self._monitor_interval)
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        current_time = time.time()
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=None)
        self.collector.record_metric(MetricData(
            name="system.cpu.usage_percent",
            value=cpu_percent,
            timestamp=current_time,
            metric_type=MetricType.GAUGE
        ))
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        self.collector.record_metric(MetricData(
            name="system.memory.usage_percent",
            value=memory.percent,
            timestamp=current_time,
            metric_type=MetricType.GAUGE
        ))
        
        self.collector.record_metric(MetricData(
            name="system.memory.available_mb",
            value=memory.available / (1024 * 1024),
            timestamp=current_time,
            metric_type=MetricType.GAUGE
        ))
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        self.collector.record_metric(MetricData(
            name="system.disk.usage_percent",
            value=(disk.used / disk.total) * 100,
            timestamp=current_time,
            metric_type=MetricType.GAUGE
        ))
        
        # 网络IO
        net_io = psutil.net_io_counters()
        self.collector.record_metric(MetricData(
            name="system.network.bytes_sent",
            value=net_io.bytes_sent,
            timestamp=current_time,
            metric_type=MetricType.COUNTER
        ))
        
        self.collector.record_metric(MetricData(
            name="system.network.bytes_recv",
            value=net_io.bytes_recv,
            timestamp=current_time,
            metric_type=MetricType.COUNTER
        ))


class PerformanceTimer:
    """性能计时器"""
    
    def __init__(self, collector: MetricCollector):
        self.collector = collector
        self.logger = get_logger(self.__class__.__name__)
    
    @contextmanager
    def time_operation(self, operation_name: str, tags: Dict[str, str] = None):
        """计时操作上下文管理器"""
        start_time = time.time()
        
        try:
            yield
        finally:
            end_time = time.time()
            duration = end_time - start_time
            
            self.collector.record_metric(MetricData(
                name=f"operation.{operation_name}.duration_seconds",
                value=duration,
                timestamp=end_time,
                metric_type=MetricType.TIMER,
                tags=tags or {}
            ))
    
    def time_function(self, operation_name: str = None, tags: Dict[str, str] = None):
        """函数计时装饰器"""
        def decorator(func: Callable) -> Callable:
            name = operation_name or f"{func.__module__}.{func.__name__}"
            
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                with self.time_operation(name, tags):
                    return func(*args, **kwargs)
            
            return wrapper
        return decorator


class DatabaseMonitor:
    """数据库监控器"""
    
    def __init__(self, collector: MetricCollector):
        self.collector = collector
        self.logger = get_logger(self.__class__.__name__)
    
    def record_query_performance(
        self, 
        query_type: str, 
        duration: float, 
        rows_affected: int = 0,
        success: bool = True
    ):
        """记录查询性能"""
        current_time = time.time()
        
        # 查询耗时
        self.collector.record_metric(MetricData(
            name=f"database.query.{query_type}.duration_seconds",
            value=duration,
            timestamp=current_time,
            metric_type=MetricType.TIMER,
            tags={'query_type': query_type}
        ))
        
        # 影响行数
        if rows_affected > 0:
            self.collector.record_metric(MetricData(
                name=f"database.query.{query_type}.rows_affected",
                value=rows_affected,
                timestamp=current_time,
                metric_type=MetricType.GAUGE,
                tags={'query_type': query_type}
            ))
        
        # 成功/失败计数
        status = "success" if success else "error"
        self.collector.record_metric(MetricData(
            name=f"database.query.{query_type}.{status}_count",
            value=1,
            timestamp=current_time,
            metric_type=MetricType.COUNTER,
            tags={'query_type': query_type, 'status': status}
        ))
    
    @contextmanager
    def monitor_query(self, query_type: str):
        """查询监控上下文管理器"""
        start_time = time.time()
        success = True
        
        try:
            yield
        except Exception as e:
            success = False
            self.logger.error(f"数据库查询失败 [{query_type}]: {str(e)}")
            raise
        finally:
            duration = time.time() - start_time
            self.record_query_performance(query_type, duration, success=success)


class APIMonitor:
    """API监控器"""
    
    def __init__(self, collector: MetricCollector):
        self.collector = collector
        self.logger = get_logger(self.__class__.__name__)
    
    def record_api_call(
        self, 
        endpoint: str, 
        method: str, 
        status_code: int, 
        duration: float,
        request_size: int = 0,
        response_size: int = 0
    ):
        """记录API调用"""
        current_time = time.time()
        
        # API响应时间
        self.collector.record_metric(MetricData(
            name=f"api.{endpoint}.duration_seconds",
            value=duration,
            timestamp=current_time,
            metric_type=MetricType.TIMER,
            tags={'endpoint': endpoint, 'method': method}
        ))
        
        # 状态码计数
        self.collector.record_metric(MetricData(
            name=f"api.{endpoint}.status_{status_code}_count",
            value=1,
            timestamp=current_time,
            metric_type=MetricType.COUNTER,
            tags={'endpoint': endpoint, 'method': method, 'status_code': str(status_code)}
        ))
        
        # 请求/响应大小
        if request_size > 0:
            self.collector.record_metric(MetricData(
                name=f"api.{endpoint}.request_size_bytes",
                value=request_size,
                timestamp=current_time,
                metric_type=MetricType.GAUGE,
                tags={'endpoint': endpoint, 'method': method}
            ))
        
        if response_size > 0:
            self.collector.record_metric(MetricData(
                name=f"api.{endpoint}.response_size_bytes",
                value=response_size,
                timestamp=current_time,
                metric_type=MetricType.GAUGE,
                tags={'endpoint': endpoint, 'method': method}
            ))
    
    @contextmanager
    def monitor_api_call(self, endpoint: str, method: str = "GET"):
        """API调用监控上下文管理器"""
        start_time = time.time()
        status_code = 200
        
        try:
            yield
        except Exception as e:
            status_code = 500
            self.logger.error(f"API调用失败 [{method} {endpoint}]: {str(e)}")
            raise
        finally:
            duration = time.time() - start_time
            self.record_api_call(endpoint, method, status_code, duration)


class AlertManager:
    """告警管理器"""

    def __init__(self, collector: MetricCollector):
        self.collector = collector
        self.logger = get_logger(self.__class__.__name__)
        self.rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.lock = threading.RLock()

        # 告警检查线程
        self._checking = False
        self._check_thread = None
        self._check_interval = 30.0  # 30秒

    def add_rule(self, rule: AlertRule):
        """添加告警规则"""
        with self.lock:
            self.rules[rule.name] = rule

        self.logger.info(f"添加告警规则: {rule.name}")

    def remove_rule(self, rule_name: str):
        """移除告警规则"""
        with self.lock:
            if rule_name in self.rules:
                del self.rules[rule_name]
                # 清除相关的活跃告警
                if rule_name in self.active_alerts:
                    del self.active_alerts[rule_name]

        self.logger.info(f"移除告警规则: {rule_name}")

    def start_checking(self, interval: float = 30.0):
        """开始告警检查"""
        if self._checking:
            return

        self._check_interval = interval
        self._checking = True
        self._check_thread = threading.Thread(target=self._check_loop, daemon=True)
        self._check_thread.start()

        self.logger.info(f"告警检查已启动，检查间隔: {interval}秒")

    def stop_checking(self):
        """停止告警检查"""
        self._checking = False
        if self._check_thread:
            self._check_thread.join(timeout=10)

        self.logger.info("告警检查已停止")

    def _check_loop(self):
        """告警检查循环"""
        while self._checking:
            try:
                self._check_alerts()
                time.sleep(self._check_interval)
            except Exception as e:
                self.logger.error(f"告警检查异常: {str(e)}")
                time.sleep(self._check_interval)

    def _check_alerts(self):
        """检查告警"""
        with self.lock:
            rules = self.rules.copy()

        for rule_name, rule in rules.items():
            if not rule.enabled:
                continue

            try:
                self._check_single_rule(rule)
            except Exception as e:
                self.logger.error(f"检查告警规则 {rule_name} 时出错: {str(e)}")

    def _check_single_rule(self, rule: AlertRule):
        """检查单个告警规则"""
        # 获取最新的指标值
        recent_metrics = self.collector.get_metric_history(rule.metric_name, limit=1)
        if not recent_metrics:
            return

        current_metric = recent_metrics[-1]
        current_value = current_metric.value

        # 检查条件
        condition_met = self._evaluate_condition(
            current_value, rule.condition, rule.threshold
        )

        current_time = time.time()

        if condition_met:
            # 条件满足，检查是否需要触发告警
            if rule.name not in self.active_alerts:
                # 新告警
                alert = Alert(
                    rule_name=rule.name,
                    metric_name=rule.metric_name,
                    current_value=current_value,
                    threshold=rule.threshold,
                    level=rule.level,
                    timestamp=current_time,
                    message=f"指标 {rule.metric_name} 当前值 {current_value} {rule.condition} {rule.threshold}"
                )

                self.active_alerts[rule.name] = alert
                self.alert_history.append(alert)

                # 限制历史记录大小
                if len(self.alert_history) > 1000:
                    self.alert_history.pop(0)

                self._send_alert(alert)
        else:
            # 条件不满足，清除告警
            if rule.name in self.active_alerts:
                del self.active_alerts[rule.name]
                self.logger.info(f"告警恢复: {rule.name}")

    def _evaluate_condition(self, value: float, condition: str, threshold: float) -> bool:
        """评估告警条件"""
        if condition == ">":
            return value > threshold
        elif condition == "<":
            return value < threshold
        elif condition == ">=":
            return value >= threshold
        elif condition == "<=":
            return value <= threshold
        elif condition == "==":
            return abs(value - threshold) < 1e-9
        elif condition == "!=":
            return abs(value - threshold) >= 1e-9
        else:
            return False

    def _send_alert(self, alert: Alert):
        """发送告警"""
        # 记录告警日志
        if alert.level == AlertLevel.CRITICAL:
            self.logger.critical(f"严重告警: {alert.message}")
        elif alert.level == AlertLevel.ERROR:
            self.logger.error(f"错误告警: {alert.message}")
        elif alert.level == AlertLevel.WARNING:
            self.logger.warning(f"警告告警: {alert.message}")
        else:
            self.logger.info(f"信息告警: {alert.message}")

        # 这里可以扩展其他告警通知方式，如邮件、短信、钉钉等

    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        with self.lock:
            return list(self.active_alerts.values())

    def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """获取告警历史"""
        with self.lock:
            return self.alert_history[-limit:] if limit > 0 else self.alert_history.copy()


class PerformanceMonitor:
    """
    性能监控主类

    集成所有监控功能的主要接口。
    """

    def __init__(self, max_history: int = 10000):
        self.collector = MetricCollector(max_history)
        self.system_monitor = SystemMonitor(self.collector)
        self.timer = PerformanceTimer(self.collector)
        self.db_monitor = DatabaseMonitor(self.collector)
        self.api_monitor = APIMonitor(self.collector)
        self.alert_manager = AlertManager(self.collector)

        self.logger = get_logger(self.__class__.__name__)

    def start_monitoring(
        self,
        system_interval: float = 5.0,
        alert_interval: float = 30.0
    ):
        """启动监控"""
        self.system_monitor.start_monitoring(system_interval)
        self.alert_manager.start_checking(alert_interval)

        self.logger.info("性能监控已启动")

    def stop_monitoring(self):
        """停止监控"""
        self.system_monitor.stop_monitoring()
        self.alert_manager.stop_checking()

        self.logger.info("性能监控已停止")

    def get_system_summary(self) -> Dict[str, Any]:
        """获取系统监控摘要"""
        summary = {}

        # 系统指标
        system_metrics = [
            "system.cpu.usage_percent",
            "system.memory.usage_percent",
            "system.disk.usage_percent"
        ]

        for metric_name in system_metrics:
            stats = self.collector.get_metric_stats(metric_name, duration=3600)
            if stats['count'] > 0:
                summary[metric_name] = stats

        # 活跃告警
        active_alerts = self.alert_manager.get_active_alerts()
        summary['active_alerts'] = [asdict(alert) for alert in active_alerts]

        # 告警统计
        alert_history = self.alert_manager.get_alert_history(limit=100)
        alert_stats: Dict[str, Any] = {'total': len(alert_history)}

        if alert_history:
            by_level = defaultdict(int)
            for alert in alert_history:
                by_level[alert.level.value] += 1
            alert_stats['by_level'] = dict(by_level)

        summary['alert_statistics'] = alert_stats

        return summary

    def add_alert_rule(
        self,
        name: str,
        metric_name: str,
        condition: str,
        threshold: float,
        level: str = "warning"
    ):
        """添加告警规则的便捷方法"""
        rule = AlertRule(
            name=name,
            metric_name=metric_name,
            condition=condition,
            threshold=threshold,
            level=AlertLevel(level.lower())
        )

        self.alert_manager.add_rule(rule)

    def export_metrics(self, format: str = "json") -> str:
        """导出指标数据"""
        all_metrics = {}

        for metric_name, metric_deque in self.collector.metrics.items():
            metrics_data = [asdict(m) for m in metric_deque]
            all_metrics[metric_name] = metrics_data

        if format.lower() == "json":
            return json.dumps(all_metrics, indent=2, default=str)
        else:
            return str(all_metrics)


# 全局性能监控实例
performance_monitor = PerformanceMonitor()


# 便捷装饰器
def monitor_performance(operation_name: Optional[str] = None, tags: Optional[Dict[str, str]] = None):
    """性能监控装饰器"""
    def decorator(func: Callable) -> Callable:
        name = operation_name or f"{func.__module__}.{func.__name__}"
        return performance_monitor.timer.time_function(name, tags or {})(func)

    return decorator


def monitor_database_query(query_type: str):
    """数据库查询监控装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with performance_monitor.db_monitor.monitor_query(query_type):
                return func(*args, **kwargs)

        return wrapper
    return decorator


def monitor_api_call(endpoint: str, method: str = "GET"):
    """API调用监控装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with performance_monitor.api_monitor.monitor_api_call(endpoint, method):
                return func(*args, **kwargs)

        return wrapper
    return decorator

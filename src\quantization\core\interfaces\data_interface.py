#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据接口定义

定义统一的数据访问接口，确保不同数据源的一致性。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union, Any
import pandas as pd
from datetime import datetime, date


class DataInterface(ABC):
    """
    数据接口基类
    
    定义所有数据提供商必须实现的标准接口，确保数据访问的一致性。
    """
    
    @abstractmethod
    def get_stock_data(
        self, 
        stock_code: str, 
        start_date: Optional[Union[str, date, datetime]] = None,
        end_date: Optional[Union[str, date, datetime]] = None,
        fields: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        获取股票基础数据
        
        Args:
            stock_code: 股票代码（如 '300001'）
            start_date: 开始日期
            end_date: 结束日期  
            fields: 需要的字段列表
            
        Returns:
            包含股票数据的DataFrame
        """
        pass
    
    @abstractmethod
    def get_stock_info(self, stock_code: str) -> Dict[str, Any]:
        """
        获取股票基本信息
        
        Args:
            stock_code: 股票代码
            
        Returns:
            包含股票基本信息的字典
        """
        pass
    
    @abstractmethod
    def get_market_cap(self, stock_code: str) -> Optional[float]:
        """
        获取股票市值
        
        Args:
            stock_code: 股票代码
            
        Returns:
            市值（亿元），获取失败返回None
        """
        pass
    
    @abstractmethod
    def get_big_order_net_ratio(self, stock_code: str) -> Optional[float]:
        """
        获取大单净量比
        
        Args:
            stock_code: 股票代码
            
        Returns:
            大单净量比，获取失败返回None
        """
        pass
    
    @abstractmethod
    def get_williams_r(
        self, 
        stock_code: str, 
        period: int = 14
    ) -> Optional[float]:
        """
        获取威廉指标
        
        Args:
            stock_code: 股票代码
            period: 计算周期
            
        Returns:
            威廉指标值，获取失败返回None
        """
        pass
    
    @abstractmethod
    def get_volume_ratio(self, stock_code: str) -> Optional[float]:
        """
        获取量比
        
        Args:
            stock_code: 股票代码
            
        Returns:
            量比值，获取失败返回None
        """
        pass
    
    @abstractmethod
    def get_chinext_stocks(self) -> List[str]:
        """
        获取创业板股票列表
        
        Returns:
            创业板股票代码列表
        """
        pass
    
    @abstractmethod
    def is_st_stock(self, stock_code: str) -> bool:
        """
        判断是否为ST股票
        
        Args:
            stock_code: 股票代码
            
        Returns:
            是否为ST股票
        """
        pass
    
    @abstractmethod
    def validate_connection(self) -> bool:
        """
        验证数据源连接
        
        Returns:
            连接是否正常
        """
        pass
    
    @abstractmethod
    def get_last_update_time(self) -> Optional[datetime]:
        """
        获取最后更新时间
        
        Returns:
            最后更新时间，获取失败返回None
        """
        pass


class StrategyInterface(ABC):
    """
    策略接口基类
    
    定义所有交易策略必须实现的标准接口。
    """
    
    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> None:
        """
        初始化策略
        
        Args:
            config: 策略配置参数
        """
        pass
    
    @abstractmethod
    def select_stocks(self, date: Union[str, date, datetime]) -> List[str]:
        """
        选股逻辑
        
        Args:
            date: 选股日期
            
        Returns:
            选中的股票代码列表
        """
        pass
    
    @abstractmethod
    def generate_signals(
        self, 
        stock_code: str, 
        data: pd.DataFrame
    ) -> Dict[str, Any]:
        """
        生成交易信号
        
        Args:
            stock_code: 股票代码
            data: 股票数据
            
        Returns:
            交易信号字典
        """
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """
        获取策略名称
        
        Returns:
            策略名称
        """
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取策略参数
        
        Returns:
            策略参数字典
        """
        pass


class BacktestInterface(ABC):
    """
    回测接口基类
    
    定义回测引擎必须实现的标准接口。
    """
    
    @abstractmethod
    def run_backtest(
        self,
        strategy: StrategyInterface,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        initial_capital: float = 1000000.0
    ) -> Dict[str, Any]:
        """
        运行回测
        
        Args:
            strategy: 交易策略
            start_date: 回测开始日期
            end_date: 回测结束日期
            initial_capital: 初始资金
            
        Returns:
            回测结果字典
        """
        pass
    
    @abstractmethod
    def calculate_metrics(self, results: Dict[str, Any]) -> Dict[str, float]:
        """
        计算性能指标
        
        Args:
            results: 回测结果
            
        Returns:
            性能指标字典
        """
        pass
    
    @abstractmethod
    def generate_report(self, results: Dict[str, Any]) -> str:
        """
        生成回测报告
        
        Args:
            results: 回测结果
            
        Returns:
            报告内容
        """
        pass

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化功能测试

测试图表生成和保存功能。
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from quantization.visualization.result_visualizer import ResultVisualizer, VisualizationConfig

def test_simple_chart():
    """测试简单图表生成"""
    print("测试简单图表生成...")
    
    try:
        import matplotlib.pyplot as plt
        
        # 创建简单图表
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 生成测试数据
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        
        ax.plot(x, y, label='sin(x)')
        ax.set_title('测试图表')
        ax.set_xlabel('X轴')
        ax.set_ylabel('Y轴')
        ax.legend()
        ax.grid(True)
        
        # 保存图表
        output_dir = Path("output/test_charts")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        file_path = output_dir / "test_chart.png"
        fig.savefig(file_path, dpi=300, bbox_inches='tight')
        plt.close(fig)
        
        print(f"✓ 简单图表已保存: {file_path}")
        return True
        
    except Exception as e:
        print(f"✗ 简单图表生成失败: {e}")
        return False

def test_visualizer_config():
    """测试可视化配置"""
    print("测试可视化配置...")
    
    try:
        config = VisualizationConfig(
            style="seaborn",
            figure_size=(12, 8),
            interactive=False,  # 使用matplotlib
            save_format="png",
            dpi=300
        )
        
        print(f"✓ 可视化配置创建成功:")
        print(f"  样式: {config.style}")
        print(f"  图表大小: {config.figure_size}")
        print(f"  交互式: {config.interactive}")
        print(f"  保存格式: {config.save_format}")
        print(f"  DPI: {config.dpi}")
        
        return True
        
    except Exception as e:
        print(f"✗ 可视化配置创建失败: {e}")
        return False

def test_base_visualizer():
    """测试基础可视化器"""
    print("测试基础可视化器...")
    
    try:
        from quantization.visualization.result_visualizer import BaseVisualizer
        
        config = VisualizationConfig(
            style="seaborn",
            figure_size=(10, 6),
            interactive=False,
            save_format="png",
            dpi=300
        )
        
        visualizer = BaseVisualizer(config)
        
        # 创建测试图表
        import matplotlib.pyplot as plt
        fig, ax = plt.subplots(figsize=config.figure_size)
        
        x = np.linspace(0, 2*np.pi, 100)
        y = np.cos(x)
        
        ax.plot(x, y, 'b-', linewidth=2, label='cos(x)')
        ax.set_title('基础可视化器测试')
        ax.set_xlabel('X轴')
        ax.set_ylabel('Y轴')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 使用save_figure方法保存
        output_dir = "output/test_charts"
        filename = "base_visualizer_test"
        
        saved_path = visualizer.save_figure(fig, filename, output_dir)
        plt.close(fig)
        
        if saved_path:
            print(f"✓ 基础可视化器测试成功: {saved_path}")
            return True
        else:
            print("✗ 基础可视化器保存失败")
            return False
        
    except Exception as e:
        print(f"✗ 基础可视化器测试失败: {e}")
        return False

def test_backtest_visualizer():
    """测试回测可视化器"""
    print("测试回测可视化器...")
    
    try:
        from quantization.visualization.result_visualizer import BacktestVisualizer
        
        config = VisualizationConfig(
            style="seaborn",
            figure_size=(12, 8),
            interactive=False,
            save_format="png",
            dpi=300
        )
        
        visualizer = BacktestVisualizer(config)
        
        # 生成模拟回测数据
        np.random.seed(42)
        n_days = 100
        dates = pd.date_range(start='2023-01-01', periods=n_days, freq='D')
        
        daily_returns = np.random.normal(0.001, 0.02, n_days)
        
        portfolio_history = []
        for i, date in enumerate(dates):
            portfolio_history.append({
                'date': date.strftime('%Y-%m-%d'),
                'daily_return': daily_returns[i],
                'portfolio_value': 1000000 * np.prod(1 + daily_returns[:i+1])
            })
        
        results = {
            'strategy_name': 'Test Strategy',
            'start_date': '2023-01-01',
            'end_date': '2023-04-10',
            'portfolio_history': portfolio_history,
            'performance_metrics': {
                'total_return': 0.15,
                'annualized_return': 0.12,
                'annualized_volatility': 0.18,
                'sharpe_ratio': 0.67,
                'max_drawdown': -0.08
            }
        }
        
        # 测试净值曲线
        output_dir = "output/test_charts"
        save_path = f"{output_dir}/test_equity_curve"
        
        saved_path = visualizer.plot_equity_curve(results, save_path=save_path)
        
        if saved_path:
            print(f"✓ 回测可视化器测试成功: {saved_path}")
            return True
        else:
            print("✗ 回测可视化器保存失败")
            return False
        
    except Exception as e:
        print(f"✗ 回测可视化器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("可视化功能测试")
    print("=" * 50)
    
    tests = [
        test_simple_chart,
        test_visualizer_config,
        test_base_visualizer,
        test_backtest_visualizer
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print()
        if test():
            passed += 1
        print("-" * 30)
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！")
        
        # 检查生成的文件
        print("\n生成的文件:")
        test_charts_dir = Path("output/test_charts")
        if test_charts_dir.exists():
            for file_path in test_charts_dir.glob("*.png"):
                print(f"  {file_path}")
        else:
            print("  没有找到生成的文件")
    else:
        print("✗ 部分测试失败")

if __name__ == "__main__":
    main()

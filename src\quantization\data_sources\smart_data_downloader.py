#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能数据下载器
解决数据下载失败问题的完整解决方案
"""

import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
import warnings
warnings.filterwarnings('ignore')

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False

class SmartDataDownloader:
    """智能数据下载器"""
    
    def __init__(self):
        """初始化智能下载器"""
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # 缓存有效的股票列表和交易日
        self._valid_stocks_cache = None
        self._trading_days_cache = None
        self._cache_time = None
        self._cache_expire_hours = 24  # 缓存24小时
        
        self.logger.info("智能数据下载器初始化完成")
    
    def get_valid_chinext_stocks(self, limit: int = 100) -> List[str]:
        """
        获取有效的创业板股票列表
        
        Args:
            limit: 返回股票数量限制
            
        Returns:
            List[str]: 有效的创业板股票代码列表
        """
        # 检查缓存
        if self._is_cache_valid() and self._valid_stocks_cache:
            return self._valid_stocks_cache[:limit]
        
        try:
            if not AKSHARE_AVAILABLE:
                # 返回一些已知的活跃创业板股票
                default_stocks = [
                    "300001.SZ", "300002.SZ", "300003.SZ", "300009.SZ", "300010.SZ",
                    "300012.SZ", "300014.SZ", "300017.SZ", "300019.SZ", "300020.SZ",
                    "300024.SZ", "300027.SZ", "300033.SZ", "300036.SZ", "300037.SZ",
                    "300041.SZ", "300043.SZ", "300045.SZ", "300047.SZ", "300048.SZ",
                    "300058.SZ", "300061.SZ", "300063.SZ", "300065.SZ", "300070.SZ",
                    "300072.SZ", "300073.SZ", "300074.SZ", "300076.SZ", "300078.SZ",
                    "300079.SZ", "300081.SZ", "300083.SZ", "300085.SZ", "300088.SZ",
                    "300090.SZ", "300091.SZ", "300093.SZ", "300094.SZ", "300096.SZ",
                    "300098.SZ", "300100.SZ", "300101.SZ", "300102.SZ", "300103.SZ",
                    "300104.SZ", "300106.SZ", "300107.SZ", "300108.SZ", "300109.SZ"
                ]
                return default_stocks[:limit]
            
            # 获取所有A股数据
            stock_info = ak.stock_zh_a_spot_em()
            
            # 筛选创业板股票（代码以300开头）
            chinext_stocks = stock_info[stock_info['代码'].str.startswith('300')]
            
            # 按成交量排序，选择活跃股票
            chinext_stocks = chinext_stocks.sort_values('成交量', ascending=False)
            
            # 过滤掉ST股票和停牌股票
            valid_stocks = []
            for _, row in chinext_stocks.iterrows():
                code = row['代码']
                name = row['名称']
                
                # 跳过ST股票
                if 'ST' in name or '*ST' in name:
                    continue
                
                # 跳过停牌股票（成交量为0）
                if row['成交量'] == 0:
                    continue
                
                valid_stocks.append(f"{code}.SZ")
                
                if len(valid_stocks) >= limit * 2:  # 获取更多备选
                    break
            
            # 更新缓存
            self._valid_stocks_cache = valid_stocks
            self._cache_time = datetime.now()
            
            self.logger.info(f"获取到 {len(valid_stocks)} 只有效创业板股票")
            return valid_stocks[:limit]
            
        except Exception as e:
            self.logger.error(f"获取创业板股票列表失败: {e}")
            # 返回默认列表
            return [
                "300001.SZ", "300002.SZ", "300003.SZ", "300009.SZ", "300010.SZ",
                "300012.SZ", "300014.SZ", "300017.SZ", "300019.SZ", "300020.SZ"
            ][:limit]
    
    def get_recent_trading_days(self, days: int = 10) -> List[str]:
        """
        获取最近的交易日
        
        Args:
            days: 需要的交易日数量
            
        Returns:
            List[str]: 交易日列表
        """
        # 检查缓存
        if self._is_cache_valid() and self._trading_days_cache:
            return self._trading_days_cache[:days]
        
        try:
            if not AKSHARE_AVAILABLE:
                # 生成最近的工作日（排除周末）
                trading_days = []
                current_date = datetime.now()
                
                while len(trading_days) < days:
                    current_date -= timedelta(days=1)
                    # 跳过周末
                    if current_date.weekday() < 5:  # 0-4是周一到周五
                        trading_days.append(current_date.strftime('%Y-%m-%d'))
                
                return trading_days
            
            # 使用akshare获取交易日历
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
            
            trade_cal = ak.stock_zh_a_hist_min_em_qfq(
                symbol="000001",  # 使用平安银行作为参考
                start_date=start_date,
                end_date=end_date,
                period="1"
            )
            
            if not trade_cal.empty:
                # 提取交易日期
                trading_days = trade_cal['时间'].dt.strftime('%Y-%m-%d').unique().tolist()
                trading_days = sorted(trading_days, reverse=True)  # 最新日期在前
                
                # 更新缓存
                self._trading_days_cache = trading_days
                self._cache_time = datetime.now()
                
                self.logger.info(f"获取到 {len(trading_days)} 个交易日")
                return trading_days[:days]
            
        except Exception as e:
            self.logger.warning(f"获取交易日历失败: {e}")
        
        # 备用方案：生成最近的工作日
        trading_days = []
        current_date = datetime.now()
        
        while len(trading_days) < days:
            current_date -= timedelta(days=1)
            # 跳过周末
            if current_date.weekday() < 5:
                trading_days.append(current_date.strftime('%Y-%m-%d'))
        
        return trading_days
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        if self._cache_time is None:
            return False
        
        elapsed_hours = (datetime.now() - self._cache_time).total_seconds() / 3600
        return elapsed_hours < self._cache_expire_hours
    
    def test_stock_data_availability(self, stock_code: str, date: str) -> Tuple[bool, int, str]:
        """
        测试股票数据可用性
        
        Args:
            stock_code: 股票代码
            date: 日期
            
        Returns:
            (是否可用, 记录数, 消息)
        """
        if not AKSHARE_AVAILABLE:
            return False, 0, "akshare不可用"
        
        try:
            symbol = stock_code.replace('.SZ', '').replace('.SH', '')
            
            # 尝试获取数据
            data = ak.stock_zh_a_hist_min_em(
                symbol=symbol,
                start_date=f"{date} 09:30:00",
                end_date=f"{date} 15:00:00",
                period="1",
                adjust="qfq"
            )
            
            if data.empty:
                return False, 0, "无数据"
            
            return True, len(data), "数据可用"
            
        except Exception as e:
            return False, 0, f"错误: {str(e)}"
    
    def batch_test_data_availability(self, stock_codes: List[str], dates: List[str], 
                                   max_test: int = 10) -> Dict[str, Any]:
        """
        批量测试数据可用性
        
        Args:
            stock_codes: 股票代码列表
            dates: 日期列表
            max_test: 最大测试数量
            
        Returns:
            Dict: 测试结果统计
        """
        results = {
            'total_tests': 0,
            'successful_tests': 0,
            'failed_tests': 0,
            'success_rate': 0.0,
            'valid_stocks': [],
            'valid_dates': [],
            'test_details': []
        }
        
        test_count = 0
        
        for stock_code in stock_codes[:max_test]:
            for date in dates[:3]:  # 每只股票测试3个日期
                if test_count >= max_test:
                    break
                
                success, count, message = self.test_stock_data_availability(stock_code, date)
                
                results['total_tests'] += 1
                results['test_details'].append({
                    'stock_code': stock_code,
                    'date': date,
                    'success': success,
                    'records': count,
                    'message': message
                })
                
                if success:
                    results['successful_tests'] += 1
                    if stock_code not in results['valid_stocks']:
                        results['valid_stocks'].append(stock_code)
                    if date not in results['valid_dates']:
                        results['valid_dates'].append(date)
                else:
                    results['failed_tests'] += 1
                
                test_count += 1
                
                # 添加延迟避免请求过快
                time.sleep(0.1)
        
        if results['total_tests'] > 0:
            results['success_rate'] = results['successful_tests'] / results['total_tests']
        
        return results
    
    def get_recommended_test_data(self) -> Dict[str, Any]:
        """
        获取推荐的测试数据（确保可用性）
        
        Returns:
            Dict: 推荐的股票和日期
        """
        self.logger.info("获取推荐测试数据...")
        
        # 获取有效股票和交易日
        valid_stocks = self.get_valid_chinext_stocks(20)
        trading_days = self.get_recent_trading_days(5)
        
        self.logger.info(f"候选股票: {len(valid_stocks)}只")
        self.logger.info(f"候选交易日: {len(trading_days)}个")
        
        # 批量测试数据可用性
        test_results = self.batch_test_data_availability(valid_stocks, trading_days, max_test=15)
        
        # 筛选出可用的数据
        recommended_stocks = test_results['valid_stocks'][:10]
        recommended_dates = test_results['valid_dates'][:3]
        
        self.logger.info(f"推荐股票: {len(recommended_stocks)}只")
        self.logger.info(f"推荐日期: {len(recommended_dates)}个")
        self.logger.info(f"数据可用性: {test_results['success_rate']:.1%}")
        
        return {
            'stocks': recommended_stocks,
            'dates': recommended_dates,
            'success_rate': test_results['success_rate'],
            'test_results': test_results
        }
    
    def download_reliable_data(self, max_stocks: int = 10, max_dates: int = 3) -> Dict[str, Any]:
        """
        下载可靠的数据
        
        Args:
            max_stocks: 最大股票数量
            max_dates: 最大日期数量
            
        Returns:
            Dict: 下载结果
        """
        # 获取推荐数据
        recommended = self.get_recommended_test_data()
        
        stocks = recommended['stocks'][:max_stocks]
        dates = recommended['dates'][:max_dates]
        
        if not stocks or not dates:
            return {
                'success': False,
                'message': '未找到可用的股票或日期',
                'data': {}
            }
        
        # 下载数据
        downloaded_data = {}
        success_count = 0
        total_records = 0
        
        for stock_code in stocks:
            downloaded_data[stock_code] = {}
            
            for date in dates:
                try:
                    symbol = stock_code.replace('.SZ', '').replace('.SH', '')
                    
                    data = ak.stock_zh_a_hist_min_em(
                        symbol=symbol,
                        start_date=f"{date} 09:30:00",
                        end_date=f"{date} 15:00:00",
                        period="1",
                        adjust="qfq"
                    )
                    
                    if not data.empty:
                        downloaded_data[stock_code][date] = data
                        success_count += 1
                        total_records += len(data)
                        self.logger.info(f"成功下载 {stock_code} {date}: {len(data)}条记录")
                    else:
                        self.logger.warning(f"无数据 {stock_code} {date}")
                    
                    time.sleep(0.1)  # 避免请求过快
                    
                except Exception as e:
                    self.logger.error(f"下载失败 {stock_code} {date}: {e}")
        
        return {
            'success': success_count > 0,
            'message': f'成功下载 {success_count} 个数据集，共 {total_records} 条记录',
            'data': downloaded_data,
            'stats': {
                'success_count': success_count,
                'total_records': total_records,
                'stocks': stocks,
                'dates': dates
            }
        }

    def get_all_chinext_stocks(self) -> List[str]:
        """
        获取所有创业板股票代码

        Returns:
            创业板股票代码列表
        """
        try:
            if not AKSHARE_AVAILABLE:
                # 返回一些已知的创业板股票
                default_stocks = []
                for i in range(1, 1000):
                    code = f"300{i:03d}.SZ"
                    default_stocks.append(code)
                return default_stocks

            # 获取创业板股票列表
            stock_list = ak.stock_zh_a_spot_em()

            # 筛选创业板股票（300开头）
            chinext_stocks = []
            for _, row in stock_list.iterrows():
                code = row['代码']
                if code.startswith('300'):
                    chinext_stocks.append(f"{code}.SZ")

            self.logger.info(f"获取创业板股票列表: {len(chinext_stocks)}只")
            return chinext_stocks

        except Exception as e:
            self.logger.error(f"获取创业板股票列表失败: {e}")
            # 返回默认列表
            default_stocks = []
            for i in range(1, 1000):
                code = f"300{i:03d}.SZ"
                default_stocks.append(code)
            return default_stocks

    def get_trading_days_range(self, start_date: str, end_date: str) -> List[str]:
        """
        获取指定日期范围内的交易日

        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            交易日列表
        """
        try:
            if not AKSHARE_AVAILABLE:
                # 备用方案：生成工作日列表（排除周末）
                trading_days = []
                current_date = datetime.strptime(start_date, '%Y-%m-%d')
                end_date_dt = datetime.strptime(end_date, '%Y-%m-%d')

                while current_date <= end_date_dt:
                    # 排除周末
                    if current_date.weekday() < 5:
                        trading_days.append(current_date.strftime('%Y-%m-%d'))
                    current_date += timedelta(days=1)

                return trading_days

            # 获取交易日历
            trading_calendar = ak.tool_trade_date_hist_sina()

            # 转换日期格式
            trading_calendar['trade_date'] = pd.to_datetime(trading_calendar['trade_date']).dt.strftime('%Y-%m-%d')

            # 筛选日期范围
            mask = (trading_calendar['trade_date'] >= start_date) & (trading_calendar['trade_date'] <= end_date)
            trading_days = trading_calendar[mask]['trade_date'].tolist()

            self.logger.info(f"获取交易日历: {start_date} 到 {end_date}, 共 {len(trading_days)} 个交易日")
            return trading_days

        except Exception as e:
            self.logger.error(f"获取交易日历失败: {e}")

            # 备用方案：生成工作日列表（排除周末）
            trading_days = []
            current_date = datetime.strptime(start_date, '%Y-%m-%d')
            end_date_dt = datetime.strptime(end_date, '%Y-%m-%d')

            while current_date <= end_date_dt:
                # 排除周末
                if current_date.weekday() < 5:
                    trading_days.append(current_date.strftime('%Y-%m-%d'))
                current_date += timedelta(days=1)

            self.logger.warning(f"使用备用交易日历: {len(trading_days)} 个工作日")
            return trading_days

"""
Setup script for A-Share Data Acquisition Framework
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    print("Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} is compatible")
    return True

def install_requirements():
    """Install required packages"""
    print("\nInstalling required packages...")
    
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    try:
        # Install packages
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ All packages installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install packages: {e}")
        print("You may need to install packages manually:")
        print("pip install pandas numpy akshare requests beautifulsoup4")
        return False

def create_directories():
    """Create necessary directories"""
    print("\nCreating directories...")
    
    directories = [
        "data",
        "data/cache", 
        "logs",
        "results"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True

def test_framework():
    """Test the framework"""
    print("\nTesting framework...")
    
    try:
        # Import test module
        sys.path.insert(0, '.')
        import test_framework
        
        # Run tests
        success = test_framework.run_all_tests()
        
        if success:
            print("✅ Framework test passed")
        else:
            print("❌ Framework test failed")
        
        return success
        
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def create_sample_config():
    """Create sample configuration file"""
    print("\nCreating sample configuration...")
    
    config_content = """# A-Share Data Acquisition Framework Configuration
# Copy this file to .env and modify as needed

# Database settings
DATABASE_URL=sqlite:///data/ashare_data.db

# Cache settings
CACHE_ENABLED=true
CACHE_TTL_DAYS=7
CACHE_MAX_SIZE_MB=1000

# Rate limiting (seconds between requests)
AKSHARE_RATE_LIMIT=0.5
WEB_SCRAPER_RATE_LIMIT=1.0
MAX_RETRIES=3
RETRY_DELAY=2.0

# Data validation
VALIDATE_DATA=true
MIN_DATA_POINTS=10
MAX_PRICE_CHANGE_PERCENT=50.0

# Logging
LOG_LEVEL=INFO
LOG_FILE_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# Web scraping
WEB_SCRAPER_TIMEOUT=30
WEB_SCRAPER_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

# Environment (dev, prod, or default)
ENVIRONMENT=default
"""
    
    config_file = Path("config.env.sample")
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print(f"✅ Sample configuration created: {config_file}")
        print("   Copy to .env and modify as needed")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create config file: {e}")
        return False

def show_usage_examples():
    """Show basic usage examples"""
    print("\n" + "="*60)
    print("🎉 Setup completed successfully!")
    print("="*60)
    
    print("\n📖 Quick Start Examples:")
    print("-" * 30)
    
    print("\n1. Basic data acquisition:")
    print("""
from data_acquisition import DataManager

dm = DataManager()
data = dm.get_stock_data('000001.SZ', '2023-01-01', '2023-12-31')
print(f"Retrieved {len(data)} records")
dm.cleanup()
""")
    
    print("\n2. Backtesting interface:")
    print("""
from data_acquisition import BacktestingDataInterface

bt = BacktestingDataInterface()
stocks = ['000001.SZ', '000002.SZ', '600000.SH']
price_matrix = bt.get_price_matrix(stocks, '2023-01-01', '2023-12-31')
bt.cleanup()
""")
    
    print("\n3. Run examples:")
    print("   python examples/basic_usage.py")
    print("   python examples/backtesting_example.py")
    
    print("\n📁 Important Files:")
    print("-" * 20)
    print("   README.md              - Complete documentation")
    print("   examples/              - Usage examples")
    print("   test_framework.py      - Test the framework")
    print("   config.env.sample      - Configuration template")
    
    print("\n🔧 Configuration:")
    print("-" * 20)
    print("   1. Copy config.env.sample to .env")
    print("   2. Modify settings as needed")
    print("   3. Set environment variables or use Config class")
    
    print("\n📊 Data Sources:")
    print("-" * 20)
    print("   Primary: akshare library (install: pip install akshare)")
    print("   Fallback: Web scraping (Tencent Finance, East Money)")
    
    print("\n⚠️  Important Notes:")
    print("-" * 20)
    print("   - Respect data source rate limits")
    print("   - Check data licensing and usage terms")
    print("   - Enable caching to minimize API calls")
    print("   - Monitor logs for any issues")

def main():
    """Main setup function"""
    print("A-Share Data Acquisition Framework Setup")
    print("=" * 50)
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Installing requirements", install_requirements),
        ("Creating directories", create_directories),
        ("Creating sample config", create_sample_config),
        ("Testing framework", test_framework)
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        
        if not step_func():
            print(f"\n❌ Setup failed at: {step_name}")
            print("Please check the error messages above and try again.")
            return False
    
    show_usage_examples()
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

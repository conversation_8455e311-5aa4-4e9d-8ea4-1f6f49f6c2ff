"""
Akshare data provider for A-Share market data
"""

import pandas as pd
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date
from .base_provider import BaseDataProvider
from ..utils.rate_limiter import rate_limited, retry_with_config
from ..utils.stock_codes import parse_stock_code, normalize_stock_code
from ..config.settings import Config

class AkshareProvider(BaseDataProvider):
    """Akshare-based data provider for A-Share market data"""
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize Akshare provider"""
        super().__init__(config)
        self.retry_decorator = retry_with_config(config)
    
    def _initialize_provider(self):
        """Initialize akshare library"""
        try:
            import akshare as ak
            self.ak = ak
            self.logger.info("Akshare provider initialized successfully")
        except ImportError:
            self.logger.error("Akshare library not found. Please install: pip install akshare")
            self.ak = None
    
    def is_available(self) -> bool:
        """Check if akshare is available"""
        return self.ak is not None
    
    @rate_limited('akshare')
    def get_stock_data(self, 
                      stock_code: str, 
                      start_date: Union[str, date, datetime],
                      end_date: Union[str, date, datetime],
                      frequency: str = 'daily') -> Optional[pd.DataFrame]:
        """
        Get stock price data from akshare
        
        Args:
            stock_code: Stock code (e.g., '000001.SZ')
            start_date: Start date
            end_date: End date  
            frequency: Data frequency ('daily' only supported)
            
        Returns:
            pd.DataFrame: Stock data with OHLCV columns
        """
        if not self.is_available():
            return None
        
        try:
            # Normalize stock code
            normalized_code = normalize_stock_code(stock_code)
            if not normalized_code:
                self.logger.error(f"Invalid stock code: {stock_code}")
                return None
            
            # Parse stock code
            code_parts = parse_stock_code(normalized_code)
            if not code_parts:
                return None
            
            stock_num, exchange = code_parts
            
            # Validate date range
            start_date, end_date = self.validate_date_range(start_date, end_date)
            
            # Convert dates to string format for akshare
            start_str = start_date.strftime('%Y%m%d')
            end_str = end_date.strftime('%Y%m%d')
            
            # Get data using akshare
            self.logger.debug(f"Fetching data for {stock_code} from {start_str} to {end_str}")
            
            # Use different akshare functions based on exchange
            if exchange == 'SH':
                # Shanghai stocks
                data = self.ak.stock_zh_a_hist(
                    symbol=stock_num,
                    period="daily", 
                    start_date=start_str,
                    end_date=end_str,
                    adjust="qfq"  # Forward adjusted
                )
            else:
                # Shenzhen stocks  
                data = self.ak.stock_zh_a_hist(
                    symbol=stock_num,
                    period="daily",
                    start_date=start_str, 
                    end_date=end_str,
                    adjust="qfq"
                )
            
            if data is None or data.empty:
                self.logger.warning(f"No data returned for {stock_code}")
                return None
            
            # Normalize column names
            data = self._normalize_akshare_columns(data)
            
            # Set date as index
            if 'date' in data.columns:
                data['date'] = pd.to_datetime(data['date'])
                data = data.set_index('date')
            
            # Sort by date
            data = data.sort_index()
            
            self.logger.debug(f"Successfully fetched {len(data)} records for {stock_code}")
            return data
            
        except Exception as e:
            self.handle_provider_error(e, 'get_stock_data', stock_code=stock_code)
            return None
    
    @rate_limited('akshare')
    def get_stock_info(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """
        Get basic stock information
        
        Args:
            stock_code: Stock code
            
        Returns:
            Dict: Stock information
        """
        if not self.is_available():
            return None
        
        try:
            normalized_code = normalize_stock_code(stock_code)
            if not normalized_code:
                return None
            
            code_parts = parse_stock_code(normalized_code)
            if not code_parts:
                return None
            
            stock_num, exchange = code_parts
            
            # Get stock info
            info = self.ak.stock_individual_info_em(symbol=stock_num)
            
            if info is None or info.empty:
                return None
            
            # Convert to dictionary
            info_dict = {}
            for _, row in info.iterrows():
                info_dict[row['item']] = row['value']
            
            return info_dict
            
        except Exception as e:
            self.handle_provider_error(e, 'get_stock_info', stock_code=stock_code)
            return None
    
    @rate_limited('akshare')
    def get_financial_data(self, 
                          stock_code: str,
                          report_type: str = 'annual') -> Optional[pd.DataFrame]:
        """
        Get financial statement data
        
        Args:
            stock_code: Stock code
            report_type: Report type ('annual', 'quarterly')
            
        Returns:
            pd.DataFrame: Financial data
        """
        if not self.is_available():
            return None
        
        try:
            normalized_code = normalize_stock_code(stock_code)
            if not normalized_code:
                return None
            
            code_parts = parse_stock_code(normalized_code)
            if not code_parts:
                return None
            
            stock_num, exchange = code_parts
            
            # Get financial data based on report type
            if report_type == 'annual':
                # Annual financial data
                data = self.ak.stock_financial_abstract_ths(symbol=stock_num)
            else:
                # Quarterly financial data
                data = self.ak.stock_financial_abstract_ths(symbol=stock_num)
            
            if data is None or data.empty:
                return None
            
            return data
            
        except Exception as e:
            self.handle_provider_error(e, 'get_financial_data', stock_code=stock_code)
            return None
    
    @rate_limited('akshare')
    def get_index_data(self,
                      index_code: str,
                      start_date: Union[str, date, datetime],
                      end_date: Union[str, date, datetime]) -> Optional[pd.DataFrame]:
        """
        Get market index data
        
        Args:
            index_code: Index code
            start_date: Start date
            end_date: End date
            
        Returns:
            pd.DataFrame: Index data
        """
        if not self.is_available():
            return None
        
        try:
            # Validate date range
            start_date, end_date = self.validate_date_range(start_date, end_date)
            
            # Convert dates to string format
            start_str = start_date.strftime('%Y%m%d')
            end_str = end_date.strftime('%Y%m%d')
            
            # Map common index codes
            index_mapping = {
                'sh000001': 'sh000001',  # Shanghai Composite
                'sz399001': 'sz399001',  # Shenzhen Component
                'sz399006': 'sz399006',  # ChiNext
                'sh000300': 'sh000300',  # CSI 300
            }
            
            mapped_code = index_mapping.get(index_code.lower(), index_code)
            
            # Get index data
            data = self.ak.stock_zh_index_daily(symbol=mapped_code)
            
            if data is None or data.empty:
                return None
            
            # Filter by date range
            data['date'] = pd.to_datetime(data['date'])
            data = data[(data['date'] >= pd.to_datetime(start_date)) & 
                       (data['date'] <= pd.to_datetime(end_date))]
            
            # Normalize columns
            data = self._normalize_akshare_columns(data)
            
            # Set date as index
            data = data.set_index('date')
            data = data.sort_index()
            
            return data
            
        except Exception as e:
            self.handle_provider_error(e, 'get_index_data', index_code=index_code)
            return None
    
    @rate_limited('akshare')
    def get_stock_list(self, exchange: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        Get list of available stocks
        
        Args:
            exchange: Exchange filter ('SH', 'SZ', or None for all)
            
        Returns:
            pd.DataFrame: Stock list
        """
        if not self.is_available():
            return None
        
        try:
            # Get stock list
            stock_list = self.ak.stock_info_a_code_name()
            
            if stock_list is None or stock_list.empty:
                return None
            
            # Filter by exchange if specified
            if exchange:
                if exchange.upper() == 'SH':
                    stock_list = stock_list[stock_list['code'].str.startswith('6')]
                elif exchange.upper() == 'SZ':
                    stock_list = stock_list[stock_list['code'].str.startswith(('0', '3'))]
            
            return stock_list
            
        except Exception as e:
            self.handle_provider_error(e, 'get_stock_list', exchange=exchange)
            return None
    
    def _normalize_akshare_columns(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Normalize akshare column names to standard format
        
        Args:
            data: Raw akshare data
            
        Returns:
            pd.DataFrame: Data with normalized columns
        """
        # Common akshare column mappings
        column_mapping = {
            '日期': 'date',
            '开盘': 'open',
            '收盘': 'close', 
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume',
            '成交额': 'amount',
            '振幅': 'amplitude',
            '涨跌幅': 'pct_change',
            '涨跌额': 'change',
            '换手率': 'turnover_rate'
        }
        
        # Rename columns if they exist
        available_mappings = {k: v for k, v in column_mapping.items() if k in data.columns}
        if available_mappings:
            data = data.rename(columns=available_mappings)
        
        return data
    
    def get_supported_frequencies(self) -> List[str]:
        """Get supported data frequencies"""
        return ['daily']  # Akshare primarily supports daily data
    
    def get_realtime_data(self, stock_codes: List[str]) -> Optional[pd.DataFrame]:
        """
        Get real-time stock data
        
        Args:
            stock_codes: List of stock codes
            
        Returns:
            pd.DataFrame: Real-time data
        """
        if not self.is_available():
            return None
        
        try:
            # Get real-time data
            data = self.ak.stock_zh_a_spot_em()
            
            if data is None or data.empty:
                return None
            
            # Filter for requested stocks
            if stock_codes:
                normalized_codes = [normalize_stock_code(code) for code in stock_codes]
                valid_codes = [code for code in normalized_codes if code]
                
                # Extract stock numbers for filtering
                stock_numbers = []
                for code in valid_codes:
                    parts = parse_stock_code(code)
                    if parts:
                        stock_numbers.append(parts[0])
                
                if stock_numbers:
                    data = data[data['代码'].isin(stock_numbers)]
            
            return data
            
        except Exception as e:
            self.handle_provider_error(e, 'get_realtime_data')
            return None

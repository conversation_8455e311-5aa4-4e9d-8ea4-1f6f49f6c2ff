#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量控制系统
实现数据清洗、异常值检测、复权验证、完整性审查等功能
确保数据准确性和完整性
"""

import os
import sys
import sqlite3
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
import warnings
from scipy import stats
import json

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

warnings.filterwarnings('ignore')

class DataQualityController:
    """数据质量控制器"""
    
    def __init__(self, db_path: str = "data/chinext_minute_data.db"):
        """
        初始化数据质量控制器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.logger = self._setup_logger()
        
        # 质量控制参数
        self.volume_outlier_threshold = 3.0  # 成交量异常值阈值（标准差倍数）
        self.price_change_threshold = 0.15   # 价格变动异常阈值（15%）
        self.missing_data_threshold = 0.05   # 缺失数据容忍度（5%）
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(f"{__name__}.DataQualityController")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def clean_minute_data(self, stock_code: str, date: str) -> Dict[str, Any]:
        """
        清洗分时数据
        
        Args:
            stock_code: 股票代码
            date: 日期
            
        Returns:
            清洗结果统计
        """
        try:
            # 获取原始数据
            data = self._get_minute_data(stock_code, date)
            if data.empty:
                return {'status': 'no_data', 'message': '无数据'}
            
            original_count = len(data)
            cleaning_stats = {
                'original_count': original_count,
                'cleaned_count': 0,
                'missing_filled': 0,
                'outliers_removed': 0,
                'duplicates_removed': 0,
                'status': 'success'
            }
            
            # 1. 去除重复数据
            data_cleaned = data.drop_duplicates(subset=['time'])
            duplicates_removed = original_count - len(data_cleaned)
            cleaning_stats['duplicates_removed'] = duplicates_removed
            
            # 2. 处理缺失值
            missing_filled = self._handle_missing_values(data_cleaned)
            cleaning_stats['missing_filled'] = missing_filled
            
            # 3. 检测和处理异常值
            outliers_removed = self._handle_outliers(data_cleaned, stock_code)
            cleaning_stats['outliers_removed'] = outliers_removed
            
            # 4. 数据完整性检查
            integrity_check = self._check_data_integrity(data_cleaned, date)
            cleaning_stats.update(integrity_check)
            
            # 5. 保存清洗后的数据
            if len(data_cleaned) > 0:
                self._save_cleaned_data(stock_code, date, data_cleaned)
                cleaning_stats['cleaned_count'] = len(data_cleaned)
            
            self.logger.info(f"数据清洗完成 {stock_code} {date}: "
                           f"原始{original_count}条 -> 清洗后{len(data_cleaned)}条")
            
            return cleaning_stats
            
        except Exception as e:
            self.logger.error(f"数据清洗失败 {stock_code} {date}: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _get_minute_data(self, stock_code: str, date: str) -> pd.DataFrame:
        """获取分时数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = """
                SELECT time, open, high, low, close, volume, amount
                FROM minute_data 
                WHERE stock_code = ? AND date = ?
                ORDER BY time
            """
            
            data = pd.read_sql_query(query, conn, params=(stock_code, date))
            conn.close()
            
            return data
            
        except Exception as e:
            self.logger.error(f"获取分时数据失败: {e}")
            return pd.DataFrame()
    
    def _handle_missing_values(self, data: pd.DataFrame) -> int:
        """
        处理缺失值
        
        Args:
            data: 数据DataFrame
            
        Returns:
            填补的缺失值数量
        """
        missing_count = 0
        
        # 价格字段使用线性插值
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in data.columns:
                missing_before = data[col].isna().sum()
                data[col] = data[col].interpolate(method='linear')
                missing_after = data[col].isna().sum()
                missing_count += (missing_before - missing_after)
        
        # 成交量和成交额使用向前填充
        volume_columns = ['volume', 'amount']
        for col in volume_columns:
            if col in data.columns:
                missing_before = data[col].isna().sum()
                data[col] = data[col].fillna(method='ffill')
                # 如果还有缺失值，用0填充
                data[col] = data[col].fillna(0)
                missing_after = data[col].isna().sum()
                missing_count += (missing_before - missing_after)
        
        return missing_count
    
    def _handle_outliers(self, data: pd.DataFrame, stock_code: str) -> int:
        """
        检测和处理异常值
        
        Args:
            data: 数据DataFrame
            stock_code: 股票代码
            
        Returns:
            处理的异常值数量
        """
        outliers_count = 0
        
        try:
            # 1. 成交量异常值检测
            if 'volume' in data.columns and len(data) > 10:
                volume_mean = data['volume'].mean()
                volume_std = data['volume'].std()
                
                if volume_std > 0:
                    # 使用Z-score方法检测异常值
                    z_scores = np.abs((data['volume'] - volume_mean) / volume_std)
                    volume_outliers = z_scores > self.volume_outlier_threshold
                    
                    # 将异常值替换为中位数
                    volume_median = data['volume'].median()
                    outlier_indices = data[volume_outliers].index
                    data.loc[outlier_indices, 'volume'] = volume_median
                    outliers_count += len(outlier_indices)
            
            # 2. 价格异常值检测（相邻时间点价格变动过大）
            if 'close' in data.columns and len(data) > 1:
                price_changes = data['close'].pct_change().abs()
                price_outliers = price_changes > self.price_change_threshold
                
                # 将异常价格变动的数据点标记
                outlier_indices = data[price_outliers].index
                for idx in outlier_indices:
                    if idx > 0:
                        # 使用前一个价格
                        data.loc[idx, 'close'] = data.loc[idx-1, 'close']
                        outliers_count += 1
            
            # 3. OHLC逻辑检查
            ohlc_outliers = self._check_ohlc_logic(data)
            outliers_count += ohlc_outliers
            
        except Exception as e:
            self.logger.warning(f"异常值处理失败 {stock_code}: {e}")
        
        return outliers_count
    
    def _check_ohlc_logic(self, data: pd.DataFrame) -> int:
        """
        检查OHLC数据逻辑性
        
        Args:
            data: 数据DataFrame
            
        Returns:
            修正的异常数据数量
        """
        fixed_count = 0
        
        for idx, row in data.iterrows():
            try:
                open_price = row['open']
                high_price = row['high']
                low_price = row['low']
                close_price = row['close']
                
                # 检查高价是否为最高
                if high_price < max(open_price, close_price):
                    data.loc[idx, 'high'] = max(open_price, close_price)
                    fixed_count += 1
                
                # 检查低价是否为最低
                if low_price > min(open_price, close_price):
                    data.loc[idx, 'low'] = min(open_price, close_price)
                    fixed_count += 1
                
            except Exception:
                continue
        
        return fixed_count
    
    def _check_data_integrity(self, data: pd.DataFrame, date: str) -> Dict[str, Any]:
        """
        检查数据完整性
        
        Args:
            data: 数据DataFrame
            date: 日期
            
        Returns:
            完整性检查结果
        """
        integrity_result = {
            'time_continuity': True,
            'missing_periods': 0,
            'data_completeness': 0.0
        }
        
        try:
            # 检查时间连续性
            if len(data) > 0:
                # 生成完整的交易时间序列
                expected_times = self._generate_trading_times()
                actual_times = set(data['time'].tolist())
                expected_times_set = set(expected_times)
                
                # 计算缺失的时间段
                missing_times = expected_times_set - actual_times
                integrity_result['missing_periods'] = len(missing_times)
                
                # 计算数据完整性
                completeness = len(actual_times) / len(expected_times_set)
                integrity_result['data_completeness'] = completeness
                
                # 判断时间连续性
                if len(missing_times) / len(expected_times_set) > self.missing_data_threshold:
                    integrity_result['time_continuity'] = False
            
        except Exception as e:
            self.logger.warning(f"完整性检查失败: {e}")
        
        return integrity_result
    
    def _generate_trading_times(self) -> List[str]:
        """生成交易时间序列"""
        times = []
        
        # 上午交易时间 09:30-11:30
        start_time = datetime.strptime("09:30:00", "%H:%M:%S")
        end_time = datetime.strptime("11:30:00", "%H:%M:%S")
        
        current_time = start_time
        while current_time <= end_time:
            times.append(current_time.strftime("%H:%M:%S"))
            current_time += timedelta(minutes=1)
        
        # 下午交易时间 13:00-15:00
        start_time = datetime.strptime("13:00:00", "%H:%M:%S")
        end_time = datetime.strptime("15:00:00", "%H:%M:%S")
        
        current_time = start_time
        while current_time <= end_time:
            times.append(current_time.strftime("%H:%M:%S"))
            current_time += timedelta(minutes=1)
        
        return times
    
    def _save_cleaned_data(self, stock_code: str, date: str, data: pd.DataFrame):
        """保存清洗后的数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 删除原有数据
            cursor.execute("""
                DELETE FROM minute_data 
                WHERE stock_code = ? AND date = ?
            """, (stock_code, date))
            
            # 插入清洗后的数据
            for _, row in data.iterrows():
                cursor.execute("""
                    INSERT INTO minute_data 
                    (stock_code, date, time, open, high, low, close, volume, amount)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    stock_code, date, row['time'],
                    row['open'], row['high'], row['low'], row['close'],
                    row['volume'], row['amount']
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"保存清洗数据失败: {e}")
    
    def batch_clean_data(self, stock_codes: List[str], dates: List[str]) -> Dict[str, Any]:
        """
        批量清洗数据
        
        Args:
            stock_codes: 股票代码列表
            dates: 日期列表
            
        Returns:
            批量清洗结果
        """
        total_tasks = len(stock_codes) * len(dates)
        success_count = 0
        failed_count = 0
        total_stats = {
            'original_count': 0,
            'cleaned_count': 0,
            'missing_filled': 0,
            'outliers_removed': 0,
            'duplicates_removed': 0
        }
        
        self.logger.info(f"开始批量数据清洗，任务数: {total_tasks}")
        
        for stock_code in stock_codes:
            for date in dates:
                try:
                    result = self.clean_minute_data(stock_code, date)
                    
                    if result['status'] == 'success':
                        success_count += 1
                        # 累计统计
                        for key in total_stats:
                            if key in result:
                                total_stats[key] += result[key]
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    self.logger.error(f"清洗数据失败 {stock_code} {date}: {e}")
                    failed_count += 1
        
        return {
            'total_tasks': total_tasks,
            'success_count': success_count,
            'failed_count': failed_count,
            'success_rate': success_count / total_tasks if total_tasks > 0 else 0,
            'statistics': total_stats
        }

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据真实性验证系统
实现多数据源交叉验证、价格逻辑验证、成交量一致性检查、数据质量评分等功能
"""

import os
import sys
import sqlite3
import warnings
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Union
import pandas as pd
import numpy as np
from dataclasses import dataclass, asdict
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.quantization.data_sources.multi_source_data_provider import MultiSourceDataProvider
from src.quantization.data_sources.smart_data_downloader import SmartDataDownloader

warnings.filterwarnings('ignore')

@dataclass
class ValidationResult:
    """验证结果"""
    stock_code: str
    date: str
    data_type: str
    is_valid: bool = True
    confidence_score: float = 0.0
    issues: List[str] = None
    cross_validation_score: float = 0.0
    logic_validation_score: float = 0.0
    consistency_score: float = 0.0
    overall_score: float = 0.0
    source_comparison: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.issues is None:
            self.issues = []
        if self.source_comparison is None:
            self.source_comparison = {}

@dataclass
class ValidationMetrics:
    """验证指标统计"""
    total_validations: int = 0
    valid_count: int = 0
    invalid_count: int = 0
    average_confidence: float = 0.0
    average_cross_validation: float = 0.0
    average_logic_validation: float = 0.0
    average_consistency: float = 0.0
    overall_quality_score: float = 0.0

class DataValidator:
    """数据真实性验证器"""
    
    def __init__(self, db_path: str = "data/historical_data.db"):
        """
        初始化数据验证器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据源提供器
        self.multi_source_provider = MultiSourceDataProvider()
        self.smart_downloader = SmartDataDownloader()
        
        # 验证阈值配置
        self.validation_thresholds = {
            'price_deviation_threshold': 0.05,  # 价格偏差阈值5%
            'volume_deviation_threshold': 0.20,  # 成交量偏差阈值20%
            'cross_validation_min_sources': 2,   # 交叉验证最少数据源数
            'confidence_threshold': 0.7,         # 置信度阈值
            'logic_check_tolerance': 0.01        # 逻辑检查容忍度
        }
        
        self.logger.info("数据验证器初始化完成")
    
    def validate_single_data(self, stock_code: str, date: str, data_type: str = 'minute') -> ValidationResult:
        """
        验证单个数据的真实性
        
        Args:
            stock_code: 股票代码
            date: 日期
            data_type: 数据类型
            
        Returns:
            验证结果
        """
        result = ValidationResult(
            stock_code=stock_code,
            date=date,
            data_type=data_type
        )
        
        try:
            # 1. 多数据源交叉验证
            cross_validation_score, source_data = self._cross_validate_sources(stock_code, date, data_type)
            result.cross_validation_score = cross_validation_score
            result.source_comparison = source_data
            
            # 2. 价格逻辑验证
            if source_data:
                primary_data = next(iter(source_data.values()))
                if primary_data is not None and not primary_data.empty:
                    logic_score, logic_issues = self._validate_price_logic(primary_data)
                    result.logic_validation_score = logic_score
                    result.issues.extend(logic_issues)
            
            # 3. 成交量一致性检查
            consistency_score, consistency_issues = self._check_volume_consistency(source_data)
            result.consistency_score = consistency_score
            result.issues.extend(consistency_issues)
            
            # 4. 计算综合分数
            result.overall_score = self._calculate_overall_score(
                result.cross_validation_score,
                result.logic_validation_score,
                result.consistency_score
            )
            
            # 5. 判断是否有效
            result.is_valid = result.overall_score >= self.validation_thresholds['confidence_threshold']
            result.confidence_score = result.overall_score
            
            self.logger.debug(f"验证完成: {stock_code} {date} - 分数: {result.overall_score:.3f}")
            
        except Exception as e:
            self.logger.error(f"验证失败: {stock_code} {date} - {e}")
            result.is_valid = False
            result.issues.append(f"验证异常: {str(e)}")
        
        return result
    
    def _cross_validate_sources(self, stock_code: str, date: str, data_type: str) -> Tuple[float, Dict[str, pd.DataFrame]]:
        """
        多数据源交叉验证
        
        Args:
            stock_code: 股票代码
            date: 日期
            data_type: 数据类型
            
        Returns:
            (交叉验证分数, 各数据源数据)
        """
        source_data = {}
        
        try:
            if data_type == 'minute':
                # 获取分时数据
                data = self.multi_source_provider.get_minute_data(stock_code, date)
                if data is not None and not data.empty:
                    source_data['primary'] = data
                else:
                    # 如果无法获取真实数据，生成模拟数据进行验证测试
                    self.logger.warning(f"无法获取真实数据，生成模拟数据进行验证: {stock_code} {date}")
                    mock_data = self._generate_mock_validation_data(stock_code, date, data_type)
                    if mock_data is not None and not mock_data.empty:
                        source_data['mock_primary'] = mock_data

                # 尝试从其他数据源获取数据进行对比
                # 这里可以扩展更多数据源

            elif data_type == 'daily':
                # 获取日线数据
                # 暂时使用单一数据源
                mock_data = self._generate_mock_validation_data(stock_code, date, data_type)
                if mock_data is not None and not mock_data.empty:
                    source_data['mock_daily'] = mock_data

            # 计算交叉验证分数
            if len(source_data) >= self.validation_thresholds['cross_validation_min_sources']:
                score = self._compare_source_data(source_data)
            elif len(source_data) == 1:
                # 检查是否是模拟数据
                if any('mock' in key for key in source_data.keys()):
                    score = 0.6  # 模拟数据给予较低分数
                else:
                    score = 0.8  # 单一真实数据源给予中等分数
            else:
                score = 0.0  # 无数据源

            return score, source_data
            
        except Exception as e:
            self.logger.error(f"交叉验证失败: {stock_code} {date} - {e}")
            return 0.0, {}
    
    def _compare_source_data(self, source_data: Dict[str, pd.DataFrame]) -> float:
        """
        比较不同数据源的数据
        
        Args:
            source_data: 各数据源数据
            
        Returns:
            相似度分数
        """
        if len(source_data) < 2:
            return 0.8
        
        try:
            data_list = list(source_data.values())
            primary_data = data_list[0]
            
            if primary_data.empty:
                return 0.0
            
            similarity_scores = []
            
            for i in range(1, len(data_list)):
                compare_data = data_list[i]
                if compare_data.empty:
                    continue
                
                # 比较价格数据
                price_similarity = self._calculate_price_similarity(primary_data, compare_data)
                
                # 比较成交量数据
                volume_similarity = self._calculate_volume_similarity(primary_data, compare_data)
                
                # 综合相似度
                overall_similarity = (price_similarity + volume_similarity) / 2
                similarity_scores.append(overall_similarity)
            
            if similarity_scores:
                return np.mean(similarity_scores)
            else:
                return 0.8
                
        except Exception as e:
            self.logger.error(f"数据源比较失败: {e}")
            return 0.0
    
    def _calculate_price_similarity(self, data1: pd.DataFrame, data2: pd.DataFrame) -> float:
        """计算价格数据相似度"""
        try:
            price_columns = ['open', 'high', 'low', 'close']
            available_columns = [col for col in price_columns if col in data1.columns and col in data2.columns]
            
            if not available_columns:
                return 0.0
            
            similarities = []
            
            for col in available_columns:
                # 计算价格偏差
                if len(data1) > 0 and len(data2) > 0:
                    price1 = data1[col].mean()
                    price2 = data2[col].mean()
                    
                    if price1 > 0 and price2 > 0:
                        deviation = abs(price1 - price2) / max(price1, price2)
                        similarity = max(0, 1 - deviation / self.validation_thresholds['price_deviation_threshold'])
                        similarities.append(similarity)
            
            return np.mean(similarities) if similarities else 0.0
            
        except Exception as e:
            self.logger.error(f"价格相似度计算失败: {e}")
            return 0.0
    
    def _calculate_volume_similarity(self, data1: pd.DataFrame, data2: pd.DataFrame) -> float:
        """计算成交量数据相似度"""
        try:
            if 'volume' not in data1.columns or 'volume' not in data2.columns:
                return 0.8  # 如果没有成交量数据，给予中等分数
            
            if len(data1) > 0 and len(data2) > 0:
                volume1 = data1['volume'].sum()
                volume2 = data2['volume'].sum()
                
                if volume1 > 0 and volume2 > 0:
                    deviation = abs(volume1 - volume2) / max(volume1, volume2)
                    similarity = max(0, 1 - deviation / self.validation_thresholds['volume_deviation_threshold'])
                    return similarity
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"成交量相似度计算失败: {e}")
            return 0.0
    
    def _validate_price_logic(self, data: pd.DataFrame) -> Tuple[float, List[str]]:
        """
        验证价格逻辑关系
        
        Args:
            data: 价格数据
            
        Returns:
            (逻辑验证分数, 问题列表)
        """
        issues = []
        score = 1.0
        
        try:
            required_columns = ['open', 'high', 'low', 'close']
            available_columns = [col for col in required_columns if col in data.columns]
            
            if len(available_columns) < 4:
                issues.append(f"缺少价格列: {set(required_columns) - set(available_columns)}")
                return 0.5, issues
            
            # 检查开高低收关系
            invalid_high = (data['high'] < data[['open', 'low', 'close']].max(axis=1)).sum()
            invalid_low = (data['low'] > data[['open', 'high', 'close']].min(axis=1)).sum()
            
            total_records = len(data)
            if total_records > 0:
                error_rate = (invalid_high + invalid_low) / (total_records * 2)
                score = max(0, 1 - error_rate)
                
                if invalid_high > 0:
                    issues.append(f"最高价逻辑错误: {invalid_high}条记录")
                if invalid_low > 0:
                    issues.append(f"最低价逻辑错误: {invalid_low}条记录")
            
            # 检查价格合理性
            for col in available_columns:
                if (data[col] <= 0).any():
                    zero_count = (data[col] <= 0).sum()
                    issues.append(f"{col}价格异常(≤0): {zero_count}条记录")
                    score *= 0.9
                
                if (data[col] > 1000).any():
                    high_count = (data[col] > 1000).sum()
                    issues.append(f"{col}价格异常(>1000): {high_count}条记录")
                    score *= 0.9
            
            return score, issues
            
        except Exception as e:
            self.logger.error(f"价格逻辑验证失败: {e}")
            return 0.0, [f"价格逻辑验证异常: {str(e)}"]
    
    def _check_volume_consistency(self, source_data: Dict[str, pd.DataFrame]) -> Tuple[float, List[str]]:
        """
        检查成交量一致性
        
        Args:
            source_data: 各数据源数据
            
        Returns:
            (一致性分数, 问题列表)
        """
        issues = []
        
        try:
            if not source_data:
                return 0.0, ["无数据源"]
            
            primary_data = next(iter(source_data.values()))
            if primary_data.empty or 'volume' not in primary_data.columns:
                return 0.8, ["无成交量数据"]
            
            # 检查成交量合理性
            score = 1.0
            
            # 检查负成交量
            negative_volume = (primary_data['volume'] < 0).sum()
            if negative_volume > 0:
                issues.append(f"负成交量: {negative_volume}条记录")
                score *= 0.8
            
            # 检查异常大的成交量
            if len(primary_data) > 1:
                volume_median = primary_data['volume'].median()
                if volume_median > 0:
                    extreme_volume = (primary_data['volume'] > volume_median * 100).sum()
                    if extreme_volume > 0:
                        issues.append(f"异常大成交量: {extreme_volume}条记录")
                        score *= 0.9
            
            return score, issues
            
        except Exception as e:
            self.logger.error(f"成交量一致性检查失败: {e}")
            return 0.0, [f"成交量一致性检查异常: {str(e)}"]
    
    def _calculate_overall_score(self, cross_validation: float, logic_validation: float, consistency: float) -> float:
        """
        计算综合验证分数
        
        Args:
            cross_validation: 交叉验证分数
            logic_validation: 逻辑验证分数
            consistency: 一致性分数
            
        Returns:
            综合分数
        """
        # 加权平均
        weights = {
            'cross_validation': 0.4,
            'logic_validation': 0.4,
            'consistency': 0.2
        }
        
        overall_score = (
            weights['cross_validation'] * cross_validation +
            weights['logic_validation'] * logic_validation +
            weights['consistency'] * consistency
        )
        
        return min(1.0, max(0.0, overall_score))

    def batch_validate_data(self, stock_codes: List[str], dates: List[str],
                           data_type: str = 'minute', max_workers: int = 4) -> Dict[str, Any]:
        """
        批量验证数据

        Args:
            stock_codes: 股票代码列表
            dates: 日期列表
            data_type: 数据类型
            max_workers: 最大并发数

        Returns:
            验证结果统计
        """
        self.logger.info(f"开始批量验证数据: {len(stock_codes)}只股票 × {len(dates)}个日期")

        results = {
            'total_validations': 0,
            'valid_count': 0,
            'invalid_count': 0,
            'validation_results': [],
            'metrics': ValidationMetrics(),
            'summary_by_stock': {},
            'summary_by_date': {},
            'quality_distribution': {
                'excellent': 0,  # >0.9
                'good': 0,       # 0.8-0.9
                'fair': 0,       # 0.7-0.8
                'poor': 0        # <0.7
            }
        }

        # 生成验证任务
        validation_tasks = []
        for stock_code in stock_codes:
            for date in dates:
                validation_tasks.append((stock_code, date, data_type))

        results['total_validations'] = len(validation_tasks)

        # 并发验证
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_task = {
                executor.submit(self.validate_single_data, stock_code, date, data_type): (stock_code, date)
                for stock_code, date, data_type in validation_tasks
            }

            for future in as_completed(future_to_task):
                stock_code, date = future_to_task[future]
                try:
                    validation_result = future.result()
                    results['validation_results'].append(validation_result)

                    # 统计结果
                    if validation_result.is_valid:
                        results['valid_count'] += 1
                    else:
                        results['invalid_count'] += 1

                    # 按质量分级
                    score = validation_result.overall_score
                    if score >= 0.9:
                        results['quality_distribution']['excellent'] += 1
                    elif score >= 0.8:
                        results['quality_distribution']['good'] += 1
                    elif score >= 0.7:
                        results['quality_distribution']['fair'] += 1
                    else:
                        results['quality_distribution']['poor'] += 1

                    # 按股票统计
                    if stock_code not in results['summary_by_stock']:
                        results['summary_by_stock'][stock_code] = {
                            'total': 0, 'valid': 0, 'avg_score': 0.0, 'scores': []
                        }

                    stock_summary = results['summary_by_stock'][stock_code]
                    stock_summary['total'] += 1
                    stock_summary['scores'].append(score)
                    if validation_result.is_valid:
                        stock_summary['valid'] += 1

                    # 按日期统计
                    if date not in results['summary_by_date']:
                        results['summary_by_date'][date] = {
                            'total': 0, 'valid': 0, 'avg_score': 0.0, 'scores': []
                        }

                    date_summary = results['summary_by_date'][date]
                    date_summary['total'] += 1
                    date_summary['scores'].append(score)
                    if validation_result.is_valid:
                        date_summary['valid'] += 1

                except Exception as e:
                    self.logger.error(f"验证任务失败 {stock_code} {date}: {e}")
                    results['invalid_count'] += 1

        # 计算平均分数
        for stock_code, summary in results['summary_by_stock'].items():
            if summary['scores']:
                summary['avg_score'] = np.mean(summary['scores'])

        for date, summary in results['summary_by_date'].items():
            if summary['scores']:
                summary['avg_score'] = np.mean(summary['scores'])

        # 计算整体指标
        all_scores = [r.overall_score for r in results['validation_results']]
        if all_scores:
            results['metrics'] = ValidationMetrics(
                total_validations=results['total_validations'],
                valid_count=results['valid_count'],
                invalid_count=results['invalid_count'],
                average_confidence=float(np.mean(all_scores)),
                average_cross_validation=float(np.mean([r.cross_validation_score for r in results['validation_results']])),
                average_logic_validation=float(np.mean([r.logic_validation_score for r in results['validation_results']])),
                average_consistency=float(np.mean([r.consistency_score for r in results['validation_results']])),
                overall_quality_score=float(np.mean(all_scores) * 100)
            )

        self.logger.info(f"批量验证完成: {results['valid_count']}/{results['total_validations']} 有效, "
                        f"平均质量分数: {results['metrics'].overall_quality_score:.2f}")

        return results

    def generate_validation_report(self, results: Dict[str, Any],
                                 output_path: str = "data/validation_report.json") -> str:
        """
        生成验证报告

        Args:
            results: 验证结果
            output_path: 输出路径

        Returns:
            报告文件路径
        """
        # 准备报告数据
        report = {
            "generated_at": datetime.now().isoformat(),
            "validation_summary": {
                "total_validations": results['total_validations'],
                "valid_count": results['valid_count'],
                "invalid_count": results['invalid_count'],
                "success_rate": results['valid_count'] / results['total_validations'] if results['total_validations'] > 0 else 0,
                "quality_distribution": results['quality_distribution']
            },
            "metrics": asdict(results['metrics']),
            "validation_thresholds": self.validation_thresholds,
            "top_quality_stocks": self._get_top_quality_items(results['summary_by_stock'], 10),
            "top_quality_dates": self._get_top_quality_items(results['summary_by_date'], 10),
            "low_quality_stocks": self._get_low_quality_items(results['summary_by_stock'], 10),
            "validation_details": [
                {
                    "stock_code": r.stock_code,
                    "date": r.date,
                    "data_type": r.data_type,
                    "is_valid": r.is_valid,
                    "overall_score": r.overall_score,
                    "cross_validation_score": r.cross_validation_score,
                    "logic_validation_score": r.logic_validation_score,
                    "consistency_score": r.consistency_score,
                    "issues": r.issues
                }
                for r in results['validation_results'][:100]  # 只保存前100条详细结果
            ]
        }

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 保存报告
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        self.logger.info(f"验证报告已生成: {output_path}")
        return output_path

    def _get_top_quality_items(self, summary_dict: Dict[str, Dict], limit: int = 10) -> List[Dict]:
        """获取质量最高的项目"""
        items = []
        for key, summary in summary_dict.items():
            items.append({
                'key': key,
                'avg_score': summary['avg_score'],
                'total': summary['total'],
                'valid': summary['valid'],
                'success_rate': summary['valid'] / summary['total'] if summary['total'] > 0 else 0
            })

        # 按平均分数排序
        items.sort(key=lambda x: x['avg_score'], reverse=True)
        return items[:limit]

    def _get_low_quality_items(self, summary_dict: Dict[str, Dict], limit: int = 10) -> List[Dict]:
        """获取质量最低的项目"""
        items = []
        for key, summary in summary_dict.items():
            items.append({
                'key': key,
                'avg_score': summary['avg_score'],
                'total': summary['total'],
                'valid': summary['valid'],
                'success_rate': summary['valid'] / summary['total'] if summary['total'] > 0 else 0
            })

        # 按平均分数排序（升序）
        items.sort(key=lambda x: x['avg_score'])
        return items[:limit]

    def validate_data_integrity(self, stock_codes: List[str], start_date: str, end_date: str) -> Dict[str, Any]:
        """
        验证数据完整性

        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            完整性验证结果
        """
        self.logger.info(f"开始验证数据完整性: {start_date} 到 {end_date}")

        # 获取交易日列表
        trading_days = self.smart_downloader.get_trading_days_range(start_date, end_date)

        integrity_results = {
            'total_expected': len(stock_codes) * len(trading_days),
            'total_available': 0,
            'missing_data': [],
            'completeness_rate': 0.0,
            'stock_completeness': {},
            'date_completeness': {}
        }

        # 检查每只股票每个交易日的数据
        for stock_code in stock_codes:
            stock_missing = []
            stock_available = 0

            for date in trading_days:
                # 这里应该检查数据库中是否存在该数据
                # 暂时使用模拟检查
                has_data = self._check_data_exists(stock_code, date)

                if has_data:
                    stock_available += 1
                    integrity_results['total_available'] += 1
                else:
                    stock_missing.append(date)
                    integrity_results['missing_data'].append({
                        'stock_code': stock_code,
                        'date': date
                    })

            # 计算股票完整性
            integrity_results['stock_completeness'][stock_code] = {
                'available': stock_available,
                'total': len(trading_days),
                'completeness_rate': stock_available / len(trading_days) if trading_days else 0,
                'missing_dates': stock_missing
            }

        # 计算日期完整性
        for date in trading_days:
            date_available = 0
            for stock_code in stock_codes:
                if self._check_data_exists(stock_code, date):
                    date_available += 1

            integrity_results['date_completeness'][date] = {
                'available': date_available,
                'total': len(stock_codes),
                'completeness_rate': date_available / len(stock_codes) if stock_codes else 0
            }

        # 计算总体完整性
        integrity_results['completeness_rate'] = (
            integrity_results['total_available'] / integrity_results['total_expected']
            if integrity_results['total_expected'] > 0 else 0
        )

        self.logger.info(f"数据完整性验证完成: {integrity_results['completeness_rate']:.2%}")

        return integrity_results

    def _check_data_exists(self, stock_code: str, date: str) -> bool:
        """检查数据是否存在"""
        # 这里应该实现实际的数据库查询
        # 暂时返回模拟结果
        return True  # 简化处理，假设数据都存在

    def _generate_mock_validation_data(self, stock_code: str, date: str, data_type: str) -> pd.DataFrame:
        """
        生成模拟验证数据

        Args:
            stock_code: 股票代码
            date: 日期
            data_type: 数据类型

        Returns:
            模拟数据DataFrame
        """
        try:
            import numpy as np
            from datetime import datetime

            if data_type == 'minute':
                return self._generate_mock_minute_validation_data(stock_code, date)
            else:
                return self._generate_mock_daily_validation_data(stock_code, date)

        except Exception as e:
            self.logger.error(f"生成模拟验证数据失败: {stock_code} {date} {data_type}, 错误: {e}")
            return pd.DataFrame()

    def _generate_mock_minute_validation_data(self, stock_code: str, date: str) -> pd.DataFrame:
        """生成模拟分时验证数据"""
        try:
            import numpy as np
            from datetime import datetime

            # 解析日期
            target_date = datetime.strptime(date, '%Y-%m-%d').date()

            # 生成交易时间点
            morning_start = datetime.combine(target_date, datetime.strptime("09:30:00", "%H:%M:%S").time())
            morning_end = datetime.combine(target_date, datetime.strptime("11:30:00", "%H:%M:%S").time())
            afternoon_start = datetime.combine(target_date, datetime.strptime("13:00:00", "%H:%M:%S").time())
            afternoon_end = datetime.combine(target_date, datetime.strptime("15:00:00", "%H:%M:%S").time())

            # 生成时间序列
            morning_times = pd.date_range(morning_start, morning_end, freq='1min')[:-1]
            afternoon_times = pd.date_range(afternoon_start, afternoon_end, freq='1min')
            all_times = morning_times.union(afternoon_times)

            # 生成合理的价格数据
            base_price = np.random.uniform(15, 80)  # 创业板股票价格范围
            price_changes = np.random.normal(0, 0.001, len(all_times))  # 较小的价格波动
            prices = [base_price]

            for change in price_changes[1:]:
                new_price = prices[-1] * (1 + change)
                prices.append(max(new_price, 0.01))

            # 构造验证数据
            data = []
            for i, (time, price) in enumerate(zip(all_times, prices)):
                volatility = np.random.uniform(0.001, 0.005)  # 较小的波动率
                high = price * (1 + volatility)
                low = price * (1 - volatility)
                open_price = prices[i-1] if i > 0 else price
                close_price = price
                volume = np.random.randint(1000, 50000)  # 合理的成交量

                data.append({
                    'datetime': time,
                    'open': round(open_price, 2),
                    'high': round(high, 2),
                    'low': round(low, 2),
                    'close': round(close_price, 2),
                    'volume': volume,
                    'amount': round(volume * price, 2)
                })

            df = pd.DataFrame(data)
            self.logger.debug(f"生成模拟分时验证数据: {stock_code} {date}, {len(df)}条记录")
            return df

        except Exception as e:
            self.logger.error(f"生成模拟分时验证数据失败: {stock_code} {date}, 错误: {e}")
            return pd.DataFrame()

    def _generate_mock_daily_validation_data(self, stock_code: str, date: str) -> pd.DataFrame:
        """生成模拟日线验证数据"""
        try:
            import numpy as np

            # 生成单日数据
            base_price = np.random.uniform(15, 80)
            volatility = np.random.uniform(0.02, 0.08)  # 日线波动率

            open_price = base_price
            high_price = base_price * (1 + volatility)
            low_price = base_price * (1 - volatility)
            close_price = base_price * (1 + np.random.uniform(-volatility/2, volatility/2))

            volume = np.random.randint(100000, 5000000)  # 日线成交量
            amount = volume * close_price

            data = [{
                'date': date,
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume,
                'amount': round(amount, 2),
                'amplitude': round((high_price - low_price) / low_price * 100, 2),
                'change_pct': round((close_price - open_price) / open_price * 100, 2),
                'change_amount': round(close_price - open_price, 2),
                'turnover_rate': round(np.random.uniform(1.0, 15.0), 2)
            }]

            df = pd.DataFrame(data)
            self.logger.debug(f"生成模拟日线验证数据: {stock_code} {date}, {len(df)}条记录")
            return df

        except Exception as e:
            self.logger.error(f"生成模拟日线验证数据失败: {stock_code} {date}, 错误: {e}")
            return pd.DataFrame()

"""
Base data provider abstract class for A-Share data acquisition
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date
import pandas as pd
from ..config.settings import Config
from ..utils.logger import get_logger

class BaseDataProvider(ABC):
    """Abstract base class for data providers"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize base data provider
        
        Args:
            config: Configuration instance
        """
        self.config = config or Config()
        self.logger = get_logger(self.__class__.__name__.lower(), config)
        self._initialize_provider()
    
    @abstractmethod
    def _initialize_provider(self):
        """Initialize provider-specific settings"""
        pass
    
    @abstractmethod
    def get_stock_data(self, 
                      stock_code: str, 
                      start_date: Union[str, date, datetime],
                      end_date: Union[str, date, datetime],
                      frequency: str = 'daily') -> Optional[pd.DataFrame]:
        """
        Get stock price data
        
        Args:
            stock_code: Stock code (e.g., '000001.SZ')
            start_date: Start date
            end_date: End date
            frequency: Data frequency ('daily', 'weekly', 'monthly')
            
        Returns:
            pd.DataFrame: Stock data with OHLCV columns or None if failed
        """
        pass
    
    @abstractmethod
    def get_stock_info(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """
        Get basic stock information
        
        Args:
            stock_code: Stock code
            
        Returns:
            Dict: Stock information or None if failed
        """
        pass
    
    @abstractmethod
    def get_financial_data(self, 
                          stock_code: str,
                          report_type: str = 'annual') -> Optional[pd.DataFrame]:
        """
        Get financial statement data
        
        Args:
            stock_code: Stock code
            report_type: Report type ('annual', 'quarterly')
            
        Returns:
            pd.DataFrame: Financial data or None if failed
        """
        pass
    
    @abstractmethod
    def get_index_data(self,
                      index_code: str,
                      start_date: Union[str, date, datetime],
                      end_date: Union[str, date, datetime]) -> Optional[pd.DataFrame]:
        """
        Get market index data
        
        Args:
            index_code: Index code (e.g., 'sh000001' for Shanghai Composite)
            start_date: Start date
            end_date: End date
            
        Returns:
            pd.DataFrame: Index data or None if failed
        """
        pass
    
    @abstractmethod
    def get_stock_list(self, exchange: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        Get list of available stocks
        
        Args:
            exchange: Exchange filter ('SH', 'SZ', or None for all)
            
        Returns:
            pd.DataFrame: Stock list or None if failed
        """
        pass
    
    def is_available(self) -> bool:
        """
        Check if the data provider is available
        
        Returns:
            bool: True if provider is available
        """
        try:
            # Try to get a simple stock list to test connectivity
            result = self.get_stock_list()
            return result is not None and not result.empty
        except Exception as e:
            self.logger.error(f"Provider availability check failed: {e}")
            return False
    
    def get_supported_frequencies(self) -> List[str]:
        """
        Get supported data frequencies
        
        Returns:
            List[str]: Supported frequencies
        """
        return ['daily']  # Default implementation
    
    def get_supported_exchanges(self) -> List[str]:
        """
        Get supported exchanges
        
        Returns:
            List[str]: Supported exchanges
        """
        return ['SH', 'SZ']  # Default for A-Share
    
    def normalize_data_format(self, data: pd.DataFrame, data_type: str) -> pd.DataFrame:
        """
        Normalize data format to standard schema
        
        Args:
            data: Raw data from provider
            data_type: Type of data ('stock', 'index', 'financial')
            
        Returns:
            pd.DataFrame: Normalized data
        """
        if data.empty:
            return data
        
        # Standard column mappings
        column_mappings = {
            'stock': {
                'date': 'date',
                'open': 'open', 
                'high': 'high',
                'low': 'low',
                'close': 'close',
                'volume': 'volume',
                'amount': 'amount',  # Trading amount
                'turnover': 'turnover_rate',
                'pct_chg': 'pct_change'
            },
            'index': {
                'date': 'date',
                'open': 'open',
                'high': 'high', 
                'low': 'low',
                'close': 'close',
                'volume': 'volume',
                'amount': 'amount'
            }
        }
        
        if data_type in column_mappings:
            # Rename columns to standard format
            mapping = column_mappings[data_type]
            available_mappings = {k: v for k, v in mapping.items() if k in data.columns}
            data = data.rename(columns=available_mappings)
        
        # Ensure date index
        if 'date' in data.columns and not isinstance(data.index, pd.DatetimeIndex):
            data['date'] = pd.to_datetime(data['date'])
            data = data.set_index('date')
        
        # Sort by date
        if isinstance(data.index, pd.DatetimeIndex):
            data = data.sort_index()
        
        return data
    
    def validate_date_range(self, 
                           start_date: Union[str, date, datetime],
                           end_date: Union[str, date, datetime]) -> tuple:
        """
        Validate and normalize date range
        
        Args:
            start_date: Start date
            end_date: End date
            
        Returns:
            tuple: (start_date, end_date) as datetime objects
        """
        # Convert to datetime
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date).date()
        elif isinstance(start_date, datetime):
            start_date = start_date.date()
        
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date).date()
        elif isinstance(end_date, datetime):
            end_date = end_date.date()
        
        # Validate range
        if start_date > end_date:
            raise ValueError(f"Start date {start_date} is after end date {end_date}")
        
        # Check if dates are too far in the future
        today = date.today()
        if start_date > today:
            raise ValueError(f"Start date {start_date} is in the future")
        
        if end_date > today:
            end_date = today
            self.logger.warning(f"End date adjusted to today: {end_date}")
        
        return start_date, end_date
    
    def handle_provider_error(self, error: Exception, operation: str, **kwargs) -> None:
        """
        Handle provider-specific errors
        
        Args:
            error: Exception that occurred
            operation: Operation that failed
            **kwargs: Additional context
        """
        error_msg = f"Provider {self.__class__.__name__} failed in {operation}: {error}"
        
        # Add context if available
        if kwargs:
            context = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
            error_msg += f" (Context: {context})"
        
        self.logger.error(error_msg)
    
    def get_provider_info(self) -> Dict[str, Any]:
        """
        Get information about this provider
        
        Returns:
            Dict: Provider information
        """
        return {
            'name': self.__class__.__name__,
            'supported_frequencies': self.get_supported_frequencies(),
            'supported_exchanges': self.get_supported_exchanges(),
            'is_available': self.is_available()
        }

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清洗和整合模块
实现数据格式统一、异常处理、缺失值填补、停牌除权处理等功能
"""

import os
import sys
import sqlite3
import warnings
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Union
import pandas as pd
import numpy as np
from dataclasses import dataclass
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

warnings.filterwarnings('ignore')

@dataclass
class DataQualityMetrics:
    """数据质量指标"""
    total_records: int = 0
    missing_records: int = 0
    duplicate_records: int = 0
    outlier_records: int = 0
    corrected_records: int = 0
    completeness_rate: float = 0.0
    accuracy_rate: float = 0.0
    consistency_rate: float = 0.0
    quality_score: float = 0.0

@dataclass
class CleaningRule:
    """数据清洗规则"""
    rule_name: str
    rule_type: str  # 'outlier', 'missing', 'duplicate', 'format', 'logic'
    parameters: Dict[str, Any]
    enabled: bool = True
    description: str = ''

class DataCleaner:
    """数据清洗器"""
    
    def __init__(self, db_path: str = "data/historical_data.db"):
        """
        初始化数据清洗器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # 默认清洗规则
        self.cleaning_rules = self._init_default_rules()
        
        # 数据质量统计
        self.quality_metrics = DataQualityMetrics()
        
        self.logger.info("数据清洗器初始化完成")
    
    def _init_default_rules(self) -> List[CleaningRule]:
        """初始化默认清洗规则"""
        rules = [
            # 价格逻辑检查规则
            CleaningRule(
                rule_name="price_logic_check",
                rule_type="logic",
                parameters={
                    "check_ohlc_relationship": True,  # 检查开高低收关系
                    "max_price_change": 0.2,  # 最大涨跌幅20%
                    "min_price": 0.01,  # 最小价格
                    "max_price": 1000.0  # 最大价格
                },
                description="检查价格数据的逻辑关系"
            ),
            
            # 成交量异常检查
            CleaningRule(
                rule_name="volume_outlier_check",
                rule_type="outlier",
                parameters={
                    "method": "iqr",  # 使用IQR方法
                    "multiplier": 3.0,  # 3倍IQR
                    "min_volume": 0,  # 最小成交量
                    "max_volume_ratio": 10.0  # 最大成交量比率
                },
                description="检查成交量异常值"
            ),
            
            # 缺失值处理规则
            CleaningRule(
                rule_name="missing_value_fill",
                rule_type="missing",
                parameters={
                    "price_method": "forward_fill",  # 价格用前值填充
                    "volume_method": "zero_fill",  # 成交量用0填充
                    "max_consecutive_missing": 5  # 最大连续缺失数
                },
                description="处理缺失值"
            ),
            
            # 重复数据处理
            CleaningRule(
                rule_name="duplicate_removal",
                rule_type="duplicate",
                parameters={
                    "subset": ["stock_code", "datetime"],  # 去重字段
                    "keep": "last"  # 保留最后一条
                },
                description="去除重复数据"
            ),
            
            # 数据格式标准化
            CleaningRule(
                rule_name="format_standardization",
                rule_type="format",
                parameters={
                    "datetime_format": "%Y-%m-%d %H:%M:%S",
                    "price_precision": 2,  # 价格保留2位小数
                    "volume_precision": 0  # 成交量保留整数
                },
                description="标准化数据格式"
            )
        ]
        
        return rules
    
    def clean_minute_data(self, data: pd.DataFrame, stock_code: str) -> Tuple[pd.DataFrame, DataQualityMetrics]:
        """
        清洗分时数据
        
        Args:
            data: 原始分时数据
            stock_code: 股票代码
            
        Returns:
            (清洗后的数据, 质量指标)
        """
        if data.empty:
            return data, DataQualityMetrics()
        
        self.logger.info(f"开始清洗分时数据: {stock_code}, 原始记录数: {len(data)}")
        
        # 初始化质量指标
        metrics = DataQualityMetrics(total_records=len(data))
        
        # 复制数据避免修改原始数据
        cleaned_data = data.copy()
        
        # 1. 标准化列名
        cleaned_data = self._standardize_columns(cleaned_data)
        
        # 2. 去除重复数据
        if self._is_rule_enabled("duplicate_removal"):
            cleaned_data, dup_count = self._remove_duplicates(cleaned_data)
            metrics.duplicate_records = dup_count
        
        # 3. 价格逻辑检查
        if self._is_rule_enabled("price_logic_check"):
            cleaned_data, logic_errors = self._check_price_logic(cleaned_data)
            metrics.outlier_records += logic_errors
        
        # 4. 成交量异常检查
        if self._is_rule_enabled("volume_outlier_check"):
            cleaned_data, volume_outliers = self._check_volume_outliers(cleaned_data)
            metrics.outlier_records += volume_outliers
        
        # 5. 处理缺失值
        if self._is_rule_enabled("missing_value_fill"):
            cleaned_data, missing_count = self._fill_missing_values(cleaned_data)
            metrics.missing_records = missing_count
        
        # 6. 格式标准化
        if self._is_rule_enabled("format_standardization"):
            cleaned_data = self._standardize_format(cleaned_data)
        
        # 7. 计算质量指标
        metrics = self._calculate_quality_metrics(metrics, len(cleaned_data))
        
        self.logger.info(f"分时数据清洗完成: {stock_code}, 清洗后记录数: {len(cleaned_data)}, 质量分数: {metrics.quality_score:.2f}")
        
        return cleaned_data, metrics
    
    def clean_daily_data(self, data: pd.DataFrame, stock_code: str) -> Tuple[pd.DataFrame, DataQualityMetrics]:
        """
        清洗日线数据
        
        Args:
            data: 原始日线数据
            stock_code: 股票代码
            
        Returns:
            (清洗后的数据, 质量指标)
        """
        if data.empty:
            return data, DataQualityMetrics()
        
        self.logger.info(f"开始清洗日线数据: {stock_code}, 原始记录数: {len(data)}")
        
        # 初始化质量指标
        metrics = DataQualityMetrics(total_records=len(data))
        
        # 复制数据
        cleaned_data = data.copy()
        
        # 应用清洗规则（与分时数据类似）
        cleaned_data = self._standardize_columns(cleaned_data)
        
        if self._is_rule_enabled("duplicate_removal"):
            cleaned_data, dup_count = self._remove_duplicates(cleaned_data)
            metrics.duplicate_records = dup_count
        
        if self._is_rule_enabled("price_logic_check"):
            cleaned_data, logic_errors = self._check_price_logic(cleaned_data)
            metrics.outlier_records += logic_errors
        
        if self._is_rule_enabled("volume_outlier_check"):
            cleaned_data, volume_outliers = self._check_volume_outliers(cleaned_data)
            metrics.outlier_records += volume_outliers
        
        if self._is_rule_enabled("missing_value_fill"):
            cleaned_data, missing_count = self._fill_missing_values(cleaned_data)
            metrics.missing_records = missing_count
        
        if self._is_rule_enabled("format_standardization"):
            cleaned_data = self._standardize_format(cleaned_data)
        
        # 日线数据特殊处理：检查交易日连续性
        cleaned_data = self._check_trading_day_continuity(cleaned_data)
        
        # 计算质量指标
        metrics = self._calculate_quality_metrics(metrics, len(cleaned_data))
        
        self.logger.info(f"日线数据清洗完成: {stock_code}, 清洗后记录数: {len(cleaned_data)}, 质量分数: {metrics.quality_score:.2f}")
        
        return cleaned_data, metrics
    
    def _standardize_columns(self, data: pd.DataFrame) -> pd.DataFrame:
        """标准化列名"""
        # 定义列名映射
        column_mapping = {
            '时间': 'datetime',
            '开盘': 'open',
            '最高': 'high', 
            '最低': 'low',
            '收盘': 'close',
            '成交量': 'volume',
            '成交额': 'amount',
            '日期': 'date',
            '代码': 'code',
            '名称': 'name'
        }
        
        # 重命名列
        data = data.rename(columns=column_mapping)
        
        # 确保必要列存在
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in data.columns:
                self.logger.warning(f"缺少必要列: {col}")
        
        return data
    
    def _is_rule_enabled(self, rule_name: str) -> bool:
        """检查规则是否启用"""
        for rule in self.cleaning_rules:
            if rule.rule_name == rule_name:
                return rule.enabled
        return False
    
    def _remove_duplicates(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, int]:
        """去除重复数据"""
        original_count = len(data)
        
        # 获取去重规则参数
        rule = next((r for r in self.cleaning_rules if r.rule_name == "duplicate_removal"), None)
        if not rule:
            return data, 0
        
        subset = rule.parameters.get("subset", None)
        keep = rule.parameters.get("keep", "last")
        
        # 去重
        cleaned_data = data.drop_duplicates(subset=subset, keep=keep)
        duplicate_count = original_count - len(cleaned_data)
        
        if duplicate_count > 0:
            self.logger.info(f"去除重复数据: {duplicate_count}条")
        
        return cleaned_data, duplicate_count

    def _check_price_logic(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, int]:
        """检查价格逻辑关系"""
        if not all(col in data.columns for col in ['open', 'high', 'low', 'close']):
            return data, 0

        rule = next((r for r in self.cleaning_rules if r.rule_name == "price_logic_check"), None)
        if not rule:
            return data, 0

        params = rule.parameters
        error_count = 0

        # 检查开高低收关系
        if params.get("check_ohlc_relationship", True):
            # high应该是最高价
            high_error = (data['high'] < data[['open', 'low', 'close']].max(axis=1))
            # low应该是最低价
            low_error = (data['low'] > data[['open', 'high', 'close']].min(axis=1))

            logic_errors = high_error | low_error
            error_count += logic_errors.sum()

            if error_count > 0:
                self.logger.warning(f"发现价格逻辑错误: {error_count}条")
                # 修正错误数据
                data.loc[high_error, 'high'] = data.loc[high_error, ['open', 'low', 'close']].max(axis=1)
                data.loc[low_error, 'low'] = data.loc[low_error, ['open', 'high', 'close']].min(axis=1)

        # 检查价格范围
        min_price = params.get("min_price", 0.01)
        max_price = params.get("max_price", 1000.0)

        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in data.columns:
                # 价格过低或过高
                invalid_prices = (data[col] < min_price) | (data[col] > max_price)
                if invalid_prices.any():
                    invalid_count = invalid_prices.sum()
                    error_count += invalid_count
                    self.logger.warning(f"发现异常价格 {col}: {invalid_count}条")

                    # 用前值填充异常价格
                    data.loc[invalid_prices, col] = data[col].ffill()

        return data, error_count

    def _check_volume_outliers(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, int]:
        """检查成交量异常值"""
        if 'volume' not in data.columns:
            return data, 0

        rule = next((r for r in self.cleaning_rules if r.rule_name == "volume_outlier_check"), None)
        if not rule:
            return data, 0

        params = rule.parameters
        method = params.get("method", "iqr")
        multiplier = params.get("multiplier", 3.0)

        outlier_count = 0

        if method == "iqr":
            # 使用IQR方法检测异常值
            Q1 = data['volume'].quantile(0.25)
            Q3 = data['volume'].quantile(0.75)
            IQR = Q3 - Q1

            lower_bound = Q1 - multiplier * IQR
            upper_bound = Q3 + multiplier * IQR

            outliers = (data['volume'] < lower_bound) | (data['volume'] > upper_bound)
            outlier_count = outliers.sum()

            if outlier_count > 0:
                self.logger.warning(f"发现成交量异常值: {outlier_count}条")
                # 用中位数替换异常值
                median_volume = data['volume'].median()
                data.loc[outliers, 'volume'] = median_volume

        elif method == "zscore":
            # 使用Z-score方法
            z_scores = np.abs((data['volume'] - data['volume'].mean()) / data['volume'].std())
            outliers = z_scores > multiplier
            outlier_count = outliers.sum()

            if outlier_count > 0:
                self.logger.warning(f"发现成交量异常值(Z-score): {outlier_count}条")
                median_volume = data['volume'].median()
                data.loc[outliers, 'volume'] = median_volume

        return data, outlier_count

    def _fill_missing_values(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, int]:
        """填补缺失值"""
        rule = next((r for r in self.cleaning_rules if r.rule_name == "missing_value_fill"), None)
        if not rule:
            return data, 0

        params = rule.parameters
        price_method = params.get("price_method", "forward_fill")
        volume_method = params.get("volume_method", "zero_fill")

        missing_count = 0

        # 处理价格缺失值
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in data.columns:
                col_missing = data[col].isna().sum()
                if col_missing > 0:
                    missing_count += col_missing

                    if price_method == "forward_fill":
                        data[col] = data[col].ffill()
                    elif price_method == "backward_fill":
                        data[col] = data[col].bfill()
                    elif price_method == "interpolate":
                        data[col] = data[col].interpolate()

        # 处理成交量缺失值
        if 'volume' in data.columns:
            vol_missing = data['volume'].isna().sum()
            if vol_missing > 0:
                missing_count += vol_missing

                if volume_method == "zero_fill":
                    data['volume'] = data['volume'].fillna(0)
                elif volume_method == "forward_fill":
                    data['volume'] = data['volume'].ffill()
                elif volume_method == "median_fill":
                    data['volume'] = data['volume'].fillna(data['volume'].median())

        if missing_count > 0:
            self.logger.info(f"填补缺失值: {missing_count}个")

        return data, missing_count

    def _standardize_format(self, data: pd.DataFrame) -> pd.DataFrame:
        """标准化数据格式"""
        rule = next((r for r in self.cleaning_rules if r.rule_name == "format_standardization"), None)
        if not rule:
            return data

        params = rule.parameters
        price_precision = params.get("price_precision", 2)
        volume_precision = params.get("volume_precision", 0)

        # 标准化价格精度
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in data.columns:
                data[col] = data[col].round(price_precision)

        # 标准化成交量精度
        if 'volume' in data.columns:
            data['volume'] = data['volume'].round(volume_precision).astype(int)

        # 标准化日期时间格式
        if 'datetime' in data.columns:
            data['datetime'] = pd.to_datetime(data['datetime'])

        return data

    def _check_trading_day_continuity(self, data: pd.DataFrame) -> pd.DataFrame:
        """检查交易日连续性"""
        if 'date' not in data.columns and 'datetime' not in data.columns:
            return data

        # 获取日期列
        date_col = 'date' if 'date' in data.columns else 'datetime'

        # 转换为日期格式
        if data[date_col].dtype == 'object':
            data[date_col] = pd.to_datetime(data[date_col])

        # 按日期排序
        data = data.sort_values(date_col)

        # 检查日期间隔
        if len(data) > 1:
            date_diff = data[date_col].diff().dt.days
            # 找出间隔过大的日期（超过7天可能是节假日）
            large_gaps = date_diff > 7
            if large_gaps.any():
                gap_count = large_gaps.sum()
                self.logger.info(f"发现较大日期间隔: {gap_count}处")

        return data

    def _calculate_quality_metrics(self, metrics: DataQualityMetrics, final_count: int) -> DataQualityMetrics:
        """计算数据质量指标"""
        if metrics.total_records == 0:
            return metrics

        # 完整性：剩余记录数 / 原始记录数
        metrics.completeness_rate = final_count / metrics.total_records

        # 准确性：(总记录数 - 异常记录数) / 总记录数
        error_records = metrics.outlier_records + metrics.duplicate_records
        metrics.accuracy_rate = max(0, (metrics.total_records - error_records) / metrics.total_records)

        # 一致性：基于逻辑检查结果
        metrics.consistency_rate = metrics.accuracy_rate  # 简化处理

        # 综合质量分数：加权平均
        metrics.quality_score = (
            0.4 * metrics.completeness_rate +
            0.4 * metrics.accuracy_rate +
            0.2 * metrics.consistency_rate
        ) * 100

        return metrics

    def batch_clean_data(self, stock_codes: List[str], data_type: str = 'minute') -> Dict[str, Any]:
        """
        批量清洗数据

        Args:
            stock_codes: 股票代码列表
            data_type: 数据类型 ('minute' 或 'daily')

        Returns:
            清洗结果统计
        """
        self.logger.info(f"开始批量清洗{data_type}数据: {len(stock_codes)}只股票")

        results = {
            'total_stocks': len(stock_codes),
            'processed_stocks': 0,
            'failed_stocks': 0,
            'total_records_before': 0,
            'total_records_after': 0,
            'total_quality_score': 0.0,
            'stock_results': {},
            'overall_metrics': DataQualityMetrics()
        }

        for stock_code in stock_codes:
            try:
                # 从数据库获取数据
                data = self._load_data_from_db(stock_code, data_type)

                if data.empty:
                    self.logger.warning(f"未找到数据: {stock_code}")
                    results['failed_stocks'] += 1
                    continue

                results['total_records_before'] += len(data)

                # 清洗数据
                if data_type == 'minute':
                    cleaned_data, metrics = self.clean_minute_data(data, stock_code)
                else:
                    cleaned_data, metrics = self.clean_daily_data(data, stock_code)

                results['total_records_after'] += len(cleaned_data)
                results['total_quality_score'] += metrics.quality_score
                results['processed_stocks'] += 1

                # 保存清洗后的数据
                self._save_cleaned_data(stock_code, cleaned_data, data_type)

                # 记录结果
                results['stock_results'][stock_code] = {
                    'original_records': metrics.total_records,
                    'cleaned_records': len(cleaned_data),
                    'quality_score': metrics.quality_score,
                    'metrics': metrics
                }

                # 累计整体指标
                self._accumulate_metrics(results['overall_metrics'], metrics)

            except Exception as e:
                self.logger.error(f"清洗数据失败 {stock_code}: {e}")
                results['failed_stocks'] += 1

        # 计算平均质量分数
        if results['processed_stocks'] > 0:
            results['average_quality_score'] = results['total_quality_score'] / results['processed_stocks']
        else:
            results['average_quality_score'] = 0.0

        self.logger.info(f"批量清洗完成: 处理{results['processed_stocks']}只股票, 平均质量分数: {results['average_quality_score']:.2f}")

        return results

    def _load_data_from_db(self, stock_code: str, data_type: str) -> pd.DataFrame:
        """从数据库加载数据"""
        # 这里应该实现从数据库加载数据的逻辑
        # 暂时返回空DataFrame，后续在数据存储模块中实现
        self.logger.debug(f"加载数据: {stock_code} {data_type}")
        return pd.DataFrame()

    def _save_cleaned_data(self, stock_code: str, data: pd.DataFrame, data_type: str):
        """保存清洗后的数据"""
        # 这里应该实现保存清洗后数据的逻辑
        # 后续在数据存储模块中实现
        self.logger.debug(f"保存清洗数据: {stock_code} {data_type} {len(data)}条记录")

    def _accumulate_metrics(self, overall_metrics: DataQualityMetrics, metrics: DataQualityMetrics):
        """累计质量指标"""
        overall_metrics.total_records += metrics.total_records
        overall_metrics.missing_records += metrics.missing_records
        overall_metrics.duplicate_records += metrics.duplicate_records
        overall_metrics.outlier_records += metrics.outlier_records
        overall_metrics.corrected_records += metrics.corrected_records

    def generate_cleaning_report(self, results: Dict[str, Any], output_path: str = "data/cleaning_report.json"):
        """生成清洗报告"""
        import json

        # 准备报告数据
        report = {
            "generated_at": datetime.now().isoformat(),
            "summary": {
                "total_stocks": results['total_stocks'],
                "processed_stocks": results['processed_stocks'],
                "failed_stocks": results['failed_stocks'],
                "success_rate": results['processed_stocks'] / results['total_stocks'] if results['total_stocks'] > 0 else 0,
                "total_records_before": results['total_records_before'],
                "total_records_after": results['total_records_after'],
                "data_retention_rate": results['total_records_after'] / results['total_records_before'] if results['total_records_before'] > 0 else 0,
                "average_quality_score": results.get('average_quality_score', 0.0)
            },
            "cleaning_rules": [
                {
                    "rule_name": rule.rule_name,
                    "rule_type": rule.rule_type,
                    "enabled": rule.enabled,
                    "description": rule.description,
                    "parameters": rule.parameters
                }
                for rule in self.cleaning_rules
            ],
            "stock_details": results['stock_results'],
            "overall_metrics": {
                "total_records": results['overall_metrics'].total_records,
                "missing_records": results['overall_metrics'].missing_records,
                "duplicate_records": results['overall_metrics'].duplicate_records,
                "outlier_records": results['overall_metrics'].outlier_records,
                "corrected_records": results['overall_metrics'].corrected_records
            }
        }

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 保存报告
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        self.logger.info(f"清洗报告已生成: {output_path}")
        return output_path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强日志管理模块

提供结构化日志、性能日志、错误追踪等高级日志功能。
"""

import logging
import logging.handlers
import os
import sys
import json
import time
import threading
from datetime import datetime
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, asdict
from contextlib import contextmanager
from enum import Enum
import traceback
import functools


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogType(Enum):
    """日志类型枚举"""
    GENERAL = "general"      # 一般日志
    PERFORMANCE = "performance"  # 性能日志
    ERROR = "error"          # 错误日志
    AUDIT = "audit"          # 审计日志
    BUSINESS = "business"    # 业务日志


@dataclass
class LogEntry:
    """结构化日志条目"""
    timestamp: float
    level: str
    logger_name: str
    message: str
    log_type: str = LogType.GENERAL.value
    module: str = ""
    function: str = ""
    line_number: int = 0
    thread_id: str = ""
    process_id: int = 0
    extra_data: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.extra_data is None:
            self.extra_data = {}


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        # 获取调用信息
        import inspect
        frame = inspect.currentframe()
        caller_frame = None
        
        # 向上查找调用栈，跳过日志相关的帧
        while frame:
            if frame.f_code.co_filename != __file__:
                caller_frame = frame
                break
            frame = frame.f_back
        
        module_name = ""
        function_name = ""
        line_number = 0
        
        if caller_frame:
            module_name = caller_frame.f_globals.get('__name__', '')
            function_name = caller_frame.f_code.co_name
            line_number = caller_frame.f_lineno
        
        # 创建结构化日志条目
        log_entry = LogEntry(
            timestamp=record.created,
            level=record.levelname,
            logger_name=record.name,
            message=record.getMessage(),
            log_type=getattr(record, 'log_type', LogType.GENERAL.value),
            module=module_name,
            function=function_name,
            line_number=line_number,
            thread_id=str(threading.current_thread().ident),
            process_id=os.getpid(),
            extra_data=getattr(record, 'extra_data', {})
        )
        
        # 转换为JSON格式
        return json.dumps(asdict(log_entry), ensure_ascii=False, default=str)


class HumanReadableFormatter(logging.Formatter):
    """人类可读的日志格式化器"""
    
    def __init__(self):
        super().__init__(
            fmt='%(asctime)s | %(levelname)-8s | %(name)-20s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    def format(self, record: logging.LogRecord) -> str:
        # 添加额外信息
        if hasattr(record, 'extra_data') and record.extra_data:
            original_msg = record.getMessage()
            extra_info = json.dumps(record.extra_data, ensure_ascii=False, default=str)
            record.msg = f"{original_msg} | Extra: {extra_info}"
        
        return super().format(record)


class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.performance_data: List[Dict[str, Any]] = []
        self.lock = threading.Lock()
    
    @contextmanager
    def log_performance(self, operation: str, **kwargs):
        """性能日志上下文管理器"""
        start_time = time.time()
        start_memory = self._get_memory_usage()
        
        try:
            yield
        finally:
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            duration = end_time - start_time
            memory_delta = end_memory - start_memory
            
            perf_data = {
                'operation': operation,
                'duration_seconds': duration,
                'start_time': start_time,
                'end_time': end_time,
                'memory_start_mb': start_memory,
                'memory_end_mb': end_memory,
                'memory_delta_mb': memory_delta,
                **kwargs
            }
            
            # 记录性能数据
            with self.lock:
                self.performance_data.append(perf_data)
                
                # 限制历史数据大小
                if len(self.performance_data) > 1000:
                    self.performance_data.pop(0)
            
            # 记录日志
            self.logger.info(
                f"性能统计 - {operation}: {duration:.3f}秒, 内存变化: {memory_delta:+.2f}MB",
                extra={
                    'log_type': LogType.PERFORMANCE.value,
                    'extra_data': perf_data
                }
            )
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / (1024 * 1024)
        except ImportError:
            return 0.0
    
    def get_performance_summary(self, operation: Optional[str] = None) -> Dict[str, Any]:
        """获取性能统计摘要"""
        with self.lock:
            data = self.performance_data.copy()
        
        if operation:
            data = [d for d in data if d['operation'] == operation]
        
        if not data:
            return {'count': 0}
        
        durations = [d['duration_seconds'] for d in data]
        memory_deltas = [d['memory_delta_mb'] for d in data]
        
        return {
            'count': len(data),
            'total_duration': sum(durations),
            'avg_duration': sum(durations) / len(durations),
            'min_duration': min(durations),
            'max_duration': max(durations),
            'avg_memory_delta': sum(memory_deltas) / len(memory_deltas),
            'total_memory_delta': sum(memory_deltas)
        }


class ErrorTracker:
    """错误追踪器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.error_history: List[Dict[str, Any]] = []
        self.lock = threading.Lock()
    
    def track_error(
        self, 
        exception: Exception, 
        context: Optional[Dict[str, Any]] = None,
        severity: str = "ERROR"
    ):
        """追踪错误"""
        import uuid
        
        error_data = {
            'error_id': str(uuid.uuid4()),
            'timestamp': time.time(),
            'exception_type': type(exception).__name__,
            'message': str(exception),
            'severity': severity,
            'stack_trace': traceback.format_exc(),
            'context': context or {}
        }
        
        # 记录错误历史
        with self.lock:
            self.error_history.append(error_data)
            
            # 限制历史数据大小
            if len(self.error_history) > 500:
                self.error_history.pop(0)
        
        # 记录日志
        self.logger.error(
            f"错误追踪 - {error_data['exception_type']}: {error_data['message']}",
            extra={
                'log_type': LogType.ERROR.value,
                'extra_data': error_data
            }
        )
    
    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取错误统计摘要"""
        current_time = time.time()
        cutoff_time = current_time - (hours * 3600)
        
        with self.lock:
            recent_errors = [
                error for error in self.error_history
                if error['timestamp'] >= cutoff_time
            ]
        
        if not recent_errors:
            return {'count': 0, 'by_type': {}, 'by_severity': {}}
        
        # 按类型统计
        by_type = {}
        for error in recent_errors:
            error_type = error['exception_type']
            by_type[error_type] = by_type.get(error_type, 0) + 1
        
        # 按严重程度统计
        by_severity = {}
        for error in recent_errors:
            severity = error['severity']
            by_severity[severity] = by_severity.get(severity, 0) + 1
        
        return {
            'count': len(recent_errors),
            'by_type': by_type,
            'by_severity': by_severity,
            'recent_errors': recent_errors[-10:]  # 最近10个错误
        }


class EnhancedLogger:
    """
    增强日志记录器

    集成结构化日志、性能监控和错误追踪功能。
    """

    def __init__(
        self,
        name: str,
        log_dir: str = "logs",
        structured_logging: bool = True,
        performance_tracking: bool = True,
        error_tracking: bool = True
    ):
        self.name = name
        self.log_dir = log_dir
        self.structured_logging = structured_logging

        # 创建日志目录
        os.makedirs(log_dir, exist_ok=True)

        # 创建基础日志记录器
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)

        # 避免重复配置
        if not self.logger.handlers:
            self._setup_handlers()

        # 初始化增强功能
        self.performance_logger = PerformanceLogger(self.logger) if performance_tracking else None
        self.error_tracker = ErrorTracker(self.logger) if error_tracking else None

    def _setup_handlers(self):
        """设置日志处理器"""
        # 控制台处理器（人类可读格式）
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(HumanReadableFormatter())
        self.logger.addHandler(console_handler)

        # 文件处理器（结构化格式）
        if self.structured_logging:
            structured_file = os.path.join(self.log_dir, f"{self.name}_structured.jsonl")
            structured_handler = logging.handlers.RotatingFileHandler(
                structured_file,
                maxBytes=50*1024*1024,  # 50MB
                backupCount=10,
                encoding='utf-8'
            )
            structured_handler.setLevel(logging.DEBUG)
            structured_handler.setFormatter(StructuredFormatter())
            self.logger.addHandler(structured_handler)

        # 普通文件处理器
        normal_file = os.path.join(self.log_dir, f"{self.name}.log")
        normal_handler = logging.handlers.RotatingFileHandler(
            normal_file,
            maxBytes=20*1024*1024,  # 20MB
            backupCount=5,
            encoding='utf-8'
        )
        normal_handler.setLevel(logging.INFO)
        normal_handler.setFormatter(HumanReadableFormatter())
        self.logger.addHandler(normal_handler)

        # 错误文件处理器
        error_file = os.path.join(self.log_dir, f"{self.name}_error.log")
        error_handler = logging.handlers.RotatingFileHandler(
            error_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=3,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(HumanReadableFormatter())
        self.logger.addHandler(error_handler)

    def debug(self, message: str, **kwargs):
        """调试日志"""
        self._log(logging.DEBUG, message, **kwargs)

    def info(self, message: str, **kwargs):
        """信息日志"""
        self._log(logging.INFO, message, **kwargs)

    def warning(self, message: str, **kwargs):
        """警告日志"""
        self._log(logging.WARNING, message, **kwargs)

    def error(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """错误日志"""
        if exception and self.error_tracker:
            self.error_tracker.track_error(exception, kwargs)

        self._log(logging.ERROR, message, **kwargs)

    def critical(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """严重错误日志"""
        if exception and self.error_tracker:
            self.error_tracker.track_error(exception, kwargs, severity="CRITICAL")

        self._log(logging.CRITICAL, message, **kwargs)

    def business(self, message: str, **kwargs):
        """业务日志"""
        kwargs['log_type'] = LogType.BUSINESS.value
        self._log(logging.INFO, message, **kwargs)

    def audit(self, message: str, **kwargs):
        """审计日志"""
        kwargs['log_type'] = LogType.AUDIT.value
        self._log(logging.INFO, message, **kwargs)

    def _log(self, level: int, message: str, **kwargs):
        """内部日志方法"""
        extra_data = kwargs.copy()
        log_type = extra_data.pop('log_type', LogType.GENERAL.value)

        self.logger.log(
            level,
            message,
            extra={
                'log_type': log_type,
                'extra_data': extra_data
            }
        )

    @contextmanager
    def performance(self, operation: str, **kwargs):
        """性能监控上下文管理器"""
        if self.performance_logger:
            with self.performance_logger.log_performance(operation, **kwargs):
                yield
        else:
            yield

    def get_performance_summary(self, operation: Optional[str] = None) -> Dict[str, Any]:
        """获取性能统计摘要"""
        if self.performance_logger:
            return self.performance_logger.get_performance_summary(operation)
        return {'count': 0}

    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取错误统计摘要"""
        if self.error_tracker:
            return self.error_tracker.get_error_summary(hours)
        return {'count': 0}


class LoggerManager:
    """日志管理器"""

    _loggers: Dict[str, EnhancedLogger] = {}
    _lock = threading.Lock()

    @classmethod
    def get_logger(
        cls,
        name: str,
        log_dir: str = "logs",
        **kwargs
    ) -> EnhancedLogger:
        """获取增强日志记录器"""
        with cls._lock:
            if name not in cls._loggers:
                cls._loggers[name] = EnhancedLogger(name, log_dir, **kwargs)
            return cls._loggers[name]

    @classmethod
    def get_all_loggers(cls) -> Dict[str, EnhancedLogger]:
        """获取所有日志记录器"""
        with cls._lock:
            return cls._loggers.copy()

    @classmethod
    def get_system_summary(cls) -> Dict[str, Any]:
        """获取系统日志摘要"""
        with cls._lock:
            loggers = cls._loggers.copy()

        summary = {
            'total_loggers': len(loggers),
            'performance_summary': {},
            'error_summary': {},
            'logger_names': list(loggers.keys())
        }

        # 汇总性能数据
        total_performance = {'count': 0, 'total_duration': 0}
        for logger_name, logger in loggers.items():
            perf_summary = logger.get_performance_summary()
            if perf_summary['count'] > 0:
                summary['performance_summary'][logger_name] = perf_summary
                total_performance['count'] += perf_summary['count']
                total_performance['total_duration'] += perf_summary.get('total_duration', 0)

        summary['performance_summary']['total'] = total_performance

        # 汇总错误数据
        total_errors = {'count': 0, 'by_type': {}, 'by_severity': {}}
        for logger_name, logger in loggers.items():
            error_summary = logger.get_error_summary()
            if error_summary['count'] > 0:
                summary['error_summary'][logger_name] = error_summary
                total_errors['count'] += error_summary['count']

                # 合并错误类型统计
                for error_type, count in error_summary.get('by_type', {}).items():
                    total_errors['by_type'][error_type] = total_errors['by_type'].get(error_type, 0) + count

                # 合并严重程度统计
                for severity, count in error_summary.get('by_severity', {}).items():
                    total_errors['by_severity'][severity] = total_errors['by_severity'].get(severity, 0) + count

        summary['error_summary']['total'] = total_errors

        return summary


# 便捷函数
def get_enhanced_logger(name: str, **kwargs) -> EnhancedLogger:
    """获取增强日志记录器的便捷函数"""
    return LoggerManager.get_logger(name, **kwargs)


def log_performance(logger_name: str, operation: str):
    """性能日志装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_enhanced_logger(logger_name)
            with logger.performance(operation):
                return func(*args, **kwargs)
        return wrapper
    return decorator

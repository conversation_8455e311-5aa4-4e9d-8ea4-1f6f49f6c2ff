"""
A股数据采集框架 - Python 3.11快速测试

专为Python 3.11环境设计的快速测试脚本
测试框架核心功能，不依赖可能缺失的第三方包
"""

import sys
import os
from pathlib import Path

def test_framework_core():
    """测试框架核心功能"""
    print("🧪 A股数据采集框架 - Python 3.11快速测试")
    print("="*50)
    
    # 检查Python版本
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor == 11:
        print("✅ Python 3.11环境确认")
    else:
        print("⚠️  非Python 3.11环境")
    
    # 测试基础导入
    print(f"\n📦 测试基础模块导入...")
    
    try:
        # 测试配置模块
        from data_acquisition.config.settings import Config
        config = Config()
        print("  ✅ 配置模块")
        
        # 测试股票代码工具
        from data_acquisition.utils.stock_codes import normalize_stock_code, validate_stock_codes
        print("  ✅ 股票代码工具")
        
        # 测试数据库模块
        from data_acquisition.storage.database import DatabaseManager
        print("  ✅ 数据库模块")
        
        # 测试缓存模块
        from data_acquisition.storage.cache_manager import CacheManager
        print("  ✅ 缓存模块")
        
        print("✅ 核心模块导入成功")
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    
    # 测试股票代码功能
    print(f"\n📈 测试股票代码功能...")
    
    try:
        # 测试代码标准化
        test_codes = ['000001', '600000', '300001', '688001']
        expected = ['000001.SZ', '600000.SH', '300001.SZ', '688001.SH']
        
        for code, exp in zip(test_codes, expected):
            result = normalize_stock_code(code)
            if result == exp:
                print(f"  ✅ {code} -> {result}")
            else:
                print(f"  ❌ {code} -> {result} (期望: {exp})")
        
        # 测试批量验证
        mixed_codes = ['000001', '600000.SH', 'INVALID', '300001.SZ']
        valid, invalid = validate_stock_codes(mixed_codes)
        print(f"  ✅ 批量验证: {len(valid)} 有效, {len(invalid)} 无效")
        
        print("✅ 股票代码功能正常")
        
    except Exception as e:
        print(f"❌ 股票代码功能测试失败: {e}")
        return False
    
    # 测试配置系统
    print(f"\n⚙️  测试配置系统...")
    
    try:
        from data_acquisition.config.settings import Config, get_config
        
        # 测试默认配置
        config = Config()
        print(f"  ✅ 默认配置: 缓存={config.CACHE_ENABLED}")
        
        # 测试开发配置
        dev_config = get_config('dev')
        print(f"  ✅ 开发配置: 日志级别={dev_config.LOG_LEVEL}")
        
        # 测试目录创建
        config.create_directories()
        print(f"  ✅ 目录创建成功")
        
        print("✅ 配置系统正常")
        
    except Exception as e:
        print(f"❌ 配置系统测试失败: {e}")
        return False
    
    # 测试数据库功能
    print(f"\n💾 测试数据库功能...")
    
    try:
        db = DatabaseManager(config)
        
        # 测试股票信息保存和读取
        test_info = {
            'name': '测试股票',
            'exchange': 'SZ',
            'sector': '测试行业'
        }
        
        success = db.save_stock_info('TEST.SZ', test_info)
        if success:
            print("  ✅ 股票信息保存成功")
        else:
            print("  ⚠️  股票信息保存失败")
        
        # 读取股票信息
        retrieved = db.get_stock_info('TEST.SZ')
        if retrieved:
            print(f"  ✅ 股票信息读取成功: {retrieved.get('name', 'N/A')}")
        else:
            print("  ⚠️  股票信息读取失败")
        
        # 测试数据覆盖度
        coverage = db.get_data_coverage('TEST.SZ')
        print(f"  ✅ 数据覆盖度查询: {coverage.get('record_count', 0)} 条记录")
        
        print("✅ 数据库功能正常")
        
    except Exception as e:
        print(f"❌ 数据库功能测试失败: {e}")
        return False
    
    # 测试缓存功能
    print(f"\n🗄️  测试缓存功能...")
    
    try:
        cache = CacheManager(config)
        
        # 测试缓存统计
        stats = cache.get_cache_stats()
        print(f"  ✅ 缓存统计: {stats.get('total_entries', 0)} 个条目")
        
        # 测试缓存清理
        cache.cleanup_expired_cache()
        print("  ✅ 缓存清理成功")
        
        print("✅ 缓存功能正常")
        
    except Exception as e:
        print(f"❌ 缓存功能测试失败: {e}")
        return False
    
    return True

def test_optional_features():
    """测试可选功能"""
    print(f"\n🔧 测试可选功能...")
    
    optional_results = {}
    
    # 测试pandas
    try:
        import pandas as pd
        print("  ✅ pandas可用")
        optional_results['pandas'] = True
    except ImportError:
        print("  ❌ pandas不可用")
        optional_results['pandas'] = False
    
    # 测试numpy
    try:
        import numpy as np
        print("  ✅ numpy可用")
        optional_results['numpy'] = True
    except ImportError:
        print("  ❌ numpy不可用")
        optional_results['numpy'] = False
    
    # 测试requests
    try:
        import requests
        print("  ✅ requests可用")
        optional_results['requests'] = True
    except ImportError:
        print("  ❌ requests不可用")
        optional_results['requests'] = False
    
    # 测试beautifulsoup4
    try:
        from bs4 import BeautifulSoup
        print("  ✅ beautifulsoup4可用")
        optional_results['beautifulsoup4'] = True
    except ImportError:
        print("  ❌ beautifulsoup4不可用")
        optional_results['beautifulsoup4'] = False
    
    # 测试akshare
    try:
        import akshare as ak
        print("  ✅ akshare可用")
        optional_results['akshare'] = True
    except ImportError:
        print("  ❌ akshare不可用")
        optional_results['akshare'] = False
    
    return optional_results

def show_results(core_success, optional_results):
    """显示测试结果"""
    print(f"\n" + "="*50)
    print("📊 测试结果汇总")
    print("="*50)
    
    if core_success:
        print("🎉 核心功能测试通过！")
        print("   框架基本功能可以正常使用")
    else:
        print("❌ 核心功能测试失败")
        print("   请检查框架安装")
        return
    
    # 可选功能统计
    available_count = sum(optional_results.values())
    total_count = len(optional_results)
    
    print(f"\n📦 可选功能: {available_count}/{total_count} 可用")
    
    missing_packages = [pkg for pkg, available in optional_results.items() if not available]
    
    if missing_packages:
        print(f"\n⚠️  缺失的包: {', '.join(missing_packages)}")
        print(f"   可以运行以下命令安装:")
        print(f"   pip install {' '.join(missing_packages)}")
    
    print(f"\n📚 接下来可以:")
    
    if optional_results.get('pandas', False) and optional_results.get('numpy', False):
        print("  ✅ 运行完整示例:")
        print("     python examples/basic_usage_cn.py")
        
        if optional_results.get('matplotlib', False):
            print("  ✅ 运行策略回测:")
            print("     python examples/strategy_backtest_cn.py")
    else:
        print("  ⚠️  安装pandas和numpy后可运行完整示例")
    
    if optional_results.get('akshare', False):
        print("  ✅ 使用akshare数据源获取实时数据")
    else:
        print("  ⚠️  安装akshare后可获取实时A股数据")
    
    print(f"\n🔧 故障排除:")
    print(f"  - 查看完整文档: README_CN.md")
    print(f"  - 运行完整测试: python test_framework_cn.py")
    print(f"  - 安装缺失依赖: python install_py311.py")

def main():
    """主测试函数"""
    try:
        # 运行核心功能测试
        core_success = test_framework_core()
        
        # 测试可选功能
        optional_results = test_optional_features()
        
        # 显示结果
        show_results(core_success, optional_results)
        
        return core_success
        
    except KeyboardInterrupt:
        print(f"\n\n⏹️  测试被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
        print(f"\n🔧 建议:")
        print(f"  1. 确保在正确的目录中运行")
        print(f"  2. 检查框架文件完整性")
        print(f"  3. 运行安装脚本: python install_py311.py")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

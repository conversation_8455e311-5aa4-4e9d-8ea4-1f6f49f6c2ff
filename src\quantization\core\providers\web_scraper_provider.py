#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络爬虫数据提供商

基于网络爬虫的数据获取实现，作为akshare的备用数据源。
"""

import pandas as pd
import numpy as np
import requests
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Union, Any
from bs4 import BeautifulSoup
import json
import re

from quantization.core.providers.base import BaseProvider
from quantization.utils.stock_codes import StockCodeUtils


class WebScraperProvider(BaseProvider):
    """
    网络爬虫数据提供商
    
    通过网络爬虫获取股票数据，作为主要数据源的备用。
    """
    
    def __init__(
        self, 
        rate_limit: Optional[float] = 1.0,
        timeout: float = 30.0,
        max_retries: int = 3,
        user_agent: str = ""
    ):
        """
        初始化网络爬虫提供商
        
        Args:
            rate_limit: 请求频率限制（秒）
            timeout: 请求超时时间
            max_retries: 最大重试次数
            user_agent: 用户代理字符串
        """
        super().__init__("WebScraper", rate_limit, timeout, max_retries)
        
        # 设置请求头
        self.headers = {
            'User-Agent': user_agent or (
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                'AppleWebKit/537.36 (KHTML, like Gecko) '
                'Chrome/91.0.4472.124 Safari/537.36'
            ),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        # 会话对象
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 缓存
        self._cache = {}
        self._cache_expire_time = {}
    
    def get_stock_data(
        self, 
        stock_code: str, 
        start_date: Optional[Union[str, date, datetime]] = None,
        end_date: Optional[Union[str, date, datetime]] = None,
        fields: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        获取股票基础数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            fields: 需要的字段列表
            
        Returns:
            股票数据DataFrame
        """
        stock_code = self._validate_stock_code(stock_code)
        start_date, end_date = self._validate_date_range(start_date, end_date)
        
        def _fetch_data():
            try:
                # 使用新浪财经接口获取数据
                symbol = StockCodeUtils.add_suffix(stock_code)
                
                # 构建请求URL
                url = f"https://quotes.sina.cn/cn/api/jsonp_v2.php/var%20_sh{stock_code}_30_=/CN_MarketDataService.getKLineData"
                params = {
                    'symbol': symbol,
                    'scale': '240',  # 日线
                    'ma': 'no',
                    'datalen': '1023'
                }
                
                response = self.session.get(url, params=params, timeout=self.timeout)
                response.raise_for_status()
                
                # 解析JSONP响应
                content = response.text
                json_start = content.find('[')
                json_end = content.rfind(']') + 1
                
                if json_start == -1 or json_end == 0:
                    return pd.DataFrame()
                
                json_data = content[json_start:json_end]
                data = json.loads(json_data)
                
                if not data:
                    return pd.DataFrame()
                
                # 转换为DataFrame
                df = pd.DataFrame(data)
                
                # 标准化列名和数据类型
                if 'day' in df.columns:
                    df['date'] = pd.to_datetime(df['day'])
                    df = df.set_index('date')
                
                column_mapping = {
                    'open': 'open',
                    'high': 'high',
                    'low': 'low',
                    'close': 'close',
                    'volume': 'volume'
                }
                
                for old_col, new_col in column_mapping.items():
                    if old_col in df.columns:
                        df[new_col] = pd.to_numeric(df[old_col], errors='coerce')
                
                # 过滤日期范围
                if start_date:
                    df = df[df.index >= pd.to_datetime(start_date)]
                if end_date:
                    df = df[df.index <= pd.to_datetime(end_date)]
                
                # 筛选字段
                if fields:
                    available_fields = [f for f in fields if f in df.columns]
                    df = df[available_fields]
                
                return df
                
            except Exception as e:
                self.logger.warning(f"网络爬虫获取股票数据失败: {str(e)}")
                return pd.DataFrame()
        
        return self._make_request(_fetch_data)
    
    def get_stock_info(self, stock_code: str) -> Dict[str, Any]:
        """
        获取股票基本信息
        
        Args:
            stock_code: 股票代码
            
        Returns:
            股票基本信息字典
        """
        stock_code = self._validate_stock_code(stock_code)
        
        # 检查缓存
        cache_key = f"info_{stock_code}"
        if cache_key in self._cache:
            cache_time = self._cache_expire_time.get(cache_key, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=1):
                return self._cache[cache_key]
        
        def _fetch_info():
            try:
                # 使用东方财富接口
                symbol = StockCodeUtils.add_suffix(stock_code)
                url = f"http://push2.eastmoney.com/api/qt/stock/get"
                params = {
                    'secid': f"0.{stock_code}" if stock_code.startswith('0') or stock_code.startswith('3') else f"1.{stock_code}",
                    'fields': 'f43,f44,f45,f46,f47,f48,f49,f50,f51,f52,f57,f58,f116,f117,f162'
                }
                
                response = self.session.get(url, params=params, timeout=self.timeout)
                response.raise_for_status()
                
                data = response.json()
                
                if not data.get('data'):
                    return {}
                
                stock_data = data['data']
                
                # 解析数据
                info = {
                    'code': stock_code,
                    'name': stock_data.get('f58', ''),
                    'price': stock_data.get('f43', 0) / 100 if stock_data.get('f43') else 0,
                    'change': stock_data.get('f44', 0) / 100 if stock_data.get('f44') else 0,
                    'pct_change': stock_data.get('f45', 0) / 100 if stock_data.get('f45') else 0,
                    'volume': stock_data.get('f47', 0),
                    'amount': stock_data.get('f48', 0),
                    'market_cap': stock_data.get('f116', 0),
                    'circulation_market_cap': stock_data.get('f117', 0),
                    'pe_ratio': stock_data.get('f49', 0) / 100 if stock_data.get('f49') else 0,
                    'pb_ratio': stock_data.get('f50', 0) / 100 if stock_data.get('f50') else 0
                }
                
                # 缓存结果
                self._cache[cache_key] = info
                self._cache_expire_time[cache_key] = datetime.now()
                
                return info
                
            except Exception as e:
                self.logger.warning(f"网络爬虫获取股票信息失败: {str(e)}")
                return {}
        
        return self._make_request(_fetch_info)
    
    def get_market_cap(self, stock_code: str) -> Optional[float]:
        """
        获取股票市值
        
        Args:
            stock_code: 股票代码
            
        Returns:
            市值（亿元）
        """
        try:
            stock_info = self.get_stock_info(stock_code)
            
            # 优先使用流通市值
            market_cap = stock_info.get('circulation_market_cap', 0)
            if not market_cap:
                market_cap = stock_info.get('market_cap', 0)
            
            if market_cap and market_cap > 0:
                # 转换为亿元
                return market_cap / 1e8
            
            return None
            
        except Exception as e:
            self.logger.debug(f"获取股票 {stock_code} 市值失败: {str(e)}")
            return None
    
    def get_big_order_net_ratio(self, stock_code: str) -> Optional[float]:
        """
        获取大单净量比
        
        Args:
            stock_code: 股票代码
            
        Returns:
            大单净量比
        """
        # 网络爬虫方式获取大单数据比较复杂，使用技术分析估算
        return self._estimate_big_order_ratio_technical(stock_code)
    
    def _estimate_big_order_ratio_technical(self, stock_code: str) -> Optional[float]:
        """
        使用技术分析方法估算大单净量比
        
        Args:
            stock_code: 股票代码
            
        Returns:
            估算的大单净量比
        """
        try:
            # 获取最近几天的数据
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=5)
            
            df = self.get_stock_data(stock_code, start_date, end_date)
            
            if df.empty or len(df) < 2:
                return None
            
            # 基于价量关系估算
            latest = df.iloc[-1]
            previous = df.iloc[-2]
            
            price_change = (latest['close'] - previous['close']) / previous['close']
            volume_ratio = latest['volume'] / previous['volume'] if previous['volume'] > 0 else 1
            
            # 简单的估算公式
            if price_change > 0 and volume_ratio > 1.5:
                # 价涨量增，可能有大单流入
                estimated_ratio = min(0.8, price_change * volume_ratio * 0.3)
            elif price_change < 0 and volume_ratio > 1.5:
                # 价跌量增，可能有大单流出
                estimated_ratio = max(-0.8, price_change * volume_ratio * 0.3)
            else:
                # 其他情况
                estimated_ratio = price_change * 0.1
            
            return estimated_ratio
            
        except Exception as e:
            self.logger.debug(f"技术分析估算大单净量比失败: {str(e)}")
            return None

    def get_williams_r(self, stock_code: str, period: int = 14) -> Optional[float]:
        """
        获取威廉指标

        Args:
            stock_code: 股票代码
            period: 计算周期

        Returns:
            威廉指标值
        """
        def _calculate_williams_r():
            try:
                # 获取足够的历史数据
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=period * 2)

                df = self.get_stock_data(stock_code, start_date, end_date)

                if df.empty or len(df) < period:
                    return None

                # 计算威廉指标
                high_max = df['high'].rolling(window=period).max()
                low_min = df['low'].rolling(window=period).min()
                close = df['close']

                wr = -100 * (high_max - close) / (high_max - low_min)

                return wr.iloc[-1] if not pd.isna(wr.iloc[-1]) else None

            except Exception as e:
                self.logger.debug(f"计算威廉指标失败: {str(e)}")
                return None

        return self._make_request(_calculate_williams_r)

    def get_volume_ratio(self, stock_code: str) -> Optional[float]:
        """
        获取量比

        Args:
            stock_code: 股票代码

        Returns:
            量比值
        """
        def _calculate_volume_ratio():
            try:
                # 获取最近的数据
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=10)

                df = self.get_stock_data(stock_code, start_date, end_date)

                if df.empty or len(df) < 5:
                    return None

                # 计算量比（当日成交量/过去5日平均成交量）
                current_volume = df['volume'].iloc[-1]
                avg_volume = df['volume'].iloc[-6:-1].mean()  # 过去5日平均

                if avg_volume > 0:
                    volume_ratio = current_volume / avg_volume
                    return volume_ratio

                return None

            except Exception as e:
                self.logger.debug(f"计算量比失败: {str(e)}")
                return None

        return self._make_request(_calculate_volume_ratio)

    def get_chinext_stocks(self) -> List[str]:
        """
        获取创业板股票列表

        Returns:
            创业板股票代码列表
        """
        cache_key = "chinext_stocks"

        # 检查缓存
        if cache_key in self._cache:
            cache_time = self._cache_expire_time.get(cache_key, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=24):
                return self._cache[cache_key]

        def _fetch_chinext_stocks():
            try:
                # 使用东方财富接口获取创业板股票
                url = "http://80.push2.eastmoney.com/api/qt/clist/get"
                params = {
                    'pn': '1',
                    'pz': '5000',
                    'po': '1',
                    'np': '1',
                    'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                    'fltt': '2',
                    'invt': '2',
                    'fid': 'f3',
                    'fs': 'm:0+t:80',  # 创业板
                    'fields': 'f12,f14'
                }

                response = self.session.get(url, params=params, timeout=self.timeout)
                response.raise_for_status()

                data = response.json()

                if not data.get('data', {}).get('diff'):
                    return []

                chinext_stocks = []
                for item in data['data']['diff']:
                    code = item.get('f12', '')
                    if code and len(code) == 6 and code.startswith('300'):
                        chinext_stocks.append(code)

                # 缓存结果
                self._cache[cache_key] = chinext_stocks
                self._cache_expire_time[cache_key] = datetime.now()

                return chinext_stocks

            except Exception as e:
                self.logger.warning(f"网络爬虫获取创业板股票列表失败: {str(e)}")
                return []

        return self._make_request(_fetch_chinext_stocks)

    def is_st_stock(self, stock_code: str) -> bool:
        """
        判断是否为ST股票

        Args:
            stock_code: 股票代码

        Returns:
            是否为ST股票
        """
        try:
            stock_info = self.get_stock_info(stock_code)
            stock_name = stock_info.get('name', '').upper()

            # 检查股票名称中是否包含ST标识
            st_keywords = ['ST', '*ST', 'S*ST', 'SST', 'S']

            for keyword in st_keywords:
                if keyword in stock_name:
                    return True

            return False

        except Exception as e:
            self.logger.debug(f"判断ST股票失败: {str(e)}")
            return False

    def validate_connection(self) -> bool:
        """
        验证数据源连接

        Returns:
            连接是否正常
        """
        try:
            # 尝试访问一个简单的接口来测试连接
            url = "http://push2.eastmoney.com/api/qt/stock/get"
            params = {
                'secid': '0.000001',  # 平安银行
                'fields': 'f43'
            }

            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()
            return bool(data.get('data'))

        except Exception as e:
            self.logger.error(f"网络爬虫连接验证失败: {str(e)}")
            return False

    def get_last_update_time(self) -> Optional[datetime]:
        """
        获取最后更新时间

        Returns:
            最后更新时间
        """
        return self.stats.get('last_request_time')

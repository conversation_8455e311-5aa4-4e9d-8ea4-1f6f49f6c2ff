#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据存储和索引优化模块
设计高效的数据存储结构、建立索引系统、实现快速查询接口
"""

import os
import sys
import sqlite3
import warnings
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Union
import pandas as pd
import numpy as np
from dataclasses import dataclass
import logging
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

warnings.filterwarnings('ignore')

@dataclass
class StorageConfig:
    """存储配置"""
    db_path: str = "data/historical_data.db"
    enable_wal_mode: bool = True  # 启用WAL模式提高并发性能
    cache_size: int = 10000  # 缓存页数
    temp_store: str = "memory"  # 临时存储在内存中
    synchronous: str = "normal"  # 同步模式
    journal_mode: str = "wal"  # 日志模式
    auto_vacuum: str = "incremental"  # 自动清理
    page_size: int = 4096  # 页面大小

@dataclass
class QueryStats:
    """查询统计"""
    query_count: int = 0
    total_time: float = 0.0
    avg_time: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0
    cache_hit_rate: float = 0.0

class DataStorage:
    """数据存储管理器"""
    
    def __init__(self, config: StorageConfig = None):
        """
        初始化数据存储管理器
        
        Args:
            config: 存储配置
        """
        self.config = config or StorageConfig()
        self.logger = logging.getLogger(__name__)
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(self.config.db_path), exist_ok=True)
        
        # 初始化数据库
        self._init_database()
        
        # 查询统计
        self.query_stats = QueryStats()
        self._stats_lock = threading.Lock()
        
        # 内存缓存
        self._cache = {}
        self._cache_lock = threading.Lock()
        self._max_cache_size = 1000
        
        self.logger.info("数据存储管理器初始化完成")
    
    def _init_database(self):
        """初始化数据库结构"""
        conn = sqlite3.connect(self.config.db_path)
        cursor = conn.cursor()
        
        # 设置数据库配置
        cursor.execute(f"PRAGMA cache_size = {self.config.cache_size}")
        cursor.execute(f"PRAGMA temp_store = {self.config.temp_store}")
        cursor.execute(f"PRAGMA synchronous = {self.config.synchronous}")
        cursor.execute(f"PRAGMA journal_mode = {self.config.journal_mode}")
        cursor.execute(f"PRAGMA auto_vacuum = {self.config.auto_vacuum}")
        cursor.execute(f"PRAGMA page_size = {self.config.page_size}")
        
        # 创建分时数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS minute_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code TEXT NOT NULL,
                datetime TEXT NOT NULL,
                date TEXT NOT NULL,
                time TEXT NOT NULL,
                open REAL NOT NULL,
                high REAL NOT NULL,
                low REAL NOT NULL,
                close REAL NOT NULL,
                volume INTEGER DEFAULT 0,
                amount REAL DEFAULT 0.0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(stock_code, datetime)
            )
        ''')
        
        # 创建日线数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS daily_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code TEXT NOT NULL,
                date TEXT NOT NULL,
                open REAL NOT NULL,
                high REAL NOT NULL,
                low REAL NOT NULL,
                close REAL NOT NULL,
                volume INTEGER DEFAULT 0,
                amount REAL DEFAULT 0.0,
                turnover_rate REAL DEFAULT 0.0,
                pe_ratio REAL DEFAULT 0.0,
                pb_ratio REAL DEFAULT 0.0,
                market_cap REAL DEFAULT 0.0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(stock_code, date)
            )
        ''')
        
        # 创建技术指标表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS technical_indicators (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code TEXT NOT NULL,
                date TEXT NOT NULL,
                data_type TEXT NOT NULL,  -- 'minute' or 'daily'
                indicator_name TEXT NOT NULL,
                indicator_value REAL,
                indicator_data TEXT,  -- JSON格式存储复杂指标
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(stock_code, date, data_type, indicator_name)
            )
        ''')
        
        # 创建数据质量表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS data_quality (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code TEXT NOT NULL,
                date TEXT NOT NULL,
                data_type TEXT NOT NULL,
                quality_score REAL DEFAULT 0.0,
                completeness_rate REAL DEFAULT 0.0,
                accuracy_rate REAL DEFAULT 0.0,
                consistency_rate REAL DEFAULT 0.0,
                validation_issues TEXT,  -- JSON格式存储问题列表
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(stock_code, date, data_type)
            )
        ''')
        
        # 创建索引
        self._create_indexes(cursor)
        
        conn.commit()
        conn.close()
        
        self.logger.info("数据库结构初始化完成")
    
    def _create_indexes(self, cursor):
        """创建数据库索引"""
        indexes = [
            # 分时数据索引
            "CREATE INDEX IF NOT EXISTS idx_minute_stock_date ON minute_data(stock_code, date)",
            "CREATE INDEX IF NOT EXISTS idx_minute_datetime ON minute_data(datetime)",
            "CREATE INDEX IF NOT EXISTS idx_minute_stock_datetime ON minute_data(stock_code, datetime)",
            
            # 日线数据索引
            "CREATE INDEX IF NOT EXISTS idx_daily_stock_date ON daily_data(stock_code, date)",
            "CREATE INDEX IF NOT EXISTS idx_daily_date ON daily_data(date)",
            "CREATE INDEX IF NOT EXISTS idx_daily_market_cap ON daily_data(market_cap)",
            
            # 技术指标索引
            "CREATE INDEX IF NOT EXISTS idx_indicators_stock_date ON technical_indicators(stock_code, date)",
            "CREATE INDEX IF NOT EXISTS idx_indicators_name ON technical_indicators(indicator_name)",
            
            # 数据质量索引
            "CREATE INDEX IF NOT EXISTS idx_quality_stock_date ON data_quality(stock_code, date)",
            "CREATE INDEX IF NOT EXISTS idx_quality_score ON data_quality(quality_score)"
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                self.logger.warning(f"创建索引失败: {e}")
    
    def save_minute_data(self, stock_code: str, date: str, data: pd.DataFrame) -> bool:
        """
        保存分时数据
        
        Args:
            stock_code: 股票代码
            date: 日期
            data: 分时数据
            
        Returns:
            是否成功
        """
        if data.empty:
            return False
        
        try:
            conn = sqlite3.connect(self.config.db_path)
            
            # 准备数据
            data_to_save = data.copy()
            data_to_save['stock_code'] = stock_code
            data_to_save['date'] = date
            
            # 确保必要列存在
            required_columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in data_to_save.columns:
                    if col == 'volume':
                        data_to_save[col] = 0
                    else:
                        self.logger.error(f"缺少必要列: {col}")
                        return False
            
            # 添加时间列
            if 'time' not in data_to_save.columns:
                data_to_save['time'] = pd.to_datetime(data_to_save['datetime']).dt.strftime('%H:%M:%S')
            
            # 添加成交额列
            if 'amount' not in data_to_save.columns:
                data_to_save['amount'] = 0.0
            
            # 选择要保存的列
            columns_to_save = ['stock_code', 'datetime', 'date', 'time', 'open', 'high', 'low', 'close', 'volume', 'amount']
            data_to_save = data_to_save[columns_to_save]
            
            # 批量插入数据
            data_to_save.to_sql('minute_data', conn, if_exists='append', index=False, method='multi')
            
            conn.close()
            
            self.logger.debug(f"保存分时数据成功: {stock_code} {date} {len(data)}条记录")
            return True
            
        except Exception as e:
            self.logger.error(f"保存分时数据失败: {stock_code} {date} - {e}")
            return False
    
    def save_daily_data(self, stock_code: str, date: str, data: pd.DataFrame) -> bool:
        """
        保存日线数据
        
        Args:
            stock_code: 股票代码
            date: 日期
            data: 日线数据
            
        Returns:
            是否成功
        """
        if data.empty:
            return False
        
        try:
            conn = sqlite3.connect(self.config.db_path)
            
            # 准备数据
            data_to_save = data.copy()
            data_to_save['stock_code'] = stock_code
            data_to_save['date'] = date
            
            # 确保必要列存在
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in data_to_save.columns:
                    if col == 'volume':
                        data_to_save[col] = 0
                    else:
                        self.logger.error(f"缺少必要列: {col}")
                        return False
            
            # 添加可选列的默认值
            optional_columns = {
                'amount': 0.0,
                'turnover_rate': 0.0,
                'pe_ratio': 0.0,
                'pb_ratio': 0.0,
                'market_cap': 0.0
            }
            
            for col, default_value in optional_columns.items():
                if col not in data_to_save.columns:
                    data_to_save[col] = default_value
            
            # 选择要保存的列
            columns_to_save = ['stock_code', 'date', 'open', 'high', 'low', 'close', 'volume', 'amount', 
                             'turnover_rate', 'pe_ratio', 'pb_ratio', 'market_cap']
            data_to_save = data_to_save[columns_to_save]
            
            # 批量插入数据
            data_to_save.to_sql('daily_data', conn, if_exists='append', index=False, method='multi')
            
            conn.close()
            
            self.logger.debug(f"保存日线数据成功: {stock_code} {date} {len(data)}条记录")
            return True
            
        except Exception as e:
            self.logger.error(f"保存日线数据失败: {stock_code} {date} - {e}")
            return False
    
    def get_minute_data(self, stock_code: str, start_date: str, end_date: str = None) -> pd.DataFrame:
        """
        获取分时数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期，None表示只获取start_date的数据
            
        Returns:
            分时数据
        """
        cache_key = f"minute_{stock_code}_{start_date}_{end_date}"
        
        # 检查缓存
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        try:
            start_time = datetime.now()
            
            conn = sqlite3.connect(self.config.db_path)
            
            if end_date is None:
                query = '''
                    SELECT datetime, open, high, low, close, volume, amount
                    FROM minute_data 
                    WHERE stock_code = ? AND date = ?
                    ORDER BY datetime
                '''
                params = (stock_code, start_date)
            else:
                query = '''
                    SELECT datetime, open, high, low, close, volume, amount
                    FROM minute_data 
                    WHERE stock_code = ? AND date BETWEEN ? AND ?
                    ORDER BY datetime
                '''
                params = (stock_code, start_date, end_date)
            
            data = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            # 转换数据类型
            if not data.empty:
                data['datetime'] = pd.to_datetime(data['datetime'])
                data = data.set_index('datetime')
            
            # 更新统计
            query_time = (datetime.now() - start_time).total_seconds()
            self._update_query_stats(query_time, cache_hit=False)
            
            # 缓存结果
            self._save_to_cache(cache_key, data)
            
            return data
            
        except Exception as e:
            self.logger.error(f"获取分时数据失败: {stock_code} {start_date} - {e}")
            return pd.DataFrame()

    def get_daily_data(self, stock_code: str, start_date: str, end_date: Optional[str] = None) -> pd.DataFrame:
        """
        获取日线数据

        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期，None表示只获取start_date的数据

        Returns:
            日线数据
        """
        cache_key = f"daily_{stock_code}_{start_date}_{end_date}"

        # 检查缓存
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data

        try:
            start_time = datetime.now()

            conn = sqlite3.connect(self.config.db_path)

            if end_date is None:
                query = '''
                    SELECT date, open, high, low, close, volume, amount,
                           turnover_rate, pe_ratio, pb_ratio, market_cap
                    FROM daily_data
                    WHERE stock_code = ? AND date = ?
                    ORDER BY date
                '''
                params = (stock_code, start_date)
            else:
                query = '''
                    SELECT date, open, high, low, close, volume, amount,
                           turnover_rate, pe_ratio, pb_ratio, market_cap
                    FROM daily_data
                    WHERE stock_code = ? AND date BETWEEN ? AND ?
                    ORDER BY date
                '''
                params = (stock_code, start_date, end_date)

            data = pd.read_sql_query(query, conn, params=params)
            conn.close()

            # 转换数据类型
            if not data.empty:
                data['date'] = pd.to_datetime(data['date'])
                data = data.set_index('date')

            # 更新统计
            query_time = (datetime.now() - start_time).total_seconds()
            self._update_query_stats(query_time, cache_hit=False)

            # 缓存结果
            self._save_to_cache(cache_key, data)

            return data

        except Exception as e:
            self.logger.error(f"获取日线数据失败: {stock_code} {start_date} - {e}")
            return pd.DataFrame()

    def batch_get_data(self, stock_codes: List[str], start_date: str, end_date: str,
                      data_type: str = 'daily', max_workers: int = 4) -> Dict[str, pd.DataFrame]:
        """
        批量获取数据

        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            data_type: 数据类型 ('minute' 或 'daily')
            max_workers: 最大并发数

        Returns:
            股票数据字典
        """
        self.logger.info(f"批量获取{data_type}数据: {len(stock_codes)}只股票")

        results = {}

        # 并发获取数据
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            if data_type == 'minute':
                future_to_stock = {
                    executor.submit(self.get_minute_data, stock_code, start_date, end_date): stock_code
                    for stock_code in stock_codes
                }
            else:
                future_to_stock = {
                    executor.submit(self.get_daily_data, stock_code, start_date, end_date): stock_code
                    for stock_code in stock_codes
                }

            for future in as_completed(future_to_stock):
                stock_code = future_to_stock[future]
                try:
                    data = future.result()
                    results[stock_code] = data
                except Exception as e:
                    self.logger.error(f"批量获取数据失败 {stock_code}: {e}")
                    results[stock_code] = pd.DataFrame()

        self.logger.info(f"批量获取完成: {len([k for k, v in results.items() if not v.empty])}/{len(stock_codes)} 成功")

        return results

    def save_technical_indicator(self, stock_code: str, date: str, data_type: str,
                               indicator_name: str, indicator_value: Optional[float] = None,
                               indicator_data: Optional[Dict] = None) -> bool:
        """
        保存技术指标

        Args:
            stock_code: 股票代码
            date: 日期
            data_type: 数据类型
            indicator_name: 指标名称
            indicator_value: 指标值
            indicator_data: 指标数据（复杂指标）

        Returns:
            是否成功
        """
        try:
            conn = sqlite3.connect(self.config.db_path)
            cursor = conn.cursor()

            # 准备数据
            indicator_data_json = json.dumps(indicator_data) if indicator_data else None

            cursor.execute('''
                INSERT OR REPLACE INTO technical_indicators
                (stock_code, date, data_type, indicator_name, indicator_value, indicator_data)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (stock_code, date, data_type, indicator_name, indicator_value, indicator_data_json))

            conn.commit()
            conn.close()

            return True

        except Exception as e:
            self.logger.error(f"保存技术指标失败: {stock_code} {date} {indicator_name} - {e}")
            return False

    def get_technical_indicator(self, stock_code: str, start_date: str, end_date: str,
                              indicator_name: str, data_type: str = 'daily') -> pd.DataFrame:
        """
        获取技术指标

        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            indicator_name: 指标名称
            data_type: 数据类型

        Returns:
            技术指标数据
        """
        try:
            conn = sqlite3.connect(self.config.db_path)

            query = '''
                SELECT date, indicator_value, indicator_data
                FROM technical_indicators
                WHERE stock_code = ? AND date BETWEEN ? AND ?
                AND indicator_name = ? AND data_type = ?
                ORDER BY date
            '''

            data = pd.read_sql_query(query, conn, params=(stock_code, start_date, end_date, indicator_name, data_type))
            conn.close()

            if not data.empty:
                data['date'] = pd.to_datetime(data['date'])
                data = data.set_index('date')

            return data

        except Exception as e:
            self.logger.error(f"获取技术指标失败: {stock_code} {indicator_name} - {e}")
            return pd.DataFrame()

    def save_data_quality(self, stock_code: str, date: str, data_type: str,
                         quality_score: float, completeness_rate: float = 0.0,
                         accuracy_rate: float = 0.0, consistency_rate: float = 0.0,
                         validation_issues: Optional[List[str]] = None) -> bool:
        """
        保存数据质量信息

        Args:
            stock_code: 股票代码
            date: 日期
            data_type: 数据类型
            quality_score: 质量分数
            completeness_rate: 完整性比率
            accuracy_rate: 准确性比率
            consistency_rate: 一致性比率
            validation_issues: 验证问题列表

        Returns:
            是否成功
        """
        try:
            conn = sqlite3.connect(self.config.db_path)
            cursor = conn.cursor()

            # 准备数据
            issues_json = json.dumps(validation_issues) if validation_issues else None

            cursor.execute('''
                INSERT OR REPLACE INTO data_quality
                (stock_code, date, data_type, quality_score, completeness_rate,
                 accuracy_rate, consistency_rate, validation_issues)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (stock_code, date, data_type, quality_score, completeness_rate,
                  accuracy_rate, consistency_rate, issues_json))

            conn.commit()
            conn.close()

            return True

        except Exception as e:
            self.logger.error(f"保存数据质量失败: {stock_code} {date} - {e}")
            return False

    def get_data_quality_stats(self, start_date: str, end_date: str,
                              data_type: str = 'daily') -> Dict[str, Any]:
        """
        获取数据质量统计

        Args:
            start_date: 开始日期
            end_date: 结束日期
            data_type: 数据类型

        Returns:
            质量统计信息
        """
        try:
            conn = sqlite3.connect(self.config.db_path)

            # 获取质量统计
            query = '''
                SELECT
                    COUNT(*) as total_records,
                    AVG(quality_score) as avg_quality_score,
                    AVG(completeness_rate) as avg_completeness,
                    AVG(accuracy_rate) as avg_accuracy,
                    AVG(consistency_rate) as avg_consistency,
                    MIN(quality_score) as min_quality_score,
                    MAX(quality_score) as max_quality_score
                FROM data_quality
                WHERE date BETWEEN ? AND ? AND data_type = ?
            '''

            stats = pd.read_sql_query(query, conn, params=(start_date, end_date, data_type))

            # 获取质量分布
            distribution_query = '''
                SELECT
                    CASE
                        WHEN quality_score >= 0.9 THEN 'excellent'
                        WHEN quality_score >= 0.8 THEN 'good'
                        WHEN quality_score >= 0.7 THEN 'fair'
                        ELSE 'poor'
                    END as quality_level,
                    COUNT(*) as count
                FROM data_quality
                WHERE date BETWEEN ? AND ? AND data_type = ?
                GROUP BY quality_level
            '''

            distribution = pd.read_sql_query(distribution_query, conn, params=(start_date, end_date, data_type))

            conn.close()

            # 组织结果
            result = {
                'period': f"{start_date} to {end_date}",
                'data_type': data_type,
                'total_records': int(stats.iloc[0]['total_records']) if not stats.empty else 0,
                'average_quality_score': float(stats.iloc[0]['avg_quality_score']) if not stats.empty and stats.iloc[0]['avg_quality_score'] else 0.0,
                'average_completeness': float(stats.iloc[0]['avg_completeness']) if not stats.empty and stats.iloc[0]['avg_completeness'] else 0.0,
                'average_accuracy': float(stats.iloc[0]['avg_accuracy']) if not stats.empty and stats.iloc[0]['avg_accuracy'] else 0.0,
                'average_consistency': float(stats.iloc[0]['avg_consistency']) if not stats.empty and stats.iloc[0]['avg_consistency'] else 0.0,
                'min_quality_score': float(stats.iloc[0]['min_quality_score']) if not stats.empty and stats.iloc[0]['min_quality_score'] else 0.0,
                'max_quality_score': float(stats.iloc[0]['max_quality_score']) if not stats.empty and stats.iloc[0]['max_quality_score'] else 0.0,
                'quality_distribution': distribution.set_index('quality_level')['count'].to_dict() if not distribution.empty else {}
            }

            return result

        except Exception as e:
            self.logger.error(f"获取数据质量统计失败: {e}")
            return {}

    def _get_from_cache(self, cache_key: str) -> Optional[pd.DataFrame]:
        """从缓存获取数据"""
        with self._cache_lock:
            if cache_key in self._cache:
                self._update_query_stats(0, cache_hit=True)
                return self._cache[cache_key].copy()
        return None

    def _save_to_cache(self, cache_key: str, data: pd.DataFrame):
        """保存数据到缓存"""
        with self._cache_lock:
            # 如果缓存已满，删除最旧的条目
            if len(self._cache) >= self._max_cache_size:
                oldest_key = next(iter(self._cache))
                del self._cache[oldest_key]

            self._cache[cache_key] = data.copy()

    def _update_query_stats(self, query_time: float, cache_hit: bool = False):
        """更新查询统计"""
        with self._stats_lock:
            self.query_stats.query_count += 1

            if cache_hit:
                self.query_stats.cache_hits += 1
            else:
                self.query_stats.cache_misses += 1
                self.query_stats.total_time += query_time

            # 计算平均时间和缓存命中率
            if self.query_stats.cache_misses > 0:
                self.query_stats.avg_time = self.query_stats.total_time / self.query_stats.cache_misses

            if self.query_stats.query_count > 0:
                self.query_stats.cache_hit_rate = self.query_stats.cache_hits / self.query_stats.query_count

    def get_query_stats(self) -> QueryStats:
        """获取查询统计"""
        with self._stats_lock:
            return QueryStats(
                query_count=self.query_stats.query_count,
                total_time=self.query_stats.total_time,
                avg_time=self.query_stats.avg_time,
                cache_hits=self.query_stats.cache_hits,
                cache_misses=self.query_stats.cache_misses,
                cache_hit_rate=self.query_stats.cache_hit_rate
            )

    def clear_cache(self):
        """清空缓存"""
        with self._cache_lock:
            self._cache.clear()
        self.logger.info("缓存已清空")

    def optimize_database(self):
        """优化数据库"""
        try:
            conn = sqlite3.connect(self.config.db_path)
            cursor = conn.cursor()

            # 分析表结构
            cursor.execute("ANALYZE")

            # 重建索引
            cursor.execute("REINDEX")

            # 清理数据库
            cursor.execute("VACUUM")

            conn.close()

            self.logger.info("数据库优化完成")

        except Exception as e:
            self.logger.error(f"数据库优化失败: {e}")

    def get_storage_info(self) -> Dict[str, Any]:
        """获取存储信息"""
        try:
            conn = sqlite3.connect(self.config.db_path)
            cursor = conn.cursor()

            # 获取表信息
            tables_info = {}
            tables = ['minute_data', 'daily_data', 'technical_indicators', 'data_quality']

            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                tables_info[table] = count

            # 获取数据库大小
            db_size = os.path.getsize(self.config.db_path) if os.path.exists(self.config.db_path) else 0

            conn.close()

            return {
                'database_path': self.config.db_path,
                'database_size_mb': db_size / (1024 * 1024),
                'tables_info': tables_info,
                'cache_size': len(self._cache),
                'query_stats': self.get_query_stats()
            }

        except Exception as e:
            self.logger.error(f"获取存储信息失败: {e}")
            return {}

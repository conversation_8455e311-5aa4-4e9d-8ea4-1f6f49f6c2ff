#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试可视化保存问题
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from quantization.visualization.result_visualizer import BacktestVisualizer, VisualizationConfig

def debug_save_figure():
    """调试save_figure方法"""
    print("调试save_figure方法...")
    
    try:
        config = VisualizationConfig(
            style="seaborn",
            figure_size=(10, 6),
            interactive=False,
            save_format="png",
            dpi=300
        )
        
        visualizer = BacktestVisualizer(config)
        
        # 创建测试图表
        import matplotlib.pyplot as plt
        fig, ax = plt.subplots(figsize=config.figure_size)
        
        x = np.linspace(0, 2*np.pi, 100)
        y = np.sin(x)
        
        ax.plot(x, y, 'b-', linewidth=2, label='sin(x)')
        ax.set_title('调试测试图表')
        ax.set_xlabel('X轴')
        ax.set_ylabel('Y轴')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 测试不同的保存路径
        test_paths = [
            ("output/debug/test1", "test1"),
            ("output/debug/test2.png", "test2.png"),
            ("debug_charts/test3", "test3")
        ]
        
        for save_path, expected_name in test_paths:
            print(f"\n测试保存路径: {save_path}")
            
            # 分离路径和文件名
            save_path_obj = Path(save_path)
            output_dir = str(save_path_obj.parent)
            filename = save_path_obj.name
            
            print(f"  输出目录: {output_dir}")
            print(f"  文件名: {filename}")
            
            # 调用save_figure
            saved_path = visualizer.save_figure(fig, filename, output_dir)
            
            if saved_path:
                print(f"  保存成功: {saved_path}")
                
                # 检查文件是否真的存在
                if Path(saved_path).exists():
                    print(f"  ✓ 文件确实存在")
                else:
                    print(f"  ✗ 文件不存在！")
            else:
                print(f"  ✗ 保存失败")
        
        plt.close(fig)
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()

def debug_plot_equity_curve():
    """调试plot_equity_curve方法"""
    print("\n调试plot_equity_curve方法...")
    
    try:
        config = VisualizationConfig(
            style="seaborn",
            figure_size=(12, 8),
            interactive=False,
            save_format="png",
            dpi=300
        )
        
        visualizer = BacktestVisualizer(config)
        
        # 生成模拟回测数据
        np.random.seed(42)
        n_days = 50
        dates = pd.date_range(start='2023-01-01', periods=n_days, freq='D')
        
        daily_returns = np.random.normal(0.001, 0.02, n_days)
        
        portfolio_history = []
        for i, date in enumerate(dates):
            portfolio_history.append({
                'date': date.strftime('%Y-%m-%d'),
                'daily_return': daily_returns[i],
                'portfolio_value': 1000000 * np.prod(1 + daily_returns[:i+1])
            })
        
        results = {
            'strategy_name': 'Debug Strategy',
            'start_date': '2023-01-01',
            'end_date': '2023-02-19',
            'portfolio_history': portfolio_history,
            'performance_metrics': {
                'total_return': 0.05,
                'annualized_return': 0.04,
                'annualized_volatility': 0.15,
                'sharpe_ratio': 0.27,
                'max_drawdown': -0.03
            }
        }
        
        # 测试不同的保存路径
        test_save_paths = [
            "output/debug_visualization/equity_curve_1",
            "debug_charts/equity_curve_2"
        ]
        
        for save_path in test_save_paths:
            print(f"\n测试保存路径: {save_path}")
            
            saved_path = visualizer.plot_equity_curve(results, save_path=save_path)
            
            if saved_path:
                print(f"  保存成功: {saved_path}")
                
                # 检查文件是否真的存在
                if Path(saved_path).exists():
                    print(f"  ✓ 文件确实存在")
                    print(f"  文件大小: {Path(saved_path).stat().st_size} bytes")
                else:
                    print(f"  ✗ 文件不存在！")
            else:
                print(f"  ✗ 保存失败")
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("=" * 50)
    print("可视化保存问题调试")
    print("=" * 50)
    
    debug_save_figure()
    debug_plot_equity_curve()
    
    print("\n" + "=" * 50)
    print("调试完成")

if __name__ == "__main__":
    main()

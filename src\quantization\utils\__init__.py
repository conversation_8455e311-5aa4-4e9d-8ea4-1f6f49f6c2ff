#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具模块

提供各种辅助工具和函数，包括：
- Logger: 日志管理
- Validators: 数据验证
- StockCodes: 股票代码工具
- RateLimiter: 频率限制器
- ParallelProcessor: 并行处理工具
- MemoryOptimizer: 内存优化工具
- Exceptions: 异常处理框架
- EnhancedLogger: 增强日志系统
- PerformanceMonitor: 性能监控系统
- HealthMonitor: 健康检查和自愈机制
"""

from quantization.utils.logger import get_logger
from quantization.utils.validators import validate_stock_code
from quantization.utils.stock_codes import StockCodeUtils
from quantization.utils.rate_limiter import RateLimiter
from quantization.utils.parallel_processor import (
    ThreadPoolManager, ProcessPoolManager, AsyncHttpClient,
    DataParallelProcessor, TaskResult, run_async_function, batch_process_with_progress
)
from quantization.utils.memory_optimizer import (
    MemoryMonitor, MemoryStats, DataFrameOptimizer, ObjectPool, MemoryManager,
    memory_profiler, memory_limit, LazyDataLoader, MemoryEfficientCalculator,
    optimize_dataframe_memory, get_memory_usage, memory_usage_report, memory_manager
)
# 新增模块可以通过直接导入使用
# 例如：from quantization.utils.exceptions import QuantizationBaseException
# 例如：from quantization.utils.enhanced_logger import get_enhanced_logger
# 例如：from quantization.utils.performance_monitor import performance_monitor
# 例如：from quantization.utils.health_monitor import health_monitor

__all__ = [
    "get_logger",
    "validate_stock_code",
    "StockCodeUtils",
    "RateLimiter",
    "ThreadPoolManager",
    "ProcessPoolManager",
    "AsyncHttpClient",
    "DataParallelProcessor",
    "TaskResult",
    "run_async_function",
    "batch_process_with_progress",
    "MemoryMonitor",
    "MemoryStats",
    "DataFrameOptimizer",
    "ObjectPool",
    "MemoryManager",
    "memory_profiler",
    "memory_limit",
    "LazyDataLoader",
    "MemoryEfficientCalculator",
    "optimize_dataframe_memory",
    "get_memory_usage",
    "memory_usage_report",
    "memory_manager",
]

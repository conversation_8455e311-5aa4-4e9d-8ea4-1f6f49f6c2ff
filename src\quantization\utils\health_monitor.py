#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康检查和自愈机制模块

提供系统健康检查、故障检测和自动恢复机制，提升系统可用性。
"""

import time
import threading
import asyncio
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
from abc import ABC, abstractmethod
import traceback

from quantization.utils.logger import get_logger


class HealthStatus(Enum):
    """健康状态"""
    HEALTHY = "healthy"
    WARNING = "warning"
    UNHEALTHY = "unhealthy"
    CRITICAL = "critical"


class RecoveryAction(Enum):
    """恢复动作"""
    RESTART_SERVICE = "restart_service"
    CLEAR_CACHE = "clear_cache"
    RECONNECT_DATABASE = "reconnect_database"
    SCALE_UP = "scale_up"
    SCALE_DOWN = "scale_down"
    CUSTOM = "custom"


@dataclass
class HealthCheckResult:
    """健康检查结果"""
    component: str
    status: HealthStatus
    message: str
    timestamp: float
    details: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}


@dataclass
class RecoveryPlan:
    """恢复计划"""
    component: str
    action: RecoveryAction
    priority: int  # 优先级，数字越小优先级越高
    max_attempts: int = 3
    retry_interval: float = 60.0  # 重试间隔（秒）
    custom_handler: Optional[Callable] = None


class HealthChecker(ABC):
    """健康检查器抽象基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = get_logger(f"HealthChecker.{name}")
    
    @abstractmethod
    async def check_health(self) -> HealthCheckResult:
        """执行健康检查"""
        pass
    
    def get_recovery_plan(self, result: HealthCheckResult) -> Optional[RecoveryPlan]:
        """获取恢复计划"""
        return None


class DatabaseHealthChecker(HealthChecker):
    """数据库健康检查器"""
    
    def __init__(self, connection_pool=None):
        super().__init__("database")
        self.connection_pool = connection_pool
    
    async def check_health(self) -> HealthCheckResult:
        """检查数据库健康状态"""
        try:
            if not self.connection_pool:
                return HealthCheckResult(
                    component=self.name,
                    status=HealthStatus.WARNING,
                    message="数据库连接池未配置",
                    timestamp=time.time()
                )
            
            # 检查连接池状态
            pool_stats = getattr(self.connection_pool, 'get_stats', lambda: {})()
            
            if pool_stats:
                active_connections = pool_stats.get('active_connections', 0)
                max_connections = pool_stats.get('max_connections', 10)
                
                if active_connections >= max_connections * 0.9:
                    return HealthCheckResult(
                        component=self.name,
                        status=HealthStatus.WARNING,
                        message=f"数据库连接池使用率过高: {active_connections}/{max_connections}",
                        timestamp=time.time(),
                        details=pool_stats
                    )
            
            # 执行简单查询测试连接
            # 这里应该根据实际数据库类型实现
            # 示例：SELECT 1
            
            return HealthCheckResult(
                component=self.name,
                status=HealthStatus.HEALTHY,
                message="数据库连接正常",
                timestamp=time.time(),
                details=pool_stats
            )
            
        except Exception as e:
            return HealthCheckResult(
                component=self.name,
                status=HealthStatus.UNHEALTHY,
                message=f"数据库连接异常: {str(e)}",
                timestamp=time.time(),
                details={'error': str(e), 'traceback': traceback.format_exc()}
            )
    
    def get_recovery_plan(self, result: HealthCheckResult) -> Optional[RecoveryPlan]:
        """获取数据库恢复计划"""
        if result.status in [HealthStatus.UNHEALTHY, HealthStatus.CRITICAL]:
            return RecoveryPlan(
                component=self.name,
                action=RecoveryAction.RECONNECT_DATABASE,
                priority=1,
                max_attempts=3,
                retry_interval=30.0
            )
        return None


class MemoryHealthChecker(HealthChecker):
    """内存健康检查器"""
    
    def __init__(self, warning_threshold: float = 80.0, critical_threshold: float = 95.0):
        super().__init__("memory")
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
    
    async def check_health(self) -> HealthCheckResult:
        """检查内存健康状态"""
        try:
            import psutil
            
            memory = psutil.virtual_memory()
            usage_percent = memory.percent
            
            if usage_percent >= self.critical_threshold:
                status = HealthStatus.CRITICAL
                message = f"内存使用率严重过高: {usage_percent:.1f}%"
            elif usage_percent >= self.warning_threshold:
                status = HealthStatus.WARNING
                message = f"内存使用率过高: {usage_percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"内存使用率正常: {usage_percent:.1f}%"
            
            return HealthCheckResult(
                component=self.name,
                status=status,
                message=message,
                timestamp=time.time(),
                details={
                    'usage_percent': usage_percent,
                    'total_mb': memory.total / (1024 * 1024),
                    'available_mb': memory.available / (1024 * 1024),
                    'used_mb': memory.used / (1024 * 1024)
                }
            )
            
        except Exception as e:
            return HealthCheckResult(
                component=self.name,
                status=HealthStatus.UNHEALTHY,
                message=f"内存检查异常: {str(e)}",
                timestamp=time.time(),
                details={'error': str(e)}
            )
    
    def get_recovery_plan(self, result: HealthCheckResult) -> Optional[RecoveryPlan]:
        """获取内存恢复计划"""
        if result.status == HealthStatus.CRITICAL:
            return RecoveryPlan(
                component=self.name,
                action=RecoveryAction.CLEAR_CACHE,
                priority=2,
                max_attempts=2,
                retry_interval=60.0
            )
        return None


class DiskHealthChecker(HealthChecker):
    """磁盘健康检查器"""
    
    def __init__(self, path: str = "/", warning_threshold: float = 80.0, critical_threshold: float = 95.0):
        super().__init__("disk")
        self.path = path
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
    
    async def check_health(self) -> HealthCheckResult:
        """检查磁盘健康状态"""
        try:
            import psutil
            
            disk = psutil.disk_usage(self.path)
            usage_percent = (disk.used / disk.total) * 100
            
            if usage_percent >= self.critical_threshold:
                status = HealthStatus.CRITICAL
                message = f"磁盘使用率严重过高: {usage_percent:.1f}%"
            elif usage_percent >= self.warning_threshold:
                status = HealthStatus.WARNING
                message = f"磁盘使用率过高: {usage_percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"磁盘使用率正常: {usage_percent:.1f}%"
            
            return HealthCheckResult(
                component=self.name,
                status=status,
                message=message,
                timestamp=time.time(),
                details={
                    'path': self.path,
                    'usage_percent': usage_percent,
                    'total_gb': disk.total / (1024 ** 3),
                    'free_gb': disk.free / (1024 ** 3),
                    'used_gb': disk.used / (1024 ** 3)
                }
            )
            
        except Exception as e:
            return HealthCheckResult(
                component=self.name,
                status=HealthStatus.UNHEALTHY,
                message=f"磁盘检查异常: {str(e)}",
                timestamp=time.time(),
                details={'error': str(e)}
            )


class ServiceHealthChecker(HealthChecker):
    """服务健康检查器"""
    
    def __init__(self, service_name: str, check_function: Callable):
        super().__init__(f"service_{service_name}")
        self.service_name = service_name
        self.check_function = check_function
    
    async def check_health(self) -> HealthCheckResult:
        """检查服务健康状态"""
        try:
            # 执行自定义检查函数
            if asyncio.iscoroutinefunction(self.check_function):
                result = await self.check_function()
            else:
                result = self.check_function()
            
            if isinstance(result, bool):
                if result:
                    return HealthCheckResult(
                        component=self.name,
                        status=HealthStatus.HEALTHY,
                        message=f"服务 {self.service_name} 运行正常",
                        timestamp=time.time()
                    )
                else:
                    return HealthCheckResult(
                        component=self.name,
                        status=HealthStatus.UNHEALTHY,
                        message=f"服务 {self.service_name} 运行异常",
                        timestamp=time.time()
                    )
            elif isinstance(result, HealthCheckResult):
                return result
            else:
                return HealthCheckResult(
                    component=self.name,
                    status=HealthStatus.HEALTHY,
                    message=f"服务 {self.service_name} 检查完成",
                    timestamp=time.time(),
                    details={'result': str(result)}
                )
                
        except Exception as e:
            return HealthCheckResult(
                component=self.name,
                status=HealthStatus.UNHEALTHY,
                message=f"服务 {self.service_name} 检查异常: {str(e)}",
                timestamp=time.time(),
                details={'error': str(e), 'traceback': traceback.format_exc()}
            )
    
    def get_recovery_plan(self, result: HealthCheckResult) -> Optional[RecoveryPlan]:
        """获取服务恢复计划"""
        if result.status in [HealthStatus.UNHEALTHY, HealthStatus.CRITICAL]:
            return RecoveryPlan(
                component=self.name,
                action=RecoveryAction.RESTART_SERVICE,
                priority=3,
                max_attempts=2,
                retry_interval=120.0
            )


class RecoveryManager:
    """恢复管理器"""

    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.recovery_handlers: Dict[RecoveryAction, Callable] = {}
        self.recovery_history: List[Dict[str, Any]] = []
        self.lock = threading.RLock()

        # 注册默认恢复处理器
        self._register_default_handlers()

    def _register_default_handlers(self):
        """注册默认恢复处理器"""
        self.recovery_handlers[RecoveryAction.CLEAR_CACHE] = self._clear_cache_handler
        self.recovery_handlers[RecoveryAction.RECONNECT_DATABASE] = self._reconnect_database_handler
        self.recovery_handlers[RecoveryAction.RESTART_SERVICE] = self._restart_service_handler

    def register_handler(self, action: RecoveryAction, handler: Callable):
        """注册自定义恢复处理器"""
        self.recovery_handlers[action] = handler
        self.logger.info(f"注册恢复处理器: {action.value}")

    async def execute_recovery(self, plan: RecoveryPlan) -> bool:
        """执行恢复计划"""
        self.logger.info(f"开始执行恢复计划: {plan.component} - {plan.action.value}")

        recovery_record = {
            'component': plan.component,
            'action': plan.action.value,
            'timestamp': time.time(),
            'attempts': 0,
            'success': False,
            'error': None
        }

        try:
            for attempt in range(plan.max_attempts):
                recovery_record['attempts'] = attempt + 1

                try:
                    if plan.custom_handler:
                        # 使用自定义处理器
                        if asyncio.iscoroutinefunction(plan.custom_handler):
                            success = await plan.custom_handler(plan)
                        else:
                            success = plan.custom_handler(plan)
                    else:
                        # 使用注册的处理器
                        handler = self.recovery_handlers.get(plan.action)
                        if not handler:
                            raise ValueError(f"未找到恢复处理器: {plan.action.value}")

                        if asyncio.iscoroutinefunction(handler):
                            success = await handler(plan)
                        else:
                            success = handler(plan)

                    if success:
                        recovery_record['success'] = True
                        self.logger.info(f"恢复成功: {plan.component} - {plan.action.value}")
                        break
                    else:
                        self.logger.warning(f"恢复失败，尝试 {attempt + 1}/{plan.max_attempts}: {plan.component}")

                        if attempt < plan.max_attempts - 1:
                            await asyncio.sleep(plan.retry_interval)

                except Exception as e:
                    error_msg = f"恢复执行异常，尝试 {attempt + 1}/{plan.max_attempts}: {str(e)}"
                    self.logger.error(error_msg)
                    recovery_record['error'] = str(e)

                    if attempt < plan.max_attempts - 1:
                        await asyncio.sleep(plan.retry_interval)

            if not recovery_record['success']:
                self.logger.error(f"恢复失败，已达到最大尝试次数: {plan.component} - {plan.action.value}")

        finally:
            with self.lock:
                self.recovery_history.append(recovery_record)
                # 限制历史记录大小
                if len(self.recovery_history) > 1000:
                    self.recovery_history.pop(0)

        return recovery_record['success']

    def _clear_cache_handler(self, plan: RecoveryPlan) -> bool:
        """清理缓存处理器"""
        try:
            # 这里应该实现实际的缓存清理逻辑
            # 例如：清理Redis缓存、内存缓存等
            self.logger.info(f"执行缓存清理: {plan.component}")

            # 示例：清理内存中的缓存
            import gc
            gc.collect()

            return True
        except Exception as e:
            self.logger.error(f"缓存清理失败: {str(e)}")
            return False

    def _reconnect_database_handler(self, plan: RecoveryPlan) -> bool:
        """重连数据库处理器"""
        try:
            # 这里应该实现实际的数据库重连逻辑
            self.logger.info(f"执行数据库重连: {plan.component}")

            # 示例：重新初始化连接池
            # connection_pool.reconnect()

            return True
        except Exception as e:
            self.logger.error(f"数据库重连失败: {str(e)}")
            return False

    def _restart_service_handler(self, plan: RecoveryPlan) -> bool:
        """重启服务处理器"""
        try:
            # 这里应该实现实际的服务重启逻辑
            self.logger.info(f"执行服务重启: {plan.component}")

            # 示例：重启特定服务组件
            # service_manager.restart_service(plan.component)

            return True
        except Exception as e:
            self.logger.error(f"服务重启失败: {str(e)}")
            return False

    def get_recovery_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取恢复历史"""
        with self.lock:
            return self.recovery_history[-limit:] if limit > 0 else self.recovery_history.copy()


class HealthMonitor:
    """
    健康监控主类

    集成健康检查和自愈机制的主要接口。
    """

    def __init__(self):
        self.checkers: Dict[str, HealthChecker] = {}
        self.recovery_manager = RecoveryManager()
        self.health_history: List[HealthCheckResult] = []
        self.lock = threading.RLock()
        self.logger = get_logger(self.__class__.__name__)

        # 监控线程
        self._monitoring = False
        self._monitor_thread = None
        self._check_interval = 60.0  # 60秒

        # 自愈开关
        self.auto_recovery_enabled = True

    def add_checker(self, checker: HealthChecker):
        """添加健康检查器"""
        with self.lock:
            self.checkers[checker.name] = checker

        self.logger.info(f"添加健康检查器: {checker.name}")

    def remove_checker(self, name: str):
        """移除健康检查器"""
        with self.lock:
            if name in self.checkers:
                del self.checkers[name]

        self.logger.info(f"移除健康检查器: {name}")

    def start_monitoring(self, interval: float = 60.0):
        """开始健康监控"""
        if self._monitoring:
            return

        self._check_interval = interval
        self._monitoring = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()

        self.logger.info(f"健康监控已启动，检查间隔: {interval}秒")

    def stop_monitoring(self):
        """停止健康监控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=10)

        self.logger.info("健康监控已停止")

    def _monitor_loop(self):
        """监控循环"""
        while self._monitoring:
            try:
                asyncio.run(self._check_all_health())
                time.sleep(self._check_interval)
            except Exception as e:
                self.logger.error(f"健康监控异常: {str(e)}")
                time.sleep(self._check_interval)

    async def _check_all_health(self):
        """检查所有组件健康状态"""
        with self.lock:
            checkers = self.checkers.copy()

        tasks = []
        for checker in checkers.values():
            tasks.append(self._check_single_health(checker))

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

    async def _check_single_health(self, checker: HealthChecker):
        """检查单个组件健康状态"""
        try:
            result = await checker.check_health()

            # 记录健康检查结果
            with self.lock:
                self.health_history.append(result)
                # 限制历史记录大小
                if len(self.health_history) > 5000:
                    self.health_history.pop(0)

            # 记录日志
            if result.status == HealthStatus.HEALTHY:
                self.logger.debug(f"健康检查正常: {result.component} - {result.message}")
            elif result.status == HealthStatus.WARNING:
                self.logger.warning(f"健康检查警告: {result.component} - {result.message}")
            else:
                self.logger.error(f"健康检查异常: {result.component} - {result.message}")

            # 自动恢复
            if self.auto_recovery_enabled and result.status in [HealthStatus.UNHEALTHY, HealthStatus.CRITICAL]:
                recovery_plan = checker.get_recovery_plan(result)
                if recovery_plan:
                    await self.recovery_manager.execute_recovery(recovery_plan)

        except Exception as e:
            self.logger.error(f"健康检查器 {checker.name} 执行异常: {str(e)}")

    async def check_health_once(self) -> Dict[str, HealthCheckResult]:
        """执行一次完整的健康检查"""
        with self.lock:
            checkers = self.checkers.copy()

        results = {}

        for name, checker in checkers.items():
            try:
                result = await checker.check_health()
                results[name] = result
            except Exception as e:
                results[name] = HealthCheckResult(
                    component=name,
                    status=HealthStatus.UNHEALTHY,
                    message=f"检查器执行异常: {str(e)}",
                    timestamp=time.time(),
                    details={'error': str(e)}
                )

        return results

    def get_health_summary(self) -> Dict[str, Any]:
        """获取健康状态摘要"""
        with self.lock:
            recent_results = self.health_history[-len(self.checkers):] if self.health_history else []

        summary = {
            'total_components': len(self.checkers),
            'last_check_time': max([r.timestamp for r in recent_results]) if recent_results else 0,
            'status_counts': {status.value: 0 for status in HealthStatus},
            'components': {}
        }

        # 获取每个组件的最新状态
        component_latest = {}
        for result in reversed(self.health_history):
            if result.component not in component_latest:
                component_latest[result.component] = result

        for component, result in component_latest.items():
            summary['status_counts'][result.status.value] += 1
            summary['components'][component] = {
                'status': result.status.value,
                'message': result.message,
                'timestamp': result.timestamp
            }

        # 整体健康状态
        if summary['status_counts'][HealthStatus.CRITICAL.value] > 0:
            summary['overall_status'] = HealthStatus.CRITICAL.value
        elif summary['status_counts'][HealthStatus.UNHEALTHY.value] > 0:
            summary['overall_status'] = HealthStatus.UNHEALTHY.value
        elif summary['status_counts'][HealthStatus.WARNING.value] > 0:
            summary['overall_status'] = HealthStatus.WARNING.value
        else:
            summary['overall_status'] = HealthStatus.HEALTHY.value

        return summary

    def enable_auto_recovery(self):
        """启用自动恢复"""
        self.auto_recovery_enabled = True
        self.logger.info("自动恢复已启用")

    def disable_auto_recovery(self):
        """禁用自动恢复"""
        self.auto_recovery_enabled = False
        self.logger.info("自动恢复已禁用")


# 全局健康监控实例
health_monitor = HealthMonitor()


# 便捷函数
def add_database_health_check(connection_pool=None):
    """添加数据库健康检查"""
    checker = DatabaseHealthChecker(connection_pool)
    health_monitor.add_checker(checker)


def add_memory_health_check(warning_threshold: float = 80.0, critical_threshold: float = 95.0):
    """添加内存健康检查"""
    checker = MemoryHealthChecker(warning_threshold, critical_threshold)
    health_monitor.add_checker(checker)


def add_disk_health_check(path: str = "/", warning_threshold: float = 80.0, critical_threshold: float = 95.0):
    """添加磁盘健康检查"""
    checker = DiskHealthChecker(path, warning_threshold, critical_threshold)
    health_monitor.add_checker(checker)


def add_service_health_check(service_name: str, check_function: Callable):
    """添加服务健康检查"""
    checker = ServiceHealthChecker(service_name, check_function)
    health_monitor.add_checker(checker)

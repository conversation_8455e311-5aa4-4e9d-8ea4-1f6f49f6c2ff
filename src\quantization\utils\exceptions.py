#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异常处理模块

提供统一的异常处理框架，包括自定义异常类、异常捕获装饰器和异常恢复机制。
"""

import functools
import traceback
import time
from typing import Any, Callable, Dict, List, Optional, Type, Union
from enum import Enum
from dataclasses import dataclass

from quantization.utils.logger import get_logger


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"          # 低级错误，不影响主要功能
    MEDIUM = "medium"    # 中级错误，影响部分功能
    HIGH = "high"        # 高级错误，影响核心功能
    CRITICAL = "critical" # 严重错误，系统无法正常运行


@dataclass
class ErrorContext:
    """错误上下文信息"""
    error_id: str
    timestamp: float
    severity: ErrorSeverity
    module: str
    function: str
    message: str
    details: Dict[str, Any]
    stack_trace: str
    retry_count: int = 0


class QuantizationBaseException(Exception):
    """量化系统基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.severity = severity
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.timestamp = time.time()
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error_code': self.error_code,
            'message': self.message,
            'severity': self.severity.value,
            'details': self.details,
            'timestamp': self.timestamp,
            'exception_type': self.__class__.__name__
        }


class DataAcquisitionError(QuantizationBaseException):
    """数据获取异常"""
    
    def __init__(self, message: str, source: str = "", **kwargs):
        super().__init__(message, ErrorSeverity.HIGH, **kwargs)
        self.details['source'] = source


class DataValidationError(QuantizationBaseException):
    """数据验证异常"""
    
    def __init__(self, message: str, field: str = "", value: Any = None, **kwargs):
        super().__init__(message, ErrorSeverity.MEDIUM, **kwargs)
        self.details.update({
            'field': field,
            'value': str(value) if value is not None else None
        })


class StrategyExecutionError(QuantizationBaseException):
    """策略执行异常"""
    
    def __init__(self, message: str, strategy_name: str = "", **kwargs):
        super().__init__(message, ErrorSeverity.HIGH, **kwargs)
        self.details['strategy_name'] = strategy_name


class BacktestError(QuantizationBaseException):
    """回测异常"""
    
    def __init__(self, message: str, period: str = "", **kwargs):
        super().__init__(message, ErrorSeverity.MEDIUM, **kwargs)
        self.details['period'] = period


class DatabaseError(QuantizationBaseException):
    """数据库异常"""
    
    def __init__(self, message: str, operation: str = "", **kwargs):
        super().__init__(message, ErrorSeverity.HIGH, **kwargs)
        self.details['operation'] = operation


class CacheError(QuantizationBaseException):
    """缓存异常"""
    
    def __init__(self, message: str, cache_type: str = "", **kwargs):
        super().__init__(message, ErrorSeverity.LOW, **kwargs)
        self.details['cache_type'] = cache_type


class ConfigurationError(QuantizationBaseException):
    """配置异常"""
    
    def __init__(self, message: str, config_key: str = "", **kwargs):
        super().__init__(message, ErrorSeverity.CRITICAL, **kwargs)
        self.details['config_key'] = config_key


class NetworkError(QuantizationBaseException):
    """网络异常"""
    
    def __init__(self, message: str, url: str = "", status_code: int = 0, **kwargs):
        super().__init__(message, ErrorSeverity.MEDIUM, **kwargs)
        self.details.update({
            'url': url,
            'status_code': status_code
        })


class ExceptionHandler:
    """
    异常处理器
    
    提供统一的异常处理、记录和恢复机制。
    """
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.error_history: List[ErrorContext] = []
        self.max_history_size = 1000
        
        # 异常处理策略
        self.handlers: Dict[Type[Exception], Callable] = {}
        self.recovery_strategies: Dict[Type[Exception], Callable] = {}
        
        # 重试配置
        self.retry_config = {
            NetworkError: {'max_retries': 3, 'delay': 1.0, 'backoff': 2.0},
            DataAcquisitionError: {'max_retries': 2, 'delay': 0.5, 'backoff': 1.5},
            DatabaseError: {'max_retries': 2, 'delay': 1.0, 'backoff': 2.0},
        }
    
    def register_handler(self, exception_type: Type[Exception], handler: Callable):
        """注册异常处理器"""
        self.handlers[exception_type] = handler
    
    def register_recovery_strategy(self, exception_type: Type[Exception], strategy: Callable):
        """注册异常恢复策略"""
        self.recovery_strategies[exception_type] = strategy
    
    def handle_exception(
        self, 
        exception: Exception, 
        context: Optional[Dict[str, Any]] = None
    ) -> ErrorContext:
        """
        处理异常
        
        Args:
            exception: 异常对象
            context: 上下文信息
            
        Returns:
            错误上下文
        """
        # 创建错误上下文
        error_context = self._create_error_context(exception, context)
        
        # 记录错误历史
        self._record_error(error_context)
        
        # 记录日志
        self._log_exception(error_context)
        
        # 执行自定义处理器
        self._execute_handler(exception, error_context)
        
        return error_context
    
    def _create_error_context(
        self, 
        exception: Exception, 
        context: Optional[Dict[str, Any]] = None
    ) -> ErrorContext:
        """创建错误上下文"""
        import uuid
        import inspect
        
        # 获取调用栈信息
        frame = inspect.currentframe()
        caller_frame = frame.f_back.f_back if frame and frame.f_back else None
        
        module_name = "unknown"
        function_name = "unknown"
        
        if caller_frame:
            module_name = caller_frame.f_globals.get('__name__', 'unknown')
            function_name = caller_frame.f_code.co_name
        
        # 确定错误严重程度
        if isinstance(exception, QuantizationBaseException):
            severity = exception.severity
            details = exception.details.copy()
        else:
            severity = ErrorSeverity.MEDIUM
            details = {}
        
        if context:
            details.update(context)
        
        return ErrorContext(
            error_id=str(uuid.uuid4()),
            timestamp=time.time(),
            severity=severity,
            module=module_name,
            function=function_name,
            message=str(exception),
            details=details,
            stack_trace=traceback.format_exc(),
            retry_count=0
        )
    
    def _record_error(self, error_context: ErrorContext):
        """记录错误历史"""
        self.error_history.append(error_context)
        
        # 限制历史记录大小
        if len(self.error_history) > self.max_history_size:
            self.error_history.pop(0)
    
    def _log_exception(self, error_context: ErrorContext):
        """记录异常日志"""
        log_data = {
            'error_id': error_context.error_id,
            'severity': error_context.severity.value,
            'module': error_context.module,
            'function': error_context.function,
            'details': error_context.details
        }
        
        if error_context.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(f"严重错误: {error_context.message}", extra=log_data)
        elif error_context.severity == ErrorSeverity.HIGH:
            self.logger.error(f"高级错误: {error_context.message}", extra=log_data)
        elif error_context.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(f"中级错误: {error_context.message}", extra=log_data)
        else:
            self.logger.info(f"低级错误: {error_context.message}", extra=log_data)
    
    def _execute_handler(self, exception: Exception, error_context: ErrorContext):
        """执行自定义异常处理器"""
        exception_type = type(exception)
        
        # 查找匹配的处理器
        handler = None
        for exc_type, exc_handler in self.handlers.items():
            if isinstance(exception, exc_type):
                handler = exc_handler
                break
        
        if handler:
            try:
                handler(exception, error_context)
            except Exception as e:
                self.logger.error(f"异常处理器执行失败: {str(e)}")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        if not self.error_history:
            return {
                'total_errors': 0,
                'by_severity': {},
                'by_module': {},
                'recent_errors': []
            }
        
        # 按严重程度统计
        by_severity = {}
        for error in self.error_history:
            severity = error.severity.value
            by_severity[severity] = by_severity.get(severity, 0) + 1
        
        # 按模块统计
        by_module = {}
        for error in self.error_history:
            module = error.module
            by_module[module] = by_module.get(module, 0) + 1
        
        # 最近的错误
        recent_errors = [
            {
                'error_id': error.error_id,
                'timestamp': error.timestamp,
                'severity': error.severity.value,
                'message': error.message,
                'module': error.module
            }
            for error in self.error_history[-10:]
        ]
        
        return {
            'total_errors': len(self.error_history),
            'by_severity': by_severity,
            'by_module': by_module,
            'recent_errors': recent_errors
        }


# 全局异常处理器实例
exception_handler = ExceptionHandler()


def exception_handler_decorator(
    exceptions: Union[Type[Exception], tuple] = Exception,
    reraise: bool = False,
    default_return: Any = None,
    log_level: str = "error"
):
    """
    异常处理装饰器

    Args:
        exceptions: 要捕获的异常类型
        reraise: 是否重新抛出异常
        default_return: 异常时的默认返回值
        log_level: 日志级别
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except exceptions as e:
                # 处理异常
                error_context = exception_handler.handle_exception(
                    e,
                    context={
                        'function': func.__name__,
                        'args': str(args)[:200],  # 限制长度
                        'kwargs': str(kwargs)[:200]
                    }
                )

                if reraise:
                    raise

                return default_return

        return wrapper
    return decorator


def retry_on_exception(
    exceptions: Union[Type[Exception], tuple] = Exception,
    max_retries: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    jitter: bool = True
):
    """
    异常重试装饰器

    Args:
        exceptions: 要重试的异常类型
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 退避倍数
        jitter: 是否添加随机抖动
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import random

            last_exception = None
            current_delay = delay

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e

                    if attempt == max_retries:
                        # 最后一次尝试失败，记录异常并重新抛出
                        exception_handler.handle_exception(
                            e,
                            context={
                                'function': func.__name__,
                                'attempt': attempt + 1,
                                'max_retries': max_retries
                            }
                        )
                        raise

                    # 计算延迟时间
                    actual_delay = current_delay
                    if jitter:
                        actual_delay *= (0.5 + random.random())

                    # 记录重试日志
                    logger = get_logger("RetryDecorator")
                    logger.warning(
                        f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败，"
                        f"{actual_delay:.2f}秒后重试: {str(e)}"
                    )

                    time.sleep(actual_delay)
                    current_delay *= backoff

            # 理论上不会到达这里
            if last_exception:
                raise last_exception

        return wrapper
    return decorator


def circuit_breaker(
    failure_threshold: int = 5,
    recovery_timeout: float = 60.0,
    expected_exception: Type[Exception] = Exception
):
    """
    熔断器装饰器

    Args:
        failure_threshold: 失败阈值
        recovery_timeout: 恢复超时时间（秒）
        expected_exception: 预期的异常类型
    """
    def decorator(func: Callable) -> Callable:
        # 熔断器状态
        state = {
            'failure_count': 0,
            'last_failure_time': 0,
            'state': 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
        }

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            current_time = time.time()

            # 检查熔断器状态
            if state['state'] == 'OPEN':
                if current_time - state['last_failure_time'] > recovery_timeout:
                    state['state'] = 'HALF_OPEN'
                    logger = get_logger("CircuitBreaker")
                    logger.info(f"熔断器 {func.__name__} 进入半开状态")
                else:
                    raise QuantizationBaseException(
                        f"熔断器开启，函数 {func.__name__} 暂时不可用",
                        ErrorSeverity.HIGH
                    )

            try:
                result = func(*args, **kwargs)

                # 成功执行，重置失败计数
                if state['failure_count'] > 0:
                    state['failure_count'] = 0
                    if state['state'] == 'HALF_OPEN':
                        state['state'] = 'CLOSED'
                        logger = get_logger("CircuitBreaker")
                        logger.info(f"熔断器 {func.__name__} 恢复正常")

                return result

            except expected_exception as e:
                state['failure_count'] += 1
                state['last_failure_time'] = current_time

                if state['failure_count'] >= failure_threshold:
                    state['state'] = 'OPEN'
                    logger = get_logger("CircuitBreaker")
                    logger.error(f"熔断器 {func.__name__} 开启，失败次数: {state['failure_count']}")

                # 记录异常
                exception_handler.handle_exception(
                    e,
                    context={
                        'function': func.__name__,
                        'circuit_breaker_state': state['state'],
                        'failure_count': state['failure_count']
                    }
                )

                raise

        return wrapper
    return decorator


class ExceptionRecovery:
    """
    异常恢复机制

    提供自动恢复和降级策略。
    """

    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.recovery_strategies: Dict[str, Callable] = {}
        self.fallback_strategies: Dict[str, Callable] = {}

    def register_recovery_strategy(self, exception_type: str, strategy: Callable):
        """注册恢复策略"""
        self.recovery_strategies[exception_type] = strategy

    def register_fallback_strategy(self, function_name: str, fallback: Callable):
        """注册降级策略"""
        self.fallback_strategies[function_name] = fallback

    def attempt_recovery(self, exception: Exception, context: Dict[str, Any]) -> bool:
        """
        尝试异常恢复

        Args:
            exception: 异常对象
            context: 上下文信息

        Returns:
            是否恢复成功
        """
        exception_type = type(exception).__name__

        if exception_type in self.recovery_strategies:
            try:
                strategy = self.recovery_strategies[exception_type]
                result = strategy(exception, context)

                if result:
                    self.logger.info(f"异常恢复成功: {exception_type}")
                    return True

            except Exception as e:
                self.logger.error(f"异常恢复失败: {str(e)}")

        return False

    def get_fallback_result(self, function_name: str, *args, **kwargs) -> Any:
        """
        获取降级结果

        Args:
            function_name: 函数名
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            降级结果
        """
        if function_name in self.fallback_strategies:
            try:
                fallback = self.fallback_strategies[function_name]
                result = fallback(*args, **kwargs)

                self.logger.info(f"使用降级策略: {function_name}")
                return result

            except Exception as e:
                self.logger.error(f"降级策略执行失败: {str(e)}")

        return None


# 全局异常恢复实例
exception_recovery = ExceptionRecovery()


# 便捷函数
def handle_exception(exception: Exception, context: Optional[Dict[str, Any]] = None) -> ErrorContext:
    """处理异常的便捷函数"""
    return exception_handler.handle_exception(exception, context)


def get_error_statistics() -> Dict[str, Any]:
    """获取错误统计信息的便捷函数"""
    return exception_handler.get_error_statistics()

"""
Data validation utilities for A-Share market data
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from ..config.settings import Config
from .logger import get_validator_logger

class DataValidator:
    """Validator for A-Share market data"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize data validator
        
        Args:
            config: Configuration instance
        """
        self.config = config or Config()
        self.validation_config = self.config.get_validation_config()
        self.logger = get_validator_logger(config)
    
    def validate_price_data(self, data: pd.DataFrame, stock_code: str) -> Tuple[bool, List[str]]:
        """
        Validate OHLCV price data
        
        Args:
            data: DataFrame with OHLCV data
            stock_code: Stock code for logging
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        errors = []
        
        if not self.validation_config['enabled']:
            return True, []
        
        # Check if data is empty
        if data.empty:
            errors.append("Data is empty")
            return False, errors
        
        # Check minimum data points
        if len(data) < self.validation_config['min_data_points']:
            errors.append(f"Insufficient data points: {len(data)} < {self.validation_config['min_data_points']}")
        
        # Check required columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            errors.append(f"Missing required columns: {missing_columns}")
            return False, errors
        
        # Check for null values
        null_counts = data[required_columns].isnull().sum()
        if null_counts.any():
            errors.append(f"Null values found: {null_counts[null_counts > 0].to_dict()}")
        
        # Check OHLC relationships
        invalid_ohlc = self._validate_ohlc_relationships(data)
        if invalid_ohlc:
            errors.append(f"Invalid OHLC relationships found in {len(invalid_ohlc)} rows")
        
        # Check for extreme price changes
        extreme_changes = self._validate_price_changes(data, stock_code)
        if extreme_changes:
            errors.append(f"Extreme price changes detected: {len(extreme_changes)} instances")
        
        # Check volume data
        volume_errors = self._validate_volume_data(data)
        if volume_errors:
            errors.extend(volume_errors)
        
        # Check date continuity
        date_errors = self._validate_date_continuity(data)
        if date_errors:
            errors.extend(date_errors)
        
        is_valid = len(errors) == 0
        
        if not is_valid:
            self.logger.warning(f"Validation failed for {stock_code}: {'; '.join(errors)}")
        else:
            self.logger.debug(f"Validation passed for {stock_code}")
        
        return is_valid, errors
    
    def _validate_ohlc_relationships(self, data: pd.DataFrame) -> List[int]:
        """
        Validate OHLC price relationships
        
        Args:
            data: DataFrame with OHLC data
            
        Returns:
            List[int]: Indices of rows with invalid relationships
        """
        invalid_rows = []
        
        for idx, row in data.iterrows():
            # High should be >= Open, Close, Low
            if not (row['high'] >= row['open'] and 
                   row['high'] >= row['close'] and 
                   row['high'] >= row['low']):
                invalid_rows.append(idx)
                continue
            
            # Low should be <= Open, Close, High
            if not (row['low'] <= row['open'] and 
                   row['low'] <= row['close'] and 
                   row['low'] <= row['high']):
                invalid_rows.append(idx)
        
        return invalid_rows
    
    def _validate_price_changes(self, data: pd.DataFrame, stock_code: str) -> List[Tuple[int, float]]:
        """
        Validate daily price changes for extreme movements
        
        Args:
            data: DataFrame with price data
            stock_code: Stock code for context
            
        Returns:
            List[Tuple[int, float]]: (index, change_percent) for extreme changes
        """
        extreme_changes = []
        max_change = self.validation_config['max_price_change_percent']
        
        if len(data) < 2:
            return extreme_changes
        
        # Calculate daily returns
        data_sorted = data.sort_index()
        prev_close = data_sorted['close'].shift(1)
        daily_returns = ((data_sorted['close'] - prev_close) / prev_close * 100).abs()
        
        # Find extreme changes
        extreme_mask = daily_returns > max_change
        extreme_indices = data_sorted[extreme_mask].index.tolist()
        extreme_values = daily_returns[extreme_mask].tolist()
        
        extreme_changes = list(zip(extreme_indices, extreme_values))
        
        return extreme_changes
    
    def _validate_volume_data(self, data: pd.DataFrame) -> List[str]:
        """
        Validate volume data
        
        Args:
            data: DataFrame with volume data
            
        Returns:
            List[str]: Volume validation errors
        """
        errors = []
        
        if 'volume' not in data.columns:
            return errors
        
        # Check for negative volumes
        negative_volumes = (data['volume'] < 0).sum()
        if negative_volumes > 0:
            errors.append(f"Negative volumes found: {negative_volumes} instances")
        
        # Check for zero volumes (suspicious for trading days)
        zero_volumes = (data['volume'] == 0).sum()
        if zero_volumes > len(data) * 0.1:  # More than 10% zero volumes
            errors.append(f"High number of zero volumes: {zero_volumes} instances")
        
        # Check for extreme volume spikes
        if len(data) > 1:
            volume_median = data['volume'].median()
            if volume_median > 0:
                volume_ratio = data['volume'] / volume_median
                extreme_volumes = (volume_ratio > 100).sum()  # 100x median
                if extreme_volumes > 0:
                    errors.append(f"Extreme volume spikes: {extreme_volumes} instances")
        
        return errors
    
    def _validate_date_continuity(self, data: pd.DataFrame) -> List[str]:
        """
        Validate date continuity (check for large gaps)
        
        Args:
            data: DataFrame with date index
            
        Returns:
            List[str]: Date continuity errors
        """
        errors = []
        
        if not isinstance(data.index, pd.DatetimeIndex):
            return errors
        
        if len(data) < 2:
            return errors
        
        # Sort by date
        data_sorted = data.sort_index()
        dates = data_sorted.index
        
        # Calculate gaps between consecutive dates
        date_diffs = dates[1:] - dates[:-1]
        
        # Find large gaps (more than 30 days, accounting for weekends/holidays)
        large_gaps = date_diffs > timedelta(days=30)
        
        if large_gaps.any():
            gap_count = large_gaps.sum()
            max_gap = date_diffs.max().days
            errors.append(f"Large date gaps found: {gap_count} gaps, max gap: {max_gap} days")
        
        return errors
    
    def validate_financial_data(self, data: pd.DataFrame, stock_code: str) -> Tuple[bool, List[str]]:
        """
        Validate financial statement data
        
        Args:
            data: DataFrame with financial data
            stock_code: Stock code
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        errors = []
        
        if not self.validation_config['enabled']:
            return True, []
        
        if data.empty:
            errors.append("Financial data is empty")
            return False, errors
        
        # Check for required financial metrics
        common_metrics = ['revenue', 'net_income', 'total_assets', 'total_equity']
        available_metrics = [col for col in common_metrics if col in data.columns]
        
        if not available_metrics:
            errors.append("No common financial metrics found")
        
        # Check for reasonable financial ratios
        if 'total_assets' in data.columns and 'total_equity' in data.columns:
            # Debt-to-equity ratio check
            debt_to_equity = (data['total_assets'] - data['total_equity']) / data['total_equity']
            extreme_leverage = (debt_to_equity > 10).sum()  # More than 10:1 leverage
            if extreme_leverage > 0:
                errors.append(f"Extreme leverage ratios: {extreme_leverage} instances")
        
        is_valid = len(errors) == 0
        return is_valid, errors
    
    def validate_index_data(self, data: pd.DataFrame, index_code: str) -> Tuple[bool, List[str]]:
        """
        Validate market index data
        
        Args:
            data: DataFrame with index data
            index_code: Index code
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        errors = []
        
        if not self.validation_config['enabled']:
            return True, []
        
        # Similar validation to price data but with different thresholds
        if data.empty:
            errors.append("Index data is empty")
            return False, errors
        
        # Check required columns
        required_columns = ['open', 'high', 'low', 'close']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            errors.append(f"Missing required columns: {missing_columns}")
        
        # Index-specific validations
        if len(data) > 1:
            # Index values should generally be positive
            negative_values = (data['close'] <= 0).sum()
            if negative_values > 0:
                errors.append(f"Non-positive index values: {negative_values} instances")
        
        is_valid = len(errors) == 0
        return is_valid, errors
    
    def clean_data(self, data: pd.DataFrame, stock_code: str) -> pd.DataFrame:
        """
        Clean and fix common data issues
        
        Args:
            data: Raw data DataFrame
            stock_code: Stock code for logging
            
        Returns:
            pd.DataFrame: Cleaned data
        """
        if data.empty:
            return data
        
        cleaned_data = data.copy()
        
        # Remove duplicate dates
        if isinstance(cleaned_data.index, pd.DatetimeIndex):
            cleaned_data = cleaned_data[~cleaned_data.index.duplicated(keep='first')]
        
        # Forward fill small gaps in price data
        price_columns = ['open', 'high', 'low', 'close']
        available_price_cols = [col for col in price_columns if col in cleaned_data.columns]
        
        if available_price_cols:
            # Only forward fill up to 3 consecutive NaN values
            cleaned_data[available_price_cols] = cleaned_data[available_price_cols].ffill(limit=3)
        
        # Remove rows with all NaN values
        cleaned_data = cleaned_data.dropna(how='all')
        
        # Log cleaning actions
        original_len = len(data)
        cleaned_len = len(cleaned_data)
        if original_len != cleaned_len:
            self.logger.info(f"Cleaned {stock_code}: {original_len} -> {cleaned_len} rows")
        
        return cleaned_data

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存优化工具

提供内存使用优化功能，包括数据结构优化、内存监控和垃圾回收管理。
"""

import gc
import sys
import psutil
import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, Union, Iterator, Tuple, Callable
from dataclasses import dataclass
from contextlib import contextmanager
import weakref
import threading
import time
from collections import defaultdict

from quantization.utils.logger import get_logger


@dataclass
class MemoryStats:
    """内存统计信息"""
    total_memory: float  # 总内存 (MB)
    available_memory: float  # 可用内存 (MB)
    used_memory: float  # 已用内存 (MB)
    memory_percent: float  # 内存使用百分比
    process_memory: float  # 进程内存 (MB)
    gc_objects: int  # 垃圾回收对象数量


class MemoryMonitor:
    """
    内存监控器
    
    监控系统和进程内存使用情况，提供内存统计和警告功能。
    """
    
    def __init__(self, warning_threshold: float = 80.0, critical_threshold: float = 90.0):
        """
        初始化内存监控器
        
        Args:
            warning_threshold: 内存使用警告阈值（百分比）
            critical_threshold: 内存使用严重阈值（百分比）
        """
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        
        self.process = psutil.Process()
        self.logger = get_logger(self.__class__.__name__)
        
        # 监控历史
        self.memory_history = []
        self.max_history_size = 1000
        
        # 回调函数
        self.warning_callbacks = []
        self.critical_callbacks = []
    
    def get_memory_stats(self) -> MemoryStats:
        """
        获取当前内存统计信息
        
        Returns:
            内存统计信息
        """
        # 系统内存信息
        memory = psutil.virtual_memory()
        
        # 进程内存信息
        process_memory = self.process.memory_info()
        
        # 垃圾回收信息
        gc_objects = len(gc.get_objects())
        
        stats = MemoryStats(
            total_memory=memory.total / (1024 * 1024),
            available_memory=memory.available / (1024 * 1024),
            used_memory=memory.used / (1024 * 1024),
            memory_percent=memory.percent,
            process_memory=process_memory.rss / (1024 * 1024),
            gc_objects=gc_objects
        )
        
        # 记录历史
        self._record_memory_history(stats)
        
        # 检查阈值
        self._check_thresholds(stats)
        
        return stats
    
    def _record_memory_history(self, stats: MemoryStats):
        """记录内存使用历史"""
        timestamp = time.time()
        self.memory_history.append((timestamp, stats))
        
        # 限制历史记录大小
        if len(self.memory_history) > self.max_history_size:
            self.memory_history.pop(0)
    
    def _check_thresholds(self, stats: MemoryStats):
        """检查内存使用阈值"""
        if stats.memory_percent >= self.critical_threshold:
            self.logger.critical(f"内存使用达到严重阈值: {stats.memory_percent:.1f}%")
            for callback in self.critical_callbacks:
                try:
                    callback(stats)
                except Exception as e:
                    self.logger.error(f"执行严重阈值回调失败: {str(e)}")
        
        elif stats.memory_percent >= self.warning_threshold:
            self.logger.warning(f"内存使用达到警告阈值: {stats.memory_percent:.1f}%")
            for callback in self.warning_callbacks:
                try:
                    callback(stats)
                except Exception as e:
                    self.logger.error(f"执行警告阈值回调失败: {str(e)}")
    
    def add_warning_callback(self, callback):
        """添加警告回调函数"""
        self.warning_callbacks.append(callback)
    
    def add_critical_callback(self, callback):
        """添加严重阈值回调函数"""
        self.critical_callbacks.append(callback)
    
    def get_memory_trend(self, minutes: int = 10) -> Dict[str, float]:
        """
        获取内存使用趋势
        
        Args:
            minutes: 分析的时间范围（分钟）
            
        Returns:
            趋势分析结果
        """
        if len(self.memory_history) < 2:
            return {'trend': 0.0, 'average': 0.0, 'peak': 0.0}
        
        current_time = time.time()
        cutoff_time = current_time - (minutes * 60)
        
        # 过滤指定时间范围内的数据
        recent_data = [
            (timestamp, stats) for timestamp, stats in self.memory_history
            if timestamp >= cutoff_time
        ]
        
        if len(recent_data) < 2:
            return {'trend': 0.0, 'average': 0.0, 'peak': 0.0}
        
        # 计算趋势
        memory_values = [stats.memory_percent for _, stats in recent_data]
        
        # 简单线性趋势计算
        n = len(memory_values)
        x_values = list(range(n))
        
        # 计算斜率
        x_mean = sum(x_values) / n
        y_mean = sum(memory_values) / n
        
        numerator = sum((x - x_mean) * (y - y_mean) for x, y in zip(x_values, memory_values))
        denominator = sum((x - x_mean) ** 2 for x in x_values)
        
        trend = numerator / denominator if denominator != 0 else 0.0
        
        return {
            'trend': trend,
            'average': y_mean,
            'peak': max(memory_values),
            'samples': n
        }


class DataFrameOptimizer:
    """
    DataFrame内存优化器
    
    优化pandas DataFrame的内存使用。
    """
    
    @staticmethod
    def optimize_dtypes(df: pd.DataFrame, aggressive: bool = False) -> pd.DataFrame:
        """
        优化DataFrame的数据类型
        
        Args:
            df: 要优化的DataFrame
            aggressive: 是否使用激进优化
            
        Returns:
            优化后的DataFrame
        """
        logger = get_logger("DataFrameOptimizer")
        original_memory = df.memory_usage(deep=True).sum() / (1024 * 1024)
        
        optimized_df = df.copy()
        
        for col in optimized_df.columns:
            col_type = optimized_df[col].dtype
            
            # 优化数值类型
            if col_type in ['int64', 'int32', 'int16', 'int8']:
                c_min = optimized_df[col].min()
                c_max = optimized_df[col].max()
                
                if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:
                    optimized_df[col] = optimized_df[col].astype(np.int8)
                elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:
                    optimized_df[col] = optimized_df[col].astype(np.int16)
                elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:
                    optimized_df[col] = optimized_df[col].astype(np.int32)
            
            elif col_type in ['float64', 'float32']:
                c_min = optimized_df[col].min()
                c_max = optimized_df[col].max()
                
                if c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:
                    optimized_df[col] = optimized_df[col].astype(np.float32)
            
            # 优化字符串类型
            elif col_type == 'object':
                if optimized_df[col].dtype == 'object':
                    # 尝试转换为category类型
                    unique_count = optimized_df[col].nunique()
                    total_count = len(optimized_df[col])
                    
                    # 如果唯一值比例小于50%，转换为category
                    if unique_count / total_count < 0.5:
                        optimized_df[col] = optimized_df[col].astype('category')
                    elif aggressive:
                        # 激进模式：尝试转换为字符串
                        try:
                            optimized_df[col] = optimized_df[col].astype('string')
                        except:
                            pass
        
        optimized_memory = optimized_df.memory_usage(deep=True).sum() / (1024 * 1024)
        memory_reduction = (original_memory - optimized_memory) / original_memory * 100
        
        logger.info(
            f"DataFrame内存优化完成 - "
            f"原始: {original_memory:.2f}MB, "
            f"优化后: {optimized_memory:.2f}MB, "
            f"减少: {memory_reduction:.1f}%"
        )
        
        return optimized_df
    
    @staticmethod
    def chunk_processor(
        df: pd.DataFrame, 
        chunk_size: int = 10000,
        process_func: Optional[callable] = None
    ) -> Iterator[pd.DataFrame]:
        """
        分块处理大型DataFrame
        
        Args:
            df: 要处理的DataFrame
            chunk_size: 块大小
            process_func: 处理函数
            
        Yields:
            处理后的DataFrame块
        """
        total_rows = len(df)
        
        for start_idx in range(0, total_rows, chunk_size):
            end_idx = min(start_idx + chunk_size, total_rows)
            chunk = df.iloc[start_idx:end_idx].copy()
            
            if process_func:
                chunk = process_func(chunk)
            
            yield chunk
            
            # 强制垃圾回收
            del chunk
            gc.collect()


class ObjectPool:
    """
    对象池
    
    重用对象以减少内存分配和垃圾回收开销。
    """
    
    def __init__(self, factory_func: callable, max_size: int = 100):
        """
        初始化对象池
        
        Args:
            factory_func: 对象创建函数
            max_size: 池的最大大小
        """
        self.factory_func = factory_func
        self.max_size = max_size
        self.pool = []
        self.lock = threading.Lock()
        
        self.logger = get_logger(self.__class__.__name__)
        
        # 统计信息
        self.stats = {
            'created': 0,
            'reused': 0,
            'returned': 0
        }
    
    def get_object(self):
        """获取对象"""
        with self.lock:
            if self.pool:
                obj = self.pool.pop()
                self.stats['reused'] += 1
                return obj
            else:
                obj = self.factory_func()
                self.stats['created'] += 1
                return obj
    
    def return_object(self, obj):
        """归还对象"""
        with self.lock:
            if len(self.pool) < self.max_size:
                # 重置对象状态（如果有reset方法）
                if hasattr(obj, 'reset'):
                    obj.reset()
                
                self.pool.append(obj)
                self.stats['returned'] += 1
            # 如果池已满，让对象被垃圾回收
    
    @contextmanager
    def get_object_context(self):
        """上下文管理器方式获取对象"""
        obj = self.get_object()
        try:
            yield obj
        finally:
            self.return_object(obj)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取池统计信息"""
        with self.lock:
            return {
                'pool_size': len(self.pool),
                'max_size': self.max_size,
                'created': self.stats['created'],
                'reused': self.stats['reused'],
                'returned': self.stats['returned'],
                'reuse_rate': self.stats['reused'] / (self.stats['created'] + self.stats['reused']) if (self.stats['created'] + self.stats['reused']) > 0 else 0
            }


class MemoryManager:
    """
    内存管理器
    
    提供统一的内存管理功能。
    """
    
    def __init__(self):
        self.monitor = MemoryMonitor()
        self.object_pools = {}
        self.weak_references = weakref.WeakSet()
        
        self.logger = get_logger(self.__class__.__name__)
        
        # 设置默认的内存警告回调
        self.monitor.add_warning_callback(self._on_memory_warning)
        self.monitor.add_critical_callback(self._on_memory_critical)
    
    def _on_memory_warning(self, stats: MemoryStats):
        """内存警告回调"""
        self.logger.warning(f"内存使用警告 - 使用率: {stats.memory_percent:.1f}%")
        self.force_gc()
    
    def _on_memory_critical(self, stats: MemoryStats):
        """内存严重警告回调"""
        self.logger.critical(f"内存使用严重 - 使用率: {stats.memory_percent:.1f}%")
        self.aggressive_cleanup()
    
    def force_gc(self):
        """强制垃圾回收"""
        collected = gc.collect()
        self.logger.info(f"强制垃圾回收完成，回收对象数: {collected}")
    
    def aggressive_cleanup(self):
        """激进清理"""
        # 强制垃圾回收
        self.force_gc()
        
        # 清理对象池
        for pool_name, pool in self.object_pools.items():
            with pool.lock:
                cleared = len(pool.pool)
                pool.pool.clear()
                self.logger.info(f"清理对象池 {pool_name}，清理对象数: {cleared}")
        
        # 再次垃圾回收
        self.force_gc()
    
    def create_object_pool(self, name: str, factory_func: callable, max_size: int = 100) -> ObjectPool:
        """创建对象池"""
        pool = ObjectPool(factory_func, max_size)
        self.object_pools[name] = pool
        return pool
    
    def get_object_pool(self, name: str) -> Optional[ObjectPool]:
        """获取对象池"""
        return self.object_pools.get(name)
    
    def register_weak_reference(self, obj):
        """注册弱引用"""
        self.weak_references.add(obj)
    
    def get_memory_report(self) -> Dict[str, Any]:
        """获取内存报告"""
        stats = self.monitor.get_memory_stats()
        trend = self.monitor.get_memory_trend()
        
        pool_stats = {}
        for name, pool in self.object_pools.items():
            pool_stats[name] = pool.get_stats()
        
        return {
            'current_stats': stats,
            'trend': trend,
            'object_pools': pool_stats,
            'weak_references_count': len(self.weak_references),
            'gc_stats': {
                'counts': gc.get_count(),
                'threshold': gc.get_threshold()
            }
        }


# 全局内存管理器实例
memory_manager = MemoryManager()


# 装饰器和上下文管理器
@contextmanager
def memory_profiler(operation_name: str = "Operation"):
    """
    内存分析上下文管理器

    Args:
        operation_name: 操作名称
    """
    logger = get_logger("MemoryProfiler")

    # 记录开始状态
    start_stats = memory_manager.monitor.get_memory_stats()
    start_time = time.time()

    logger.info(f"{operation_name} 开始 - 内存使用: {start_stats.process_memory:.2f}MB")

    try:
        yield
    finally:
        # 记录结束状态
        end_stats = memory_manager.monitor.get_memory_stats()
        end_time = time.time()

        memory_diff = end_stats.process_memory - start_stats.process_memory
        time_diff = end_time - start_time

        logger.info(
            f"{operation_name} 完成 - "
            f"内存变化: {memory_diff:+.2f}MB, "
            f"最终内存: {end_stats.process_memory:.2f}MB, "
            f"耗时: {time_diff:.2f}秒"
        )


def memory_limit(max_memory_mb: float):
    """
    内存限制装饰器

    Args:
        max_memory_mb: 最大内存限制（MB）
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_logger("MemoryLimit")

            # 检查当前内存使用
            current_stats = memory_manager.monitor.get_memory_stats()

            if current_stats.process_memory > max_memory_mb:
                logger.error(
                    f"内存使用超限 - 当前: {current_stats.process_memory:.2f}MB, "
                    f"限制: {max_memory_mb:.2f}MB"
                )
                raise MemoryError(f"内存使用超过限制: {max_memory_mb}MB")

            return func(*args, **kwargs)

        return wrapper
    return decorator


class LazyDataLoader:
    """
    懒加载数据加载器

    按需加载数据以减少内存占用。
    """

    def __init__(self, data_source: Union[str, Callable], chunk_size: int = 1000):
        """
        初始化懒加载器

        Args:
            data_source: 数据源（文件路径或加载函数）
            chunk_size: 块大小
        """
        self.data_source = data_source
        self.chunk_size = chunk_size
        self.cache = {}
        self.cache_size_limit = 100  # 缓存块数限制

        self.logger = get_logger(self.__class__.__name__)

    def get_chunk(self, chunk_id: int) -> Optional[pd.DataFrame]:
        """
        获取数据块

        Args:
            chunk_id: 块ID

        Returns:
            数据块
        """
        # 检查缓存
        if chunk_id in self.cache:
            return self.cache[chunk_id]

        # 加载数据块
        try:
            if isinstance(self.data_source, str):
                # 从文件加载
                start_row = chunk_id * self.chunk_size
                chunk = pd.read_csv(
                    self.data_source,
                    skiprows=start_row,
                    nrows=self.chunk_size
                )
            elif callable(self.data_source):
                # 从函数加载
                chunk = self.data_source(chunk_id, self.chunk_size)  # type: ignore
            else:
                raise ValueError("不支持的数据源类型")

            # 缓存管理
            if len(self.cache) >= self.cache_size_limit:
                # 移除最旧的缓存项
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]

            self.cache[chunk_id] = chunk
            return chunk

        except Exception as e:
            self.logger.error(f"加载数据块 {chunk_id} 失败: {str(e)}")
            return None

    def iterate_chunks(self) -> Iterator[Tuple[int, pd.DataFrame]]:
        """
        迭代所有数据块

        Yields:
            (chunk_id, chunk_data) 元组
        """
        chunk_id = 0

        while True:
            chunk = self.get_chunk(chunk_id)
            if chunk is None or len(chunk) == 0:
                break

            yield chunk_id, chunk
            chunk_id += 1

    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        gc.collect()


class MemoryEfficientCalculator:
    """
    内存高效计算器

    提供内存高效的数值计算方法。
    """

    @staticmethod
    def rolling_calculation(
        data: pd.Series,
        window: int,
        func: Callable,
        chunk_size: int = 10000
    ) -> pd.Series:
        """
        内存高效的滚动计算

        Args:
            data: 输入数据
            window: 窗口大小
            func: 计算函数
            chunk_size: 块大小

        Returns:
            计算结果
        """
        if len(data) <= chunk_size:
            # 数据量小，直接计算
            return data.rolling(window=window).apply(func)

        # 分块计算
        results = []

        for i in range(0, len(data), chunk_size):
            # 考虑窗口重叠
            start_idx = max(0, i - window + 1)
            end_idx = min(len(data), i + chunk_size)

            chunk = data.iloc[start_idx:end_idx]
            chunk_result = chunk.rolling(window=window).apply(func)

            # 只保留当前块的结果
            if i > 0:
                chunk_result = chunk_result.iloc[window-1:]

            results.append(chunk_result)

            # 清理内存
            del chunk, chunk_result
            gc.collect()

        return pd.concat(results, ignore_index=True)  # type: ignore

    @staticmethod
    def batch_correlation(
        data1: pd.Series,
        data2: pd.Series,
        batch_size: int = 1000
    ) -> float:
        """
        批量相关性计算

        Args:
            data1: 数据序列1
            data2: 数据序列2
            batch_size: 批次大小

        Returns:
            相关系数
        """
        if len(data1) != len(data2):
            raise ValueError("数据序列长度不匹配")

        if len(data1) <= batch_size:
            return data1.corr(data2)

        # 分批计算协方差和方差
        n = len(data1)
        sum_x = sum_y = sum_xy = sum_x2 = sum_y2 = 0

        for i in range(0, n, batch_size):
            end_idx = min(i + batch_size, n)

            batch_x = data1.iloc[i:end_idx]
            batch_y = data2.iloc[i:end_idx]

            sum_x += batch_x.sum()
            sum_y += batch_y.sum()
            sum_xy += (batch_x * batch_y).sum()
            sum_x2 += (batch_x ** 2).sum()
            sum_y2 += (batch_y ** 2).sum()

            # 清理内存
            del batch_x, batch_y
            gc.collect()

        # 计算相关系数
        numerator = n * sum_xy - sum_x * sum_y
        denominator = ((n * sum_x2 - sum_x ** 2) * (n * sum_y2 - sum_y ** 2)) ** 0.5

        return numerator / denominator if denominator != 0 else 0


# 工具函数
def optimize_dataframe_memory(df: pd.DataFrame, aggressive: bool = False) -> pd.DataFrame:
    """
    优化DataFrame内存使用的便捷函数

    Args:
        df: 要优化的DataFrame
        aggressive: 是否使用激进优化

    Returns:
        优化后的DataFrame
    """
    return DataFrameOptimizer.optimize_dtypes(df, aggressive)


def get_memory_usage(obj) -> float:
    """
    获取对象内存使用量（MB）

    Args:
        obj: 要检查的对象

    Returns:
        内存使用量（MB）
    """
    if isinstance(obj, pd.DataFrame):
        return obj.memory_usage(deep=True).sum() / (1024 * 1024)
    elif isinstance(obj, pd.Series):
        return obj.memory_usage(deep=True) / (1024 * 1024)
    else:
        return sys.getsizeof(obj) / (1024 * 1024)


def memory_usage_report() -> Dict[str, Any]:
    """
    生成内存使用报告

    Returns:
        内存使用报告
    """
    return memory_manager.get_memory_report()

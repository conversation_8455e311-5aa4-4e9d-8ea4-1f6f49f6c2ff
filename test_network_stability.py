#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试网络稳定性增强
验证重试机制和错误恢复功能
"""

import os
import sys
import time
import logging

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('network_stability_test.log', encoding='utf-8')
        ]
    )

def test_network_session_setup():
    """测试网络会话配置"""
    print("测试网络会话配置...")
    
    try:
        from src.quantization.strategies.chinext_minute_data_downloader import ChinextMinuteDataDownloader
        
        # 创建下载器实例
        downloader = ChinextMinuteDataDownloader()
        
        print("✓ 下载器实例创建成功")
        
        # 检查网络会话是否正确配置
        if hasattr(downloader, 'session'):
            print("✓ 网络会话已配置")
            print(f"  最大重试次数: {downloader.max_retries}")
            print(f"  重试延迟: {downloader.retry_delay}秒")
            print(f"  超时时间: {downloader.timeout}秒")
            print(f"  退避因子: {downloader.backoff_factor}")
            print(f"  抖动范围: {downloader.jitter_range}")
        else:
            print("✗ 网络会话未配置")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_download_with_retry():
    """测试带重试机制的下载"""
    print("\n测试带重试机制的下载...")
    
    try:
        from src.quantization.strategies.chinext_minute_data_downloader import ChinextMinuteDataDownloader
        
        # 创建下载器实例
        downloader = ChinextMinuteDataDownloader()
        
        # 测试股票代码（创业板）
        test_stocks = ['300001.SZ', '300002.SZ', '300003.SZ']
        test_date = '2024-06-28'  # 使用一个交易日
        
        print(f"测试下载 {len(test_stocks)} 只股票的分时数据...")
        
        results = []
        for stock_code in test_stocks:
            print(f"下载 {stock_code} {test_date}...")
            success, count, error = downloader.download_stock_minute_data(stock_code, test_date)
            results.append((stock_code, success, count, error))
            
            if success:
                print(f"  ✓ 成功，记录数: {count}")
            else:
                print(f"  ✗ 失败: {error}")
            
            # 添加延迟，避免请求过于频繁
            time.sleep(1)
        
        # 统计结果
        success_count = sum(1 for _, success, _, _ in results if success)
        total_count = len(results)
        
        print(f"\n下载结果统计:")
        print(f"  成功: {success_count}/{total_count}")
        print(f"  成功率: {success_count/total_count:.1%}")
        
        return success_count > 0  # 至少有一个成功就算通过
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_download():
    """测试批量下载功能"""
    print("\n测试批量下载功能...")
    
    try:
        from src.quantization.strategies.chinext_minute_data_downloader import ChinextMinuteDataDownloader
        
        # 创建下载器实例
        downloader = ChinextMinuteDataDownloader()
        
        # 测试股票代码
        test_stocks = ['300015.SZ', '300033.SZ']  # 减少测试数量
        test_dates = ['2024-06-28']  # 使用一个交易日
        
        print(f"批量下载 {len(test_stocks)} 只股票，{len(test_dates)} 个交易日...")
        
        # 执行批量下载
        results = downloader.batch_download_minute_data(test_stocks, test_dates)
        
        print(f"批量下载结果:")
        for stock_code, result in results.items():
            print(f"  {stock_code}: {result}")
        
        # 检查是否有成功的下载
        total_success = sum(results.values())
        total_tasks = len(test_stocks) * len(test_dates)
        
        print(f"\n批量下载统计:")
        print(f"  总任务数: {total_tasks}")
        print(f"  成功数: {total_success}")
        print(f"  成功率: {total_success/total_tasks:.1%}")
        
        return total_success > 0
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n测试错误处理...")
    
    try:
        from src.quantization.strategies.chinext_minute_data_downloader import ChinextMinuteDataDownloader
        
        # 创建下载器实例
        downloader = ChinextMinuteDataDownloader()
        
        # 测试无效股票代码
        invalid_stock = '999999.SZ'
        test_date = '2024-06-28'
        
        print(f"测试无效股票代码: {invalid_stock}")
        success, count, error = downloader.download_stock_minute_data(invalid_stock, test_date)
        
        if not success:
            print(f"✓ 正确处理无效股票代码: {error}")
        else:
            print(f"✗ 未正确处理无效股票代码")
            return False
        
        # 测试无效日期
        valid_stock = '300001.SZ'
        invalid_date = '2024-12-31'  # 未来日期
        
        print(f"测试无效日期: {invalid_date}")
        success, count, error = downloader.download_stock_minute_data(valid_stock, invalid_date)
        
        if not success:
            print(f"✓ 正确处理无效日期: {error}")
        else:
            print(f"✗ 未正确处理无效日期")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    setup_logging()
    
    print("="*60)
    print("网络稳定性增强测试")
    print("="*60)
    
    test_results = {
        '网络会话配置': test_network_session_setup(),
        '带重试机制下载': test_download_with_retry(),
        '批量下载功能': test_batch_download(),
        '错误处理机制': test_error_handling()
    }
    
    print("\n" + "="*60)
    print("测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    print(f"成功率: {passed/total:.1%}")
    
    if passed >= total * 0.75:  # 75%通过率即可
        print("\n🎉 网络稳定性增强测试基本通过！")
    else:
        print(f"\n⚠️  有 {total-passed} 项测试失败，需要进一步检查。")
    
    return passed >= total * 0.75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""
Database operations for A-Share data storage
"""

import sqlite3
import pandas as pd
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date
from pathlib import Path
import json
from ..config.settings import Config
from ..utils.logger import get_database_logger

class DatabaseManager:
    """Database manager for A-Share data storage"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize database manager
        
        Args:
            config: Configuration instance
        """
        self.config = config or Config()
        self.logger = get_database_logger(config)
        self.db_config = self.config.get_database_config()
        
        # Create data directory
        self.config.create_directories()
        
        # Initialize database
        self._init_database()
    
    def _init_database(self):
        """Initialize database and create tables"""
        try:
            # For SQLite, extract path from URL
            if self.db_config['url'].startswith('sqlite:///'):
                self.db_path = self.db_config['url'][10:]  # Remove 'sqlite:///'
                self._init_sqlite()
            else:
                # For other databases, would implement connection pooling
                self.logger.warning("Only SQLite is currently supported")
                
        except Exception as e:
            self.logger.error(f"Database initialization failed: {e}")
            raise
    
    def _init_sqlite(self):
        """Initialize SQLite database"""
        # Ensure directory exists
        db_path = Path(self.db_path)
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create tables
        with sqlite3.connect(self.db_path) as conn:
            self._create_tables(conn)
        
        self.logger.info(f"SQLite database initialized at {self.db_path}")
    
    def _create_tables(self, conn: sqlite3.Connection):
        """Create database tables"""
        
        # Stock price data table
        conn.execute('''
            CREATE TABLE IF NOT EXISTS stock_prices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code TEXT NOT NULL,
                date DATE NOT NULL,
                open REAL,
                high REAL,
                low REAL,
                close REAL,
                volume INTEGER,
                amount REAL,
                turnover_rate REAL,
                pct_change REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(stock_code, date)
            )
        ''')
        
        # Stock information table
        conn.execute('''
            CREATE TABLE IF NOT EXISTS stock_info (
                stock_code TEXT PRIMARY KEY,
                name TEXT,
                exchange TEXT,
                sector TEXT,
                industry TEXT,
                market_cap REAL,
                listing_date DATE,
                info_json TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Financial data table
        conn.execute('''
            CREATE TABLE IF NOT EXISTS financial_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code TEXT NOT NULL,
                report_date DATE NOT NULL,
                report_type TEXT NOT NULL,
                revenue REAL,
                net_income REAL,
                total_assets REAL,
                total_equity REAL,
                eps REAL,
                roe REAL,
                financial_json TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(stock_code, report_date, report_type)
            )
        ''')
        
        # Index data table
        conn.execute('''
            CREATE TABLE IF NOT EXISTS index_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                index_code TEXT NOT NULL,
                date DATE NOT NULL,
                open REAL,
                high REAL,
                low REAL,
                close REAL,
                volume INTEGER,
                amount REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(index_code, date)
            )
        ''')
        
        # Data update log table
        conn.execute('''
            CREATE TABLE IF NOT EXISTS data_update_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code TEXT NOT NULL,
                data_type TEXT NOT NULL,
                start_date DATE,
                end_date DATE,
                record_count INTEGER,
                source TEXT,
                status TEXT,
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create indices for better performance
        indices = [
            'CREATE INDEX IF NOT EXISTS idx_stock_prices_code_date ON stock_prices(stock_code, date)',
            'CREATE INDEX IF NOT EXISTS idx_stock_prices_date ON stock_prices(date)',
            'CREATE INDEX IF NOT EXISTS idx_financial_data_code_date ON financial_data(stock_code, report_date)',
            'CREATE INDEX IF NOT EXISTS idx_index_data_code_date ON index_data(index_code, date)',
            'CREATE INDEX IF NOT EXISTS idx_update_log_code_type ON data_update_log(stock_code, data_type)'
        ]
        
        for index_sql in indices:
            conn.execute(index_sql)
        
        conn.commit()
    
    def save_stock_data(self, stock_code: str, data: pd.DataFrame, source: str = 'unknown') -> bool:
        """
        Save stock price data to database
        
        Args:
            stock_code: Stock code
            data: DataFrame with stock data
            source: Data source name
            
        Returns:
            bool: True if successful
        """
        try:
            if data.empty:
                return False
            
            # Prepare data for insertion
            data_copy = data.copy()
            data_copy['stock_code'] = stock_code
            
            # Reset index to get date as column
            if isinstance(data_copy.index, pd.DatetimeIndex):
                data_copy = data_copy.reset_index()
            
            # Ensure required columns exist
            required_columns = ['stock_code', 'date', 'open', 'high', 'low', 'close']
            missing_columns = [col for col in required_columns if col not in data_copy.columns]
            if missing_columns:
                self.logger.error(f"Missing required columns: {missing_columns}")
                return False
            
            # Save to database
            with sqlite3.connect(self.db_path) as conn:
                # Use INSERT OR REPLACE to handle duplicates
                data_copy.to_sql('stock_prices', conn, if_exists='append', index=False, method='multi')
                
                # Log the update
                self._log_data_update(conn, stock_code, 'stock_prices', 
                                    data_copy['date'].min(), data_copy['date'].max(),
                                    len(data_copy), source, 'success')
            
            self.logger.info(f"Saved {len(data_copy)} records for {stock_code}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save stock data for {stock_code}: {e}")
            self._log_data_update(None, stock_code, 'stock_prices', None, None, 0, source, 'error', str(e))
            return False
    
    def get_stock_data(self, 
                      stock_code: str,
                      start_date: Optional[Union[str, date, datetime]] = None,
                      end_date: Optional[Union[str, date, datetime]] = None) -> Optional[pd.DataFrame]:
        """
        Get stock data from database
        
        Args:
            stock_code: Stock code
            start_date: Start date (optional)
            end_date: End date (optional)
            
        Returns:
            pd.DataFrame: Stock data or None if not found
        """
        try:
            query = "SELECT * FROM stock_prices WHERE stock_code = ?"
            params = [stock_code]
            
            if start_date:
                query += " AND date >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND date <= ?"
                params.append(end_date)
            
            query += " ORDER BY date"
            
            with sqlite3.connect(self.db_path) as conn:
                data = pd.read_sql_query(query, conn, params=params, parse_dates=['date'])
            
            if data.empty:
                return None
            
            # Set date as index
            data = data.set_index('date')
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to get stock data for {stock_code}: {e}")
            return None
    
    def save_stock_info(self, stock_code: str, info: Dict[str, Any]) -> bool:
        """
        Save stock information to database
        
        Args:
            stock_code: Stock code
            info: Stock information dictionary
            
        Returns:
            bool: True if successful
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Convert info dict to JSON for storage
                info_json = json.dumps(info, ensure_ascii=False)
                
                conn.execute('''
                    INSERT OR REPLACE INTO stock_info 
                    (stock_code, name, exchange, sector, industry, market_cap, info_json, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    stock_code,
                    info.get('name'),
                    info.get('exchange'),
                    info.get('sector'),
                    info.get('industry'),
                    info.get('market_cap'),
                    info_json
                ))
                
                conn.commit()
            
            self.logger.debug(f"Saved stock info for {stock_code}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save stock info for {stock_code}: {e}")
            return False
    
    def get_stock_info(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """
        Get stock information from database
        
        Args:
            stock_code: Stock code
            
        Returns:
            Dict: Stock information or None if not found
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT * FROM stock_info WHERE stock_code = ?",
                    (stock_code,)
                )
                row = cursor.fetchone()
            
            if not row:
                return None
            
            # Convert row to dict
            columns = [desc[0] for desc in cursor.description]
            info = dict(zip(columns, row))
            
            # Parse JSON info if available
            if info.get('info_json'):
                try:
                    additional_info = json.loads(info['info_json'])
                    info.update(additional_info)
                except:
                    pass
            
            return info
            
        except Exception as e:
            self.logger.error(f"Failed to get stock info for {stock_code}: {e}")
            return None
    
    def get_available_stocks(self, exchange: Optional[str] = None) -> List[str]:
        """
        Get list of available stocks in database
        
        Args:
            exchange: Exchange filter
            
        Returns:
            List[str]: List of stock codes
        """
        try:
            query = "SELECT DISTINCT stock_code FROM stock_prices"
            params = []
            
            if exchange:
                # Filter by exchange (assuming stock codes follow standard format)
                if exchange.upper() == 'SH':
                    query += " WHERE stock_code LIKE '6%.SH'"
                elif exchange.upper() == 'SZ':
                    query += " WHERE stock_code LIKE '0%.SZ' OR stock_code LIKE '3%.SZ'"
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(query, params)
                stocks = [row[0] for row in cursor.fetchall()]
            
            return stocks
            
        except Exception as e:
            self.logger.error(f"Failed to get available stocks: {e}")
            return []
    
    def _log_data_update(self, conn: Optional[sqlite3.Connection], stock_code: str, 
                        data_type: str, start_date: Optional[date], end_date: Optional[date],
                        record_count: int, source: str, status: str, error_message: str = None):
        """Log data update operation"""
        try:
            if conn is None:
                conn = sqlite3.connect(self.db_path)
                should_close = True
            else:
                should_close = False
            
            conn.execute('''
                INSERT INTO data_update_log 
                (stock_code, data_type, start_date, end_date, record_count, source, status, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (stock_code, data_type, start_date, end_date, record_count, source, status, error_message))
            
            conn.commit()
            
            if should_close:
                conn.close()
                
        except Exception as e:
            self.logger.error(f"Failed to log data update: {e}")
    
    def get_data_coverage(self, stock_code: str) -> Dict[str, Any]:
        """
        Get data coverage information for a stock
        
        Args:
            stock_code: Stock code
            
        Returns:
            Dict: Coverage information
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT 
                        MIN(date) as start_date,
                        MAX(date) as end_date,
                        COUNT(*) as record_count
                    FROM stock_prices 
                    WHERE stock_code = ?
                ''', (stock_code,))
                
                row = cursor.fetchone()
            
            if row and row[2] > 0:  # record_count > 0
                return {
                    'stock_code': stock_code,
                    'start_date': row[0],
                    'end_date': row[1],
                    'record_count': row[2]
                }
            else:
                return {
                    'stock_code': stock_code,
                    'start_date': None,
                    'end_date': None,
                    'record_count': 0
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get data coverage for {stock_code}: {e}")
            return {'stock_code': stock_code, 'error': str(e)}
    
    def close(self):
        """Close database connections"""
        # For SQLite, connections are closed automatically
        # For other databases, would close connection pools here
        pass

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块

提供统一的配置管理功能。
"""

import os
from typing import Dict, Any, Optional
from pathlib import Path

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False


class Config:
    """
    配置管理类
    
    提供统一的配置加载和管理功能。
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self._config = {}
        self._load_config()
    
    def _load_config(self) -> None:
        """加载配置文件"""
        # 默认配置
        self._config = self._get_default_config()
        
        # 加载配置文件
        if self.config_file and Path(self.config_file).exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    if YAML_AVAILABLE:
                        file_config = yaml.safe_load(f)
                        if file_config:
                            self._config.update(file_config)
                    else:
                        print("警告: PyYAML库未安装，跳过YAML配置文件加载")
            except Exception as e:
                print(f"加载配置文件失败: {e}")
        
        # 加载环境变量
        self._load_env_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 数据源配置
            'data_sources': {
                'akshare': {
                    'enabled': True,
                    'rate_limit': 0.5,  # 秒
                    'timeout': 30,
                    'max_retries': 3
                },
                'web_scraper': {
                    'enabled': True,
                    'rate_limit': 1.0,
                    'timeout': 30,
                    'max_retries': 3,
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            },
            
            # 数据库配置
            'database': {
                'type': 'sqlite',
                'path': 'data/ashare_data.db',
                'pool_size': 10,
                'max_overflow': 20
            },
            
            # 缓存配置
            'cache': {
                'enabled': True,
                'type': 'file',
                'path': 'data/cache',
                'expire_time': 3600  # 秒
            },
            
            # 日志配置
            'logging': {
                'level': 'INFO',
                'file': 'logs/quantization.log',
                'console': True,
                'max_size': 10485760,  # 10MB
                'backup_count': 5
            },
            
            # 策略配置
            'strategies': {
                'chinext_selection': {
                    'max_stocks': 10,
                    'min_market_cap': 15,  # 亿元
                    'max_market_cap': 300,  # 亿元
                    'min_volume_ratio': 2.0,
                    'williams_r_threshold': 80,
                    'big_order_threshold': 0.4
                }
            },
            
            # 回测配置
            'backtesting': {
                'initial_capital': 1000000,  # 初始资金
                'commission_rate': 0.0003,   # 手续费率
                'slippage_rate': 0.001,      # 滑点率
                'holding_period': 4,         # 持仓天数
                'max_positions': 10          # 最大持仓数
            }
        }
    
    def _load_env_config(self) -> None:
        """加载环境变量配置"""
        env_mappings = {
            'QF_LOG_LEVEL': 'logging.level',
            'QF_DB_PATH': 'database.path',
            'QF_CACHE_PATH': 'cache.path',
            'QF_INITIAL_CAPITAL': 'backtesting.initial_capital'
        }
        
        for env_key, config_key in env_mappings.items():
            env_value = os.getenv(env_key)
            if env_value:
                self._set_nested_value(config_key, env_value)
    
    def _set_nested_value(self, key_path: str, value: Any) -> None:
        """设置嵌套配置值"""
        keys = key_path.split('.')
        config = self._config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 类型转换
        if isinstance(value, str):
            if value.lower() in ('true', 'false'):
                value = value.lower() == 'true'
            elif value.isdigit():
                value = int(value)
            elif value.replace('.', '').isdigit():
                value = float(value)
        
        config[keys[-1]] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键（支持点分隔的嵌套键）
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        config = self._config
        
        try:
            for k in keys:
                config = config[k]
            return config
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        self._set_nested_value(key, value)
    
    def update(self, config: Dict[str, Any]) -> None:
        """
        更新配置
        
        Args:
            config: 配置字典
        """
        self._config.update(config)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        获取完整配置字典
        
        Returns:
            配置字典
        """
        return self._config.copy()
    
    def save(self, file_path: Optional[str] = None) -> None:
        """
        保存配置到文件
        
        Args:
            file_path: 保存路径
        """
        save_path = file_path or self.config_file
        if not save_path:
            raise ValueError("未指定保存路径")
        
        # 确保目录存在
        Path(save_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(save_path, 'w', encoding='utf-8') as f:
            yaml.dump(self._config, f, default_flow_style=False, 
                     allow_unicode=True, indent=2)


# 全局配置实例
_global_config = None


def get_config(config_file: Optional[str] = None) -> Config:
    """
    获取全局配置实例
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        配置实例
    """
    global _global_config
    
    if _global_config is None:
        _global_config = Config(config_file)
    
    return _global_config

"""
数据完整性测试脚本

全面测试A股数据采集框架的数据完整性和真实性，包括：
- 数据源真实性验证
- 技术指标计算准确性测试
- 数据清洗和验证机制测试
- 回测指标计算验证
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from data_acquisition import DataManager
from data_acquisition.utils.enhanced_validator import EnhancedDataValidator
from data_acquisition.utils.technical_indicators import TechnicalIndicators

def test_data_source_authenticity():
    """测试数据源真实性"""
    print("🔍 测试数据源真实性...")
    
    dm = DataManager()
    
    # 测试获取真实股票数据
    test_stocks = ['000001.SZ', '600000.SH', '600519.SH']
    
    for stock_code in test_stocks:
        print(f"\n  测试 {stock_code}...")
        
        try:
            # 获取最近30天的数据
            end_date = date.today()
            start_date = end_date - timedelta(days=60)  # 多获取一些以确保有足够的交易日
            
            data = dm.get_stock_data(stock_code, start_date, end_date)
            
            if data is not None and not data.empty:
                print(f"    ✅ 成功获取 {len(data)} 条记录")
                print(f"    📅 日期范围: {data.index.min()} 到 {data.index.max()}")
                print(f"    💰 价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
                print(f"    📊 成交量范围: {data['volume'].min():,.0f} - {data['volume'].max():,.0f}")
                
                # 验证数据合理性
                if data['close'].min() > 0 and data['volume'].min() >= 0:
                    print(f"    ✅ 数据合理性检查通过")
                else:
                    print(f"    ❌ 数据合理性检查失败")
                    
            else:
                print(f"    ⚠️  未获取到数据")
                
        except Exception as e:
            print(f"    ❌ 获取数据失败: {e}")
    
    dm.cleanup()
    print("✅ 数据源真实性测试完成")

def test_enhanced_validation():
    """测试增强数据验证"""
    print("\n🔧 测试增强数据验证...")
    
    validator = EnhancedDataValidator()
    dm = DataManager()
    
    # 获取测试数据
    test_stock = '000001.SZ'
    end_date = date.today()
    start_date = end_date - timedelta(days=365)  # 一年数据
    
    print(f"  获取 {test_stock} 一年数据进行验证...")
    
    try:
        data = dm.get_stock_data(test_stock, start_date, end_date)
        
        if data is not None and not data.empty:
            print(f"  📊 原始数据: {len(data)} 条记录")
            
            # 执行增强验证
            is_valid, errors, stats = validator.validate_stock_data(data, test_stock)
            
            print(f"  🔍 验证结果: {'通过' if is_valid else '失败'}")
            
            if errors:
                print(f"  ⚠️  发现问题:")
                for error in errors[:5]:  # 只显示前5个问题
                    print(f"    - {error}")
                if len(errors) > 5:
                    print(f"    ... 还有 {len(errors) - 5} 个问题")
            
            # 显示统计信息
            print(f"  📈 数据统计:")
            print(f"    总记录数: {stats['total_records']}")
            if stats['price_range']:
                print(f"    收盘价范围: {stats['price_range']['close']['min']:.2f} - {stats['price_range']['close']['max']:.2f}")
            if stats['volume_stats']:
                print(f"    成交量中位数: {stats['volume_stats']['median']:,.0f}")
            
            # 测试数据清洗
            print(f"  🧹 测试数据清洗...")
            cleaned_data, cleaning_stats = validator.clean_data_conservative(data, test_stock)
            
            print(f"    清洗结果: {cleaning_stats['original_rows']} -> {cleaning_stats['final_rows']} 行")
            if cleaning_stats['removed_duplicates'] > 0:
                print(f"    移除重复: {cleaning_stats['removed_duplicates']} 行")
            if cleaning_stats['filled_missing'] > 0:
                print(f"    填充缺失: {cleaning_stats['filled_missing']} 个值")
            if cleaning_stats['removed_invalid'] > 0:
                print(f"    移除无效: {cleaning_stats['removed_invalid']} 行")
                
        else:
            print(f"  ❌ 未获取到测试数据")
            
    except Exception as e:
        print(f"  ❌ 验证测试失败: {e}")
    
    dm.cleanup()
    print("✅ 增强数据验证测试完成")

def test_technical_indicators():
    """测试技术指标计算"""
    print("\n📈 测试技术指标计算...")
    
    indicators = TechnicalIndicators()
    dm = DataManager()
    
    # 获取测试数据
    test_stock = '000001.SZ'
    end_date = date.today()
    start_date = end_date - timedelta(days=365)
    
    print(f"  获取 {test_stock} 数据计算技术指标...")
    
    try:
        data = dm.get_stock_data(test_stock, start_date, end_date)
        
        if data is not None and not data.empty and len(data) > 50:
            print(f"  📊 数据记录: {len(data)} 条")
            
            # 计算所有技术指标
            data_with_indicators = indicators.calculate_all_indicators(data)
            
            # 验证指标计算
            print(f"  🔢 技术指标验证:")
            
            # 验证SMA
            if 'sma_20' in data_with_indicators.columns:
                sma_20 = data_with_indicators['sma_20'].dropna()
                manual_sma = data['close'].rolling(20).mean().dropna()
                if len(sma_20) > 0 and len(manual_sma) > 0:
                    diff = abs(sma_20.iloc[-1] - manual_sma.iloc[-1])
                    print(f"    SMA(20): {'✅' if diff < 0.01 else '❌'} (差异: {diff:.6f})")
            
            # 验证RSI
            if 'rsi' in data_with_indicators.columns:
                rsi = data_with_indicators['rsi'].dropna()
                if len(rsi) > 0:
                    rsi_value = rsi.iloc[-1]
                    is_valid = 0 <= rsi_value <= 100
                    print(f"    RSI: {'✅' if is_valid else '❌'} (值: {rsi_value:.2f})")
            
            # 验证MACD
            if all(col in data_with_indicators.columns for col in ['macd', 'macd_signal']):
                macd = data_with_indicators['macd'].dropna()
                signal = data_with_indicators['macd_signal'].dropna()
                if len(macd) > 0 and len(signal) > 0:
                    print(f"    MACD: ✅ (MACD: {macd.iloc[-1]:.4f}, Signal: {signal.iloc[-1]:.4f})")
            
            # 验证布林带
            if all(col in data_with_indicators.columns for col in ['bb_upper', 'bb_middle', 'bb_lower']):
                bb_upper = data_with_indicators['bb_upper'].dropna()
                bb_middle = data_with_indicators['bb_middle'].dropna()
                bb_lower = data_with_indicators['bb_lower'].dropna()
                
                if len(bb_upper) > 0 and len(bb_middle) > 0 and len(bb_lower) > 0:
                    # 验证布林带关系：上轨 > 中轨 > 下轨
                    last_upper = bb_upper.iloc[-1]
                    last_middle = bb_middle.iloc[-1]
                    last_lower = bb_lower.iloc[-1]
                    
                    is_valid = last_upper > last_middle > last_lower
                    print(f"    布林带: {'✅' if is_valid else '❌'} (上:{last_upper:.2f} 中:{last_middle:.2f} 下:{last_lower:.2f})")
            
            print(f"  📊 总指标数: {len(data_with_indicators.columns) - len(data.columns)} 个")
            
        else:
            print(f"  ❌ 数据不足，无法计算技术指标")
            
    except Exception as e:
        print(f"  ❌ 技术指标测试失败: {e}")
    
    dm.cleanup()
    print("✅ 技术指标计算测试完成")

def test_performance_metrics():
    """测试回测指标计算"""
    print("\n📊 测试回测指标计算...")
    
    # 创建模拟收益率序列进行测试
    np.random.seed(42)  # 固定随机种子以便重现
    returns = pd.Series(np.random.normal(0.001, 0.02, 252))  # 模拟一年的日收益率
    
    print(f"  📈 测试数据: {len(returns)} 个收益率")
    
    try:
        # 计算总收益率
        total_return = (1 + returns).prod() - 1
        print(f"    总收益率: {total_return:.2%}")
        
        # 计算年化收益率
        annual_return = (1 + total_return) ** (252 / len(returns)) - 1
        print(f"    年化收益率: {annual_return:.2%}")
        
        # 计算年化波动率
        volatility = returns.std() * np.sqrt(252)
        print(f"    年化波动率: {volatility:.2%}")
        
        # 计算夏普比率
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        print(f"    夏普比率: {sharpe_ratio:.2f}")
        
        # 计算最大回撤
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()
        print(f"    最大回撤: {max_drawdown:.2%}")
        
        # 计算胜率
        win_rate = (returns > 0).mean()
        print(f"    胜率: {win_rate:.2%}")
        
        # 验证计算合理性
        validations = [
            ("总收益率", -1 < total_return < 10),  # 合理范围
            ("年化波动率", 0 < volatility < 2),    # 合理范围
            ("胜率", 0 <= win_rate <= 1),          # 0-100%
            ("最大回撤", -1 <= max_drawdown <= 0), # 负值
        ]
        
        print(f"  🔍 合理性验证:")
        for name, is_valid in validations:
            print(f"    {name}: {'✅' if is_valid else '❌'}")
        
    except Exception as e:
        print(f"  ❌ 回测指标计算失败: {e}")
    
    print("✅ 回测指标计算测试完成")

def test_no_simulation_data():
    """测试确保无模拟数据"""
    print("\n🚫 测试确保无模拟数据...")
    
    # 检查代码中是否有模拟数据生成
    suspicious_patterns = [
        'np.random',
        'random.',
        'mock',
        'fake',
        'simulate',
        'dummy'
    ]
    
    code_files = [
        'data_acquisition/core/akshare_provider.py',
        'data_acquisition/core/data_manager.py',
        'data_acquisition/utils/data_validator.py',
        'data_acquisition/backtesting_interface/data_interface.py'
    ]
    
    found_issues = []
    
    for file_path in code_files:
        if Path(file_path).exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for pattern in suspicious_patterns:
                    if pattern in content.lower():
                        # 排除注释和文档字符串中的提及
                        lines = content.split('\n')
                        for i, line in enumerate(lines, 1):
                            if pattern in line.lower() and not line.strip().startswith('#') and not line.strip().startswith('"""'):
                                found_issues.append(f"{file_path}:{i} - {pattern}")
                                
            except Exception as e:
                print(f"  ⚠️  无法检查文件 {file_path}: {e}")
    
    if found_issues:
        print(f"  ❌ 发现可疑的模拟数据代码:")
        for issue in found_issues[:5]:  # 只显示前5个
            print(f"    {issue}")
    else:
        print(f"  ✅ 未发现模拟数据生成代码")
    
    print("✅ 模拟数据检查完成")

def main():
    """主测试函数"""
    print("🧪 A股数据采集框架 - 数据完整性测试")
    print("=" * 60)
    
    test_functions = [
        ("数据源真实性", test_data_source_authenticity),
        ("增强数据验证", test_enhanced_validation),
        ("技术指标计算", test_technical_indicators),
        ("回测指标计算", test_performance_metrics),
        ("模拟数据检查", test_no_simulation_data),
    ]
    
    passed_tests = 0
    total_tests = len(test_functions)
    
    for test_name, test_func in test_functions:
        try:
            print(f"\n🔄 运行测试: {test_name}")
            test_func()
            passed_tests += 1
            print(f"✅ {test_name} 测试通过")
        except Exception as e:
            print(f"❌ {test_name} 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有数据完整性测试通过！")
        print("✅ 数据源真实可靠")
        print("✅ 技术指标计算准确")
        print("✅ 无模拟或虚假数据")
        print("✅ 数据验证机制完善")
    else:
        print("⚠️  部分测试未通过，请检查相关问题")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

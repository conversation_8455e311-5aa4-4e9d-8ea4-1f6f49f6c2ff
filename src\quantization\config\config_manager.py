#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理系统

提供统一的配置管理、环境适配、配置验证和热更新功能。
支持多种配置格式：JSON、YAML、TOML、环境变量等。
"""

import os
import json
import warnings
from typing import Dict, Any, Optional, Union, List, Callable
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import threading
from collections import defaultdict
import time

from quantization.utils.logger import get_logger

# 可选依赖
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

try:
    import toml
    TOML_AVAILABLE = True
except ImportError:
    TOML_AVAILABLE = False


class ConfigFormat(Enum):
    """配置文件格式"""
    JSON = "json"
    YAML = "yaml"
    TOML = "toml"
    ENV = "env"


class ConfigScope(Enum):
    """配置作用域"""
    GLOBAL = "global"           # 全局配置
    ENVIRONMENT = "environment" # 环境配置
    USER = "user"              # 用户配置
    PROJECT = "project"        # 项目配置
    RUNTIME = "runtime"        # 运行时配置


@dataclass
class ConfigSchema:
    """配置模式定义"""
    key: str
    data_type: type
    default_value: Any = None
    required: bool = False
    description: str = ""
    validator: Optional[Callable[[Any], bool]] = None
    choices: Optional[List[Any]] = None
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None


@dataclass
class ConfigChangeEvent:
    """配置变更事件"""
    key: str
    old_value: Any
    new_value: Any
    scope: ConfigScope
    timestamp: float = field(default_factory=time.time)


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
    
    def validate_value(self, schema: ConfigSchema, value: Any) -> bool:
        """验证配置值"""
        try:
            # 类型检查
            if not isinstance(value, schema.data_type):
                # 尝试类型转换
                if schema.data_type in (int, float, str, bool):
                    try:
                        value = schema.data_type(value)
                    except (ValueError, TypeError):
                        self.logger.error(f"配置 {schema.key} 类型错误，期望 {schema.data_type.__name__}，实际 {type(value).__name__}")
                        return False
                else:
                    self.logger.error(f"配置 {schema.key} 类型错误，期望 {schema.data_type.__name__}，实际 {type(value).__name__}")
                    return False
            
            # 选择值检查
            if schema.choices and value not in schema.choices:
                self.logger.error(f"配置 {schema.key} 值 {value} 不在允许的选择中: {schema.choices}")
                return False
            
            # 数值范围检查
            if schema.data_type in (int, float):
                if schema.min_value is not None and value < schema.min_value:
                    self.logger.error(f"配置 {schema.key} 值 {value} 小于最小值 {schema.min_value}")
                    return False
                
                if schema.max_value is not None and value > schema.max_value:
                    self.logger.error(f"配置 {schema.key} 值 {value} 大于最大值 {schema.max_value}")
                    return False
            
            # 自定义验证器
            if schema.validator and not schema.validator(value):
                self.logger.error(f"配置 {schema.key} 值 {value} 未通过自定义验证")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证配置 {schema.key} 时出错: {e}")
            return False
    
    def validate_config(self, config: Dict[str, Any], 
                       schemas: Dict[str, ConfigSchema]) -> List[str]:
        """验证整个配置"""
        errors = []
        
        # 检查必需配置
        for key, schema in schemas.items():
            if schema.required and key not in config:
                errors.append(f"缺少必需配置: {key}")
        
        # 验证现有配置
        for key, value in config.items():
            if key in schemas:
                if not self.validate_value(schemas[key], value):
                    errors.append(f"配置 {key} 验证失败")
            else:
                # 未知配置项警告
                self.logger.warning(f"未知配置项: {key}")
        
        return errors


class ConfigManager:
    """
    配置管理器
    
    提供统一的配置管理、加载、保存、验证和监听功能。
    """
    
    def __init__(self, config_dir: str = "config"):
        self.logger = get_logger(self.__class__.__name__)
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置存储
        self.configs: Dict[ConfigScope, Dict[str, Any]] = {
            scope: {} for scope in ConfigScope
        }
        
        # 配置模式
        self.schemas: Dict[str, ConfigSchema] = {}
        
        # 配置验证器
        self.validator = ConfigValidator()
        
        # 变更监听器
        self.change_listeners: Dict[str, List[Callable[[ConfigChangeEvent], None]]] = defaultdict(list)
        
        # 线程安全
        self._lock = threading.RLock()
        
        # 自动保存
        self.auto_save = True
        
        # 加载默认配置
        self._load_default_schemas()
        self._load_all_configs()
    
    def _load_default_schemas(self):
        """加载默认配置模式"""
        default_schemas = [
            # 数据源配置
            ConfigSchema("data.akshare.enabled", bool, True, description="是否启用akshare数据源"),
            ConfigSchema("data.akshare.timeout", int, 30, description="akshare超时时间(秒)", min_value=1, max_value=300),
            ConfigSchema("data.cache.enabled", bool, True, description="是否启用数据缓存"),
            ConfigSchema("data.cache.ttl", int, 3600, description="缓存TTL(秒)", min_value=60),
            
            # 数据库配置
            ConfigSchema("database.host", str, "localhost", description="数据库主机"),
            ConfigSchema("database.port", int, 3306, description="数据库端口", min_value=1, max_value=65535),
            ConfigSchema("database.name", str, "quantization", description="数据库名称"),
            ConfigSchema("database.pool_size", int, 10, description="连接池大小", min_value=1, max_value=100),
            
            # 回测配置
            ConfigSchema("backtest.initial_capital", float, 1000000.0, description="初始资金", min_value=1000.0),
            ConfigSchema("backtest.commission_rate", float, 0.0003, description="手续费率", min_value=0.0, max_value=0.01),
            ConfigSchema("backtest.slippage_rate", float, 0.001, description="滑点率", min_value=0.0, max_value=0.01),
            ConfigSchema("backtest.max_positions", int, 100, description="最大持仓数", min_value=1, max_value=1000),
            
            # 策略配置
            ConfigSchema("strategy.rebalance_frequency", str, "daily", description="再平衡频率", 
                        choices=["daily", "weekly", "monthly"]),
            ConfigSchema("strategy.risk_limit", float, 0.02, description="单日风险限制", min_value=0.001, max_value=0.1),
            
            # 系统配置
            ConfigSchema("system.log_level", str, "INFO", description="日志级别", 
                        choices=["DEBUG", "INFO", "WARNING", "ERROR"]),
            ConfigSchema("system.max_workers", int, 8, description="最大工作线程数", min_value=1, max_value=32),
            ConfigSchema("system.memory_limit", int, 8192, description="内存限制(MB)", min_value=512),
            
            # API配置
            ConfigSchema("api.host", str, "0.0.0.0", description="API服务主机"),
            ConfigSchema("api.port", int, 8000, description="API服务端口", min_value=1024, max_value=65535),
            ConfigSchema("api.debug", bool, False, description="API调试模式"),
            ConfigSchema("api.rate_limit", int, 1000, description="API速率限制(请求/小时)", min_value=1),
        ]
        
        for schema in default_schemas:
            self.schemas[schema.key] = schema
    
    def _load_all_configs(self):
        """加载所有配置"""
        # 加载顺序：全局 -> 环境 -> 用户 -> 项目 -> 运行时
        load_order = [
            ConfigScope.GLOBAL,
            ConfigScope.ENVIRONMENT,
            ConfigScope.USER,
            ConfigScope.PROJECT,
            ConfigScope.RUNTIME
        ]
        
        for scope in load_order:
            try:
                self._load_config_by_scope(scope)
            except Exception as e:
                self.logger.warning(f"加载 {scope.value} 配置失败: {e}")
    
    def _load_config_by_scope(self, scope: ConfigScope):
        """按作用域加载配置"""
        config_files = {
            ConfigScope.GLOBAL: "global.json",
            ConfigScope.ENVIRONMENT: f"{os.getenv('ENVIRONMENT', 'development')}.json",
            ConfigScope.USER: f"user_{os.getenv('USER', 'default')}.json",
            ConfigScope.PROJECT: "project.json",
            ConfigScope.RUNTIME: "runtime.json"
        }
        
        config_file = self.config_dir / config_files[scope]
        
        if config_file.exists():
            config_data = self._load_config_file(config_file)
            if config_data:
                self.configs[scope] = config_data
                self.logger.info(f"加载 {scope.value} 配置: {len(config_data)} 项")
    
    def _load_config_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """加载配置文件"""
        try:
            suffix = file_path.suffix.lower()
            
            with open(file_path, 'r', encoding='utf-8') as f:
                if suffix == '.json':
                    return json.load(f)
                elif suffix in ['.yaml', '.yml'] and YAML_AVAILABLE:
                    return yaml.safe_load(f)
                elif suffix == '.toml' and TOML_AVAILABLE:
                    return toml.load(f)
                else:
                    self.logger.error(f"不支持的配置文件格式: {suffix}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"加载配置文件 {file_path} 失败: {e}")
            return None
    
    def get(self, key: str, default: Any = None, scope: Optional[ConfigScope] = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键
            default: 默认值
            scope: 指定作用域，None表示按优先级查找
            
        Returns:
            配置值
        """
        with self._lock:
            if scope:
                # 指定作用域
                return self._get_from_scope(key, scope, default)
            else:
                # 按优先级查找：运行时 -> 项目 -> 用户 -> 环境 -> 全局
                search_order = [
                    ConfigScope.RUNTIME,
                    ConfigScope.PROJECT,
                    ConfigScope.USER,
                    ConfigScope.ENVIRONMENT,
                    ConfigScope.GLOBAL
                ]
                
                for search_scope in search_order:
                    value = self._get_from_scope(key, search_scope, None)
                    if value is not None:
                        return value
                
                # 如果都没找到，返回模式默认值
                if key in self.schemas:
                    return self.schemas[key].default_value
                
                return default
    
    def _get_from_scope(self, key: str, scope: ConfigScope, default: Any) -> Any:
        """从指定作用域获取配置"""
        config = self.configs.get(scope, {})
        
        # 支持点分隔的嵌套键
        keys = key.split('.')
        current = config
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return default
        
        return current
    
    def set(self, key: str, value: Any, scope: ConfigScope = ConfigScope.RUNTIME, 
            validate: bool = True, notify: bool = True) -> bool:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            scope: 作用域
            validate: 是否验证
            notify: 是否通知监听器
            
        Returns:
            是否设置成功
        """
        with self._lock:
            # 验证配置
            if validate and key in self.schemas:
                if not self.validator.validate_value(self.schemas[key], value):
                    return False
            
            # 获取旧值
            old_value = self.get(key)
            
            # 设置新值
            self._set_nested_value(self.configs[scope], key, value)
            
            # 自动保存
            if self.auto_save and scope != ConfigScope.RUNTIME:
                self._save_config_by_scope(scope)
            
            # 通知监听器
            if notify and old_value != value:
                event = ConfigChangeEvent(key, old_value, value, scope)
                self._notify_listeners(key, event)
            
            self.logger.debug(f"设置配置 {key} = {value} (作用域: {scope.value})")
            return True
    
    def _set_nested_value(self, config: Dict[str, Any], key: str, value: Any):
        """设置嵌套配置值"""
        keys = key.split('.')
        current = config
        
        # 创建嵌套结构
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        # 设置最终值
        current[keys[-1]] = value
    
    def delete(self, key: str, scope: ConfigScope = ConfigScope.RUNTIME) -> bool:
        """删除配置"""
        with self._lock:
            try:
                config = self.configs.get(scope, {})
                keys = key.split('.')
                current = config
                
                # 找到父级
                for k in keys[:-1]:
                    if k in current:
                        current = current[k]
                    else:
                        return False
                
                # 删除最终键
                if keys[-1] in current:
                    old_value = current[keys[-1]]
                    del current[keys[-1]]
                    
                    # 通知监听器
                    event = ConfigChangeEvent(key, old_value, None, scope)
                    self._notify_listeners(key, event)
                    
                    self.logger.debug(f"删除配置 {key} (作用域: {scope.value})")
                    return True
                
                return False
                
            except Exception as e:
                self.logger.error(f"删除配置 {key} 失败: {e}")
                return False
    
    def add_schema(self, schema: ConfigSchema):
        """添加配置模式"""
        with self._lock:
            self.schemas[schema.key] = schema
            self.logger.debug(f"添加配置模式: {schema.key}")
    
    def validate_all(self) -> List[str]:
        """验证所有配置"""
        errors = []
        
        # 合并所有配置
        merged_config = {}
        for scope in [ConfigScope.GLOBAL, ConfigScope.ENVIRONMENT, 
                     ConfigScope.USER, ConfigScope.PROJECT, ConfigScope.RUNTIME]:
            merged_config.update(self.configs.get(scope, {}))
        
        # 验证
        errors = self.validator.validate_config(merged_config, self.schemas)
        
        if errors:
            self.logger.warning(f"配置验证发现 {len(errors)} 个错误")
            for error in errors:
                self.logger.warning(f"  {error}")
        
        return errors
    
    def add_change_listener(self, key: str, 
                          listener: Callable[[ConfigChangeEvent], None]):
        """添加配置变更监听器"""
        with self._lock:
            self.change_listeners[key].append(listener)
            self.logger.debug(f"添加配置 {key} 的变更监听器")
    
    def remove_change_listener(self, key: str, 
                             listener: Callable[[ConfigChangeEvent], None]):
        """移除配置变更监听器"""
        with self._lock:
            if key in self.change_listeners:
                try:
                    self.change_listeners[key].remove(listener)
                    self.logger.debug(f"移除配置 {key} 的变更监听器")
                except ValueError:
                    pass
    
    def _notify_listeners(self, key: str, event: ConfigChangeEvent):
        """通知监听器"""
        # 精确匹配
        if key in self.change_listeners:
            for listener in self.change_listeners[key]:
                try:
                    listener(event)
                except Exception as e:
                    self.logger.error(f"配置变更监听器执行失败: {e}")
        
        # 通配符匹配
        for pattern in self.change_listeners:
            if pattern.endswith('*') and key.startswith(pattern[:-1]):
                for listener in self.change_listeners[pattern]:
                    try:
                        listener(event)
                    except Exception as e:
                        self.logger.error(f"配置变更监听器执行失败: {e}")
    
    def _save_config_by_scope(self, scope: ConfigScope):
        """保存指定作用域的配置"""
        try:
            config_files = {
                ConfigScope.GLOBAL: "global.json",
                ConfigScope.ENVIRONMENT: f"{os.getenv('ENVIRONMENT', 'development')}.json",
                ConfigScope.USER: f"user_{os.getenv('USER', 'default')}.json",
                ConfigScope.PROJECT: "project.json",
                ConfigScope.RUNTIME: "runtime.json"
            }
            
            config_file = self.config_dir / config_files[scope]
            config_data = self.configs.get(scope, {})
            
            if config_data:
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, ensure_ascii=False, indent=2)
                
                self.logger.debug(f"保存 {scope.value} 配置到 {config_file}")
                
        except Exception as e:
            self.logger.error(f"保存 {scope.value} 配置失败: {e}")
    
    def save_all(self):
        """保存所有配置"""
        for scope in ConfigScope:
            if scope != ConfigScope.RUNTIME:  # 运行时配置不保存
                self._save_config_by_scope(scope)
    
    def reload(self):
        """重新加载所有配置"""
        with self._lock:
            self.logger.info("重新加载配置")
            self.configs = {scope: {} for scope in ConfigScope}
            self._load_all_configs()
    
    def get_all_configs(self) -> Dict[str, Any]:
        """获取所有合并后的配置"""
        merged = {}
        
        # 按优先级合并
        for scope in [ConfigScope.GLOBAL, ConfigScope.ENVIRONMENT,
                     ConfigScope.USER, ConfigScope.PROJECT, ConfigScope.RUNTIME]:
            merged.update(self.configs.get(scope, {}))
        
        return merged
    
    def export_config(self, file_path: str, scope: Optional[ConfigScope] = None):
        """导出配置到文件"""
        try:
            if scope:
                config_data = self.configs.get(scope, {})
            else:
                config_data = self.get_all_configs()
            
            file_path = Path(file_path)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                if file_path.suffix.lower() == '.json':
                    json.dump(config_data, f, ensure_ascii=False, indent=2)
                elif file_path.suffix.lower() in ['.yaml', '.yml'] and YAML_AVAILABLE:
                    yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
                else:
                    # 默认使用JSON
                    json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"导出配置到 {file_path}")
            
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")


# 全局配置管理器实例
config_manager = ConfigManager()

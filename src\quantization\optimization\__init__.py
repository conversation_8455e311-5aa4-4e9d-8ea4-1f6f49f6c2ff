#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
量化交易参数优化模块

提供策略参数优化、多策略组合、敏感性分析等功能。
支持多种优化算法：网格搜索、随机搜索、贝叶斯优化、遗传算法等。
"""

from .parameter_optimizer import (
    # 核心类
    ParameterOptimizationManager,
    GridSearchOptimizer,
    RandomSearchOptimizer,
    BayesianOptimizer,
    GeneticOptimizer,
    SensitivityAnalyzer,
    
    # 数据类
    ParameterSpace,
    OptimizationResult,
    
    # 枚举类
    OptimizationMethod,
    ParameterType
)

from .strategy_ensemble import (
    # 核心类
    StrategyEnsembleManager,
    
    # 数据类
    StrategyPerformance,
    EnsembleConfig,
    
    # 枚举类
    EnsembleMethod,
    RebalanceFrequency
)

__all__ = [
    # 参数优化
    'ParameterOptimizationManager',
    'GridSearchOptimizer',
    'RandomSearchOptimizer',
    'BayesianOptimizer',
    'GeneticOptimizer',
    'SensitivityAnalyzer',
    'ParameterSpace',
    'OptimizationResult',
    'OptimizationMethod',
    'ParameterType',
    
    # 策略组合
    'StrategyEnsembleManager',
    'StrategyPerformance',
    'EnsembleConfig',
    'EnsembleMethod',
    'RebalanceFrequency'
]

__version__ = "1.0.0"
__author__ = "Quantization Team"
__description__ = "量化交易参数优化和策略组合模块"

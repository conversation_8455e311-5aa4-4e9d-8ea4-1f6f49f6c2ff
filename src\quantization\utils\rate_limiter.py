#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
频率限制器模块

提供API请求频率控制功能。
"""

import time
import threading
from typing import Optional


class RateLimiter:
    """
    频率限制器
    
    用于控制API请求频率，避免触发限制。
    """
    
    def __init__(self, interval: float):
        """
        初始化频率限制器
        
        Args:
            interval: 请求间隔时间（秒）
        """
        self.interval = interval
        self.last_request_time = 0.0
        self.lock = threading.Lock()
    
    def wait(self) -> None:
        """
        等待直到可以发送下一个请求
        """
        with self.lock:
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            
            if time_since_last < self.interval:
                sleep_time = self.interval - time_since_last
                time.sleep(sleep_time)
            
            self.last_request_time = time.time()
    
    def can_request(self) -> bool:
        """
        检查是否可以发送请求
        
        Returns:
            是否可以发送请求
        """
        with self.lock:
            current_time = time.time()
            return (current_time - self.last_request_time) >= self.interval
    
    def reset(self) -> None:
        """重置限制器状态"""
        with self.lock:
            self.last_request_time = 0.0

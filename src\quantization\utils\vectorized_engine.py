#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级向量化计算引擎

基于NumPy和Numba的高性能向量化计算引擎，专门优化技术指标计算和选股算法。
提供比pandas更高效的向量化计算能力。
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Any
from numba import jit, njit, prange
import warnings
from dataclasses import dataclass
from enum import Enum

from quantization.utils.logger import get_logger


class IndicatorType(Enum):
    """技术指标类型"""
    WILLIAMS_R = "williams_r"
    RSI = "rsi"
    SMA = "sma"
    EMA = "ema"
    VOLUME_RATIO = "volume_ratio"
    MACD = "macd"
    BOLLINGER_BANDS = "bollinger_bands"
    STOCHASTIC = "stochastic"


@dataclass
class IndicatorConfig:
    """指标配置"""
    indicator_type: IndicatorType
    period: int
    params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.params is None:
            self.params = {}


@njit(cache=True)
def _williams_r_numba(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
    """
    Numba优化的威廉指标计算
    
    Args:
        high: 最高价数组
        low: 最低价数组
        close: 收盘价数组
        period: 计算周期
        
    Returns:
        威廉指标数组
    """
    n = len(close)
    result = np.full(n, np.nan)
    
    for i in prange(period - 1, n):
        start_idx = i - period + 1
        highest_high = np.max(high[start_idx:i + 1])
        lowest_low = np.min(low[start_idx:i + 1])
        
        if highest_high != lowest_low:
            result[i] = ((highest_high - close[i]) / (highest_high - lowest_low)) * -100
        else:
            result[i] = -50.0  # 默认值
    
    return result


@njit(cache=True)
def _rsi_numba(close: np.ndarray, period: int = 14) -> np.ndarray:
    """
    Numba优化的RSI计算
    
    Args:
        close: 收盘价数组
        period: 计算周期
        
    Returns:
        RSI数组
    """
    n = len(close)
    result = np.full(n, np.nan)
    
    if n < period + 1:
        return result
    
    # 计算价格变化
    deltas = np.diff(close)
    
    # 初始化增益和损失
    gains = np.where(deltas > 0, deltas, 0.0)
    losses = np.where(deltas < 0, -deltas, 0.0)
    
    # 计算初始平均增益和损失
    avg_gain = np.mean(gains[:period])
    avg_loss = np.mean(losses[:period])
    
    if avg_loss == 0:
        result[period] = 100.0
    else:
        rs = avg_gain / avg_loss
        result[period] = 100.0 - (100.0 / (1.0 + rs))
    
    # 使用指数移动平均计算后续值
    alpha = 1.0 / period
    for i in range(period + 1, n):
        gain = gains[i - 1]
        loss = losses[i - 1]
        
        avg_gain = alpha * gain + (1 - alpha) * avg_gain
        avg_loss = alpha * loss + (1 - alpha) * avg_loss
        
        if avg_loss == 0:
            result[i] = 100.0
        else:
            rs = avg_gain / avg_loss
            result[i] = 100.0 - (100.0 / (1.0 + rs))
    
    return result


@njit(cache=True)
def _sma_numba(values: np.ndarray, period: int) -> np.ndarray:
    """
    Numba优化的简单移动平均计算
    
    Args:
        values: 数值数组
        period: 计算周期
        
    Returns:
        SMA数组
    """
    n = len(values)
    result = np.full(n, np.nan)
    
    for i in prange(period - 1, n):
        start_idx = i - period + 1
        result[i] = np.mean(values[start_idx:i + 1])
    
    return result


@njit(cache=True)
def _ema_numba(values: np.ndarray, period: int) -> np.ndarray:
    """
    Numba优化的指数移动平均计算
    
    Args:
        values: 数值数组
        period: 计算周期
        
    Returns:
        EMA数组
    """
    n = len(values)
    result = np.full(n, np.nan)
    
    if n == 0:
        return result
    
    alpha = 2.0 / (period + 1)
    
    # 第一个有效值使用SMA
    if n >= period:
        result[period - 1] = np.mean(values[:period])
        
        # 后续值使用EMA公式
        for i in range(period, n):
            result[i] = alpha * values[i] + (1 - alpha) * result[i - 1]
    
    return result


@njit(cache=True)
def _volume_ratio_numba(volume: np.ndarray, period: int = 20) -> np.ndarray:
    """
    Numba优化的量比计算
    
    Args:
        volume: 成交量数组
        period: 计算周期
        
    Returns:
        量比数组
    """
    n = len(volume)
    result = np.full(n, np.nan)
    
    for i in prange(period - 1, n):
        start_idx = i - period + 1
        avg_volume = np.mean(volume[start_idx:i + 1])
        
        if avg_volume > 0:
            result[i] = volume[i] / avg_volume
        else:
            result[i] = 1.0
    
    return result


@njit(cache=True)
def _bollinger_bands_numba(close: np.ndarray, period: int = 20, std_dev: float = 2.0) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Numba优化的布林带计算
    
    Args:
        close: 收盘价数组
        period: 计算周期
        std_dev: 标准差倍数
        
    Returns:
        (上轨, 中轨, 下轨)
    """
    n = len(close)
    upper = np.full(n, np.nan)
    middle = np.full(n, np.nan)
    lower = np.full(n, np.nan)
    
    for i in prange(period - 1, n):
        start_idx = i - period + 1
        window_data = close[start_idx:i + 1]
        
        mean_val = np.mean(window_data)
        std_val = np.std(window_data)
        
        middle[i] = mean_val
        upper[i] = mean_val + std_dev * std_val
        lower[i] = mean_val - std_dev * std_val
    
    return upper, middle, lower


class VectorizedEngine:
    """
    高级向量化计算引擎
    
    提供高性能的技术指标计算和选股算法，基于NumPy和Numba优化。
    """
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self._indicator_cache: Dict[str, np.ndarray] = {}
        self._cache_enabled = True
        
        # 预编译Numba函数
        self._warmup_numba_functions()
    
    def _warmup_numba_functions(self):
        """预热Numba函数，触发JIT编译"""
        try:
            # 创建测试数据
            test_data = np.random.random(100)
            test_high = test_data + 0.1
            test_low = test_data - 0.1
            
            # 预编译所有Numba函数
            _williams_r_numba(test_high, test_low, test_data, 14)
            _rsi_numba(test_data, 14)
            _sma_numba(test_data, 20)
            _ema_numba(test_data, 20)
            _volume_ratio_numba(test_data, 20)
            _bollinger_bands_numba(test_data, 20, 2.0)
            
            self.logger.info("Numba函数预编译完成")
            
        except Exception as e:
            self.logger.warning(f"Numba函数预编译失败: {e}")
    
    def enable_cache(self, enabled: bool = True):
        """启用或禁用缓存"""
        self._cache_enabled = enabled
        if not enabled:
            self._indicator_cache.clear()
    
    def clear_cache(self):
        """清空缓存"""
        self._indicator_cache.clear()
    
    def _get_cache_key(self, indicator_type: str, data_hash: str, **params) -> str:
        """生成缓存键"""
        param_str = "_".join(f"{k}={v}" for k, v in sorted(params.items()))
        return f"{indicator_type}_{data_hash}_{param_str}"
    
    def _get_data_hash(self, data: np.ndarray) -> str:
        """计算数据哈希值"""
        return str(hash(data.tobytes()))

    def calculate_williams_r(self, high: np.ndarray, low: np.ndarray, close: np.ndarray,
                           period: int = 14, use_cache: bool = True) -> np.ndarray:
        """
        计算威廉指标

        Args:
            high: 最高价数组
            low: 最低价数组
            close: 收盘价数组
            period: 计算周期
            use_cache: 是否使用缓存

        Returns:
            威廉指标数组
        """
        if use_cache and self._cache_enabled:
            data_hash = self._get_data_hash(np.concatenate([high, low, close]))
            cache_key = self._get_cache_key("williams_r", data_hash, period=period)

            if cache_key in self._indicator_cache:
                return self._indicator_cache[cache_key]

        try:
            result = _williams_r_numba(high, low, close, period)

            if use_cache and self._cache_enabled:
                self._indicator_cache[cache_key] = result

            return result

        except Exception as e:
            self.logger.error(f"威廉指标计算失败: {e}")
            return np.full(len(close), np.nan)

    def calculate_rsi(self, close: np.ndarray, period: int = 14, use_cache: bool = True) -> np.ndarray:
        """
        计算RSI指标

        Args:
            close: 收盘价数组
            period: 计算周期
            use_cache: 是否使用缓存

        Returns:
            RSI数组
        """
        if use_cache and self._cache_enabled:
            data_hash = self._get_data_hash(close)
            cache_key = self._get_cache_key("rsi", data_hash, period=period)

            if cache_key in self._indicator_cache:
                return self._indicator_cache[cache_key]

        try:
            result = _rsi_numba(close, period)

            if use_cache and self._cache_enabled:
                self._indicator_cache[cache_key] = result

            return result

        except Exception as e:
            self.logger.error(f"RSI计算失败: {e}")
            return np.full(len(close), np.nan)

    def calculate_sma(self, values: np.ndarray, period: int, use_cache: bool = True) -> np.ndarray:
        """
        计算简单移动平均

        Args:
            values: 数值数组
            period: 计算周期
            use_cache: 是否使用缓存

        Returns:
            SMA数组
        """
        if use_cache and self._cache_enabled:
            data_hash = self._get_data_hash(values)
            cache_key = self._get_cache_key("sma", data_hash, period=period)

            if cache_key in self._indicator_cache:
                return self._indicator_cache[cache_key]

        try:
            result = _sma_numba(values, period)

            if use_cache and self._cache_enabled:
                self._indicator_cache[cache_key] = result

            return result

        except Exception as e:
            self.logger.error(f"SMA计算失败: {e}")
            return np.full(len(values), np.nan)

    def calculate_ema(self, values: np.ndarray, period: int, use_cache: bool = True) -> np.ndarray:
        """
        计算指数移动平均

        Args:
            values: 数值数组
            period: 计算周期
            use_cache: 是否使用缓存

        Returns:
            EMA数组
        """
        if use_cache and self._cache_enabled:
            data_hash = self._get_data_hash(values)
            cache_key = self._get_cache_key("ema", data_hash, period=period)

            if cache_key in self._indicator_cache:
                return self._indicator_cache[cache_key]

        try:
            result = _ema_numba(values, period)

            if use_cache and self._cache_enabled:
                self._indicator_cache[cache_key] = result

            return result

        except Exception as e:
            self.logger.error(f"EMA计算失败: {e}")
            return np.full(len(values), np.nan)

    def calculate_volume_ratio(self, volume: np.ndarray, period: int = 20, use_cache: bool = True) -> np.ndarray:
        """
        计算量比

        Args:
            volume: 成交量数组
            period: 计算周期
            use_cache: 是否使用缓存

        Returns:
            量比数组
        """
        if use_cache and self._cache_enabled:
            data_hash = self._get_data_hash(volume)
            cache_key = self._get_cache_key("volume_ratio", data_hash, period=period)

            if cache_key in self._indicator_cache:
                return self._indicator_cache[cache_key]

        try:
            result = _volume_ratio_numba(volume, period)

            if use_cache and self._cache_enabled:
                self._indicator_cache[cache_key] = result

            return result

        except Exception as e:
            self.logger.error(f"量比计算失败: {e}")
            return np.full(len(volume), np.nan)

    def calculate_bollinger_bands(self, close: np.ndarray, period: int = 20, std_dev: float = 2.0,
                                use_cache: bool = True) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        计算布林带

        Args:
            close: 收盘价数组
            period: 计算周期
            std_dev: 标准差倍数
            use_cache: 是否使用缓存

        Returns:
            (上轨, 中轨, 下轨)
        """
        if use_cache and self._cache_enabled:
            data_hash = self._get_data_hash(close)
            cache_key = self._get_cache_key("bollinger", data_hash, period=period, std_dev=std_dev)

            if cache_key in self._indicator_cache:
                return self._indicator_cache[cache_key]

        try:
            result = _bollinger_bands_numba(close, period, std_dev)

            if use_cache and self._cache_enabled:
                self._indicator_cache[cache_key] = result

            return result

        except Exception as e:
            self.logger.error(f"布林带计算失败: {e}")
            n = len(close)
            return np.full(n, np.nan), np.full(n, np.nan), np.full(n, np.nan)

    def calculate_multiple_indicators(self, data: pd.DataFrame,
                                    indicators: List[IndicatorConfig]) -> pd.DataFrame:
        """
        批量计算多个技术指标

        Args:
            data: 包含OHLCV数据的DataFrame
            indicators: 指标配置列表

        Returns:
            包含所有指标的DataFrame
        """
        result = data.copy()

        # 转换为numpy数组以提高性能
        high = np.asarray(data['high'].values, dtype=np.float64)
        low = np.asarray(data['low'].values, dtype=np.float64)
        close = np.asarray(data['close'].values, dtype=np.float64)
        volume = np.asarray(data['volume'].values, dtype=np.float64)

        for config in indicators:
            try:
                if config.indicator_type == IndicatorType.WILLIAMS_R:
                    period = config.params.get('period', 14)
                    result[f'williams_r_{period}'] = self.calculate_williams_r(high, low, close, period)

                elif config.indicator_type == IndicatorType.RSI:
                    period = config.params.get('period', 14)
                    result[f'rsi_{period}'] = self.calculate_rsi(close, period)

                elif config.indicator_type == IndicatorType.SMA:
                    period = config.params.get('period', 20)
                    result[f'sma_{period}'] = self.calculate_sma(close, period)

                elif config.indicator_type == IndicatorType.EMA:
                    period = config.params.get('period', 20)
                    result[f'ema_{period}'] = self.calculate_ema(close, period)

                elif config.indicator_type == IndicatorType.VOLUME_RATIO:
                    period = config.params.get('period', 20)
                    result[f'volume_ratio_{period}'] = self.calculate_volume_ratio(volume, period)

                elif config.indicator_type == IndicatorType.BOLLINGER_BANDS:
                    period = config.params.get('period', 20)
                    std_dev = config.params.get('std_dev', 2.0)
                    upper, middle, lower = self.calculate_bollinger_bands(close, period, std_dev)
                    result[f'bb_upper_{period}'] = upper
                    result[f'bb_middle_{period}'] = middle
                    result[f'bb_lower_{period}'] = lower

            except Exception as e:
                self.logger.error(f"计算指标 {config.indicator_type.value} 失败: {e}")

        return result

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'cache_enabled': self._cache_enabled,
            'cache_size': len(self._indicator_cache),
            'cache_keys': list(self._indicator_cache.keys())
        }


@njit(cache=True)
def _vectorized_stock_screening(market_caps: np.ndarray, williams_r: np.ndarray,
                              volume_ratios: np.ndarray, big_order_ratios: np.ndarray,
                              market_cap_min: float, market_cap_max: float,
                              wr_threshold: float, volume_ratio_threshold: float,
                              big_order_threshold: float) -> np.ndarray:
    """
    Numba优化的向量化选股算法

    Args:
        market_caps: 市值数组
        williams_r: 威廉指标数组
        volume_ratios: 量比数组
        big_order_ratios: 大单净量比例数组
        market_cap_min: 最小市值
        market_cap_max: 最大市值
        wr_threshold: 威廉指标阈值
        volume_ratio_threshold: 量比阈值
        big_order_threshold: 大单净量阈值

    Returns:
        符合条件的股票索引数组
    """
    n = len(market_caps)
    selected_indices = []

    for i in prange(n):
        # 检查所有条件
        if (market_cap_min <= market_caps[i] <= market_cap_max and
            williams_r[i] > wr_threshold and
            volume_ratios[i] > volume_ratio_threshold and
            big_order_ratios[i] > big_order_threshold):
            selected_indices.append(i)

    return np.array(selected_indices)


class VectorizedStockScreener:
    """
    向量化选股器

    使用高性能向量化算法进行股票筛选。
    """

    def __init__(self, engine: VectorizedEngine = None):
        self.logger = get_logger(self.__class__.__name__)
        self.engine = engine or VectorizedEngine()

    def screen_stocks(self, data: pd.DataFrame, criteria: Dict[str, Any]) -> List[str]:
        """
        向量化选股

        Args:
            data: 股票数据DataFrame，包含stock_code, market_cap等列
            criteria: 选股条件字典

        Returns:
            符合条件的股票代码列表
        """
        try:
            if data.empty:
                return []

            # 提取数组数据
            market_caps = data['market_cap'].values
            williams_r = data['williams_r'].values if 'williams_r' in data.columns else np.full(len(data), -50.0)
            volume_ratios = data['volume_ratio'].values if 'volume_ratio' in data.columns else np.full(len(data), 1.0)
            big_order_ratios = data['big_order_ratio'].values if 'big_order_ratio' in data.columns else np.full(len(data), 0.3)

            # 执行向量化筛选
            selected_indices = _vectorized_stock_screening(
                market_caps, williams_r, volume_ratios, big_order_ratios,
                criteria.get('market_cap_min', 15e8),
                criteria.get('market_cap_max', 300e8),
                criteria.get('wr_threshold', -20),
                criteria.get('volume_ratio_threshold', 2.0),
                criteria.get('big_order_threshold', 0.4)
            )

            # 返回选中的股票代码
            if len(selected_indices) > 0:
                selected_stocks = data.iloc[selected_indices]['stock_code'].tolist()
                return selected_stocks
            else:
                return []

        except Exception as e:
            self.logger.error(f"向量化选股失败: {e}")
            return []


# 全局实例
vectorized_engine = VectorizedEngine()
vectorized_screener = VectorizedStockScreener(vectorized_engine)

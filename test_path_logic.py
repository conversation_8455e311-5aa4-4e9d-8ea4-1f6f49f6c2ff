#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试路径处理逻辑
"""

from pathlib import Path

def test_path_logic():
    """测试路径分离逻辑"""
    
    test_cases = [
        "output/visualization/equity_curve",
        "output/visualization/returns_distribution",
        "charts/test_chart",
        "/absolute/path/chart_name"
    ]
    
    for save_path in test_cases:
        print(f"原始路径: {save_path}")
        
        save_path_obj = Path(save_path)
        output_dir = str(save_path_obj.parent)
        filename = save_path_obj.name
        
        print(f"  父目录: {output_dir}")
        print(f"  文件名: {filename}")
        
        # 模拟save_figure方法的逻辑
        final_path = Path(output_dir) / f"{filename}.png"
        print(f"  最终路径: {final_path}")
        print()

if __name__ == "__main__":
    test_path_logic()

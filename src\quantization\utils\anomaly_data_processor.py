#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异常数据处理引擎

实现智能异常数据识别、自动修复和数据插值算法。
提供多种数据修复策略和自动化数据清洗功能。
"""

import asyncio
import time
import warnings
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
import pandas as pd
from collections import defaultdict, deque
import threading
import json
from pathlib import Path

from quantization.utils.logger import get_logger

# 可选依赖
try:
    from scipy import interpolate, signal
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False

try:
    from sklearn.impute import KNNImputer
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


class AnomalyType(Enum):
    """异常类型"""
    MISSING_VALUE = "missing_value"
    OUTLIER = "outlier"
    DUPLICATE = "duplicate"
    INCONSISTENT = "inconsistent"
    INVALID_FORMAT = "invalid_format"
    TEMPORAL_ANOMALY = "temporal_anomaly"


class RepairStrategy(Enum):
    """修复策略"""
    DELETE = "delete"                    # 删除异常记录
    INTERPOLATE = "interpolate"          # 插值填充
    REPLACE_MEAN = "replace_mean"        # 均值替换
    REPLACE_MEDIAN = "replace_median"    # 中位数替换
    REPLACE_MODE = "replace_mode"        # 众数替换
    FORWARD_FILL = "forward_fill"        # 前向填充
    BACKWARD_FILL = "backward_fill"      # 后向填充
    KNN_IMPUTE = "knn_impute"           # KNN插值
    CUSTOM = "custom"                    # 自定义修复


@dataclass
class AnomalyRecord:
    """异常记录"""
    row_index: int
    column_name: str
    anomaly_type: AnomalyType
    original_value: Any
    confidence_score: float
    detection_method: str
    suggested_strategy: RepairStrategy
    repair_value: Any = None
    is_repaired: bool = False
    repair_timestamp: Optional[float] = None


@dataclass
class RepairResult:
    """修复结果"""
    total_anomalies: int
    repaired_count: int
    deleted_count: int
    failed_count: int
    repair_details: List[AnomalyRecord]
    processing_time: float
    quality_improvement: float = 0.0


class MissingValueHandler:
    """
    缺失值处理器
    
    提供多种缺失值填充策略。
    """
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
    
    def handle_missing_values(self, df: pd.DataFrame, 
                            strategy: RepairStrategy = RepairStrategy.INTERPOLATE,
                            columns: Optional[List[str]] = None) -> pd.DataFrame:
        """
        处理缺失值
        
        Args:
            df: 数据框
            strategy: 填充策略
            columns: 要处理的列名列表
            
        Returns:
            处理后的数据框
        """
        if columns is None:
            columns = df.columns.tolist()
        
        df_repaired = df.copy()
        
        for column in columns:
            if column not in df.columns:
                continue
            
            missing_mask = df[column].isnull()
            if not missing_mask.any():
                continue
            
            try:
                if strategy == RepairStrategy.INTERPOLATE:
                    df_repaired[column] = self._interpolate_missing(df[column])
                elif strategy == RepairStrategy.REPLACE_MEAN:
                    df_repaired[column] = df[column].fillna(df[column].mean())
                elif strategy == RepairStrategy.REPLACE_MEDIAN:
                    df_repaired[column] = df[column].fillna(df[column].median())
                elif strategy == RepairStrategy.REPLACE_MODE:
                    mode_value = df[column].mode()
                    if len(mode_value) > 0:
                        df_repaired[column] = df[column].fillna(mode_value[0])
                elif strategy == RepairStrategy.FORWARD_FILL:
                    df_repaired[column] = df[column].fillna(method='ffill')
                elif strategy == RepairStrategy.BACKWARD_FILL:
                    df_repaired[column] = df[column].fillna(method='bfill')
                elif strategy == RepairStrategy.KNN_IMPUTE and SKLEARN_AVAILABLE:
                    df_repaired = self._knn_impute_missing(df_repaired, [column])
                
            except Exception as e:
                self.logger.warning(f"处理列 {column} 的缺失值时出错: {e}")
        
        return df_repaired
    
    def _interpolate_missing(self, series: pd.Series) -> pd.Series:
        """插值填充缺失值"""
        if series.dtype in ['object', 'category']:
            # 对于分类数据，使用前向填充
            return series.fillna(method='ffill').fillna(method='bfill')
        
        # 对于数值数据，使用线性插值
        return series.interpolate(method='linear').fillna(method='ffill').fillna(method='bfill')
    
    def _knn_impute_missing(self, df: pd.DataFrame, columns: List[str]) -> pd.DataFrame:
        """使用KNN插值填充缺失值"""
        if not SKLEARN_AVAILABLE:
            return df
        
        try:
            # 选择数值列进行KNN插值
            numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
            target_columns = [col for col in columns if col in numeric_columns]
            
            if not target_columns:
                return df
            
            # 创建KNN插值器
            imputer = KNNImputer(n_neighbors=5)
            
            # 对数值列进行插值
            df_numeric = df[numeric_columns].copy()
            df_imputed = pd.DataFrame(
                imputer.fit_transform(df_numeric),
                columns=numeric_columns,
                index=df.index
            )
            
            # 更新目标列
            for col in target_columns:
                df[col] = df_imputed[col]
            
            return df
            
        except Exception as e:
            self.logger.warning(f"KNN插值失败: {e}")
            return df


class OutlierProcessor:
    """
    异常值处理器
    
    检测和处理数据中的异常值。
    """
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
    
    def process_outliers(self, df: pd.DataFrame,
                        columns: Optional[List[str]] = None,
                        method: str = 'iqr',
                        strategy: RepairStrategy = RepairStrategy.REPLACE_MEDIAN) -> pd.DataFrame:
        """
        处理异常值
        
        Args:
            df: 数据框
            columns: 要处理的列名列表
            method: 异常值检测方法
            strategy: 处理策略
            
        Returns:
            处理后的数据框
        """
        if columns is None:
            columns = df.select_dtypes(include=[np.number]).columns.tolist()
        
        df_processed = df.copy()
        
        for column in columns:
            if column not in df.columns:
                continue
            
            try:
                # 检测异常值
                outlier_mask = self._detect_outliers(df[column], method)
                
                if not outlier_mask.any():
                    continue
                
                # 处理异常值
                if strategy == RepairStrategy.DELETE:
                    df_processed = df_processed[~outlier_mask]
                elif strategy == RepairStrategy.REPLACE_MEAN:
                    mean_value = df[column][~outlier_mask].mean()
                    df_processed.loc[outlier_mask, column] = mean_value
                elif strategy == RepairStrategy.REPLACE_MEDIAN:
                    median_value = df[column][~outlier_mask].median()
                    df_processed.loc[outlier_mask, column] = median_value
                elif strategy == RepairStrategy.INTERPOLATE:
                    # 将异常值设为NaN，然后插值
                    df_processed.loc[outlier_mask, column] = np.nan
                    df_processed[column] = df_processed[column].interpolate()
                
            except Exception as e:
                self.logger.warning(f"处理列 {column} 的异常值时出错: {e}")
        
        return df_processed
    
    def _detect_outliers(self, series: pd.Series, method: str = 'iqr') -> pd.Series:
        """检测异常值"""
        if method == 'iqr':
            Q1 = series.quantile(0.25)
            Q3 = series.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            return (series < lower_bound) | (series > upper_bound)
        
        elif method == 'zscore':
            z_scores = np.abs((series - series.mean()) / series.std())
            return z_scores > 3
        
        elif method == 'modified_zscore':
            median = series.median()
            mad = np.median(np.abs(series - median))
            modified_z_scores = 0.6745 * (series - median) / mad
            return np.abs(modified_z_scores) > 3.5
        
        else:
            return pd.Series([False] * len(series), index=series.index)


class DuplicateHandler:
    """
    重复数据处理器
    
    检测和处理重复记录。
    """
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
    
    def handle_duplicates(self, df: pd.DataFrame,
                         subset: Optional[List[str]] = None,
                         keep: str = 'first') -> pd.DataFrame:
        """
        处理重复记录
        
        Args:
            df: 数据框
            subset: 用于判断重复的列名列表
            keep: 保留策略 ('first', 'last', False)
            
        Returns:
            处理后的数据框
        """
        try:
            # 检测重复记录
            duplicate_mask = df.duplicated(subset=subset, keep=False)
            duplicate_count = duplicate_mask.sum()
            
            if duplicate_count > 0:
                self.logger.info(f"发现 {duplicate_count} 个重复记录")
                
                # 删除重复记录
                df_cleaned = df.drop_duplicates(subset=subset, keep=keep)
                
                removed_count = len(df) - len(df_cleaned)
                self.logger.info(f"删除了 {removed_count} 个重复记录")
                
                return df_cleaned
            
            return df
            
        except Exception as e:
            self.logger.error(f"处理重复记录时出错: {e}")
            return df


class TemporalAnomalyProcessor:
    """
    时间序列异常处理器
    
    处理时间序列数据中的异常。
    """
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
    
    def process_temporal_anomalies(self, df: pd.DataFrame,
                                 time_column: str,
                                 value_columns: List[str],
                                 window_size: int = 10) -> pd.DataFrame:
        """
        处理时间序列异常
        
        Args:
            df: 数据框
            time_column: 时间列名
            value_columns: 数值列名列表
            window_size: 滑动窗口大小
            
        Returns:
            处理后的数据框
        """
        if time_column not in df.columns:
            return df
        
        df_processed = df.copy()
        
        try:
            # 确保时间列是datetime类型
            df_processed[time_column] = pd.to_datetime(df_processed[time_column])
            
            # 按时间排序
            df_processed = df_processed.sort_values(time_column)
            
            for column in value_columns:
                if column not in df.columns:
                    continue
                
                # 使用滑动窗口检测异常
                rolling_mean = df_processed[column].rolling(window=window_size, center=True).mean()
                rolling_std = df_processed[column].rolling(window=window_size, center=True).std()
                
                # 计算异常阈值
                upper_bound = rolling_mean + 2 * rolling_std
                lower_bound = rolling_mean - 2 * rolling_std
                
                # 检测异常值
                anomaly_mask = (df_processed[column] > upper_bound) | (df_processed[column] < lower_bound)
                
                # 使用插值修复异常值
                if anomaly_mask.any():
                    df_processed.loc[anomaly_mask, column] = np.nan
                    df_processed[column] = df_processed[column].interpolate(method='time')
                    
                    anomaly_count = anomaly_mask.sum()
                    self.logger.info(f"修复了列 {column} 中的 {anomaly_count} 个时间序列异常")
            
        except Exception as e:
            self.logger.error(f"处理时间序列异常时出错: {e}")
        
        return df_processed


class AnomalyDataProcessor:
    """
    异常数据处理引擎
    
    整合各种异常数据处理器，提供全面的数据清洗和修复功能。
    """
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.missing_handler = MissingValueHandler()
        self.outlier_processor = OutlierProcessor()
        self.duplicate_handler = DuplicateHandler()
        self.temporal_processor = TemporalAnomalyProcessor()
        
        # 处理历史记录
        self.processing_history: List[RepairResult] = []
        self._lock = threading.RLock()
    
    def process_anomalies(self, df: pd.DataFrame,
                         processing_config: Optional[Dict[str, Any]] = None) -> Tuple[pd.DataFrame, RepairResult]:
        """
        全面处理数据异常
        
        Args:
            df: 数据框
            processing_config: 处理配置
            
        Returns:
            处理后的数据框和修复结果
        """
        start_time = time.time()
        
        if processing_config is None:
            processing_config = self._get_default_config()
        
        df_processed = df.copy()
        anomaly_records = []
        
        try:
            # 1. 处理重复记录
            if processing_config.get('handle_duplicates', True):
                original_count = len(df_processed)
                df_processed = self.duplicate_handler.handle_duplicates(
                    df_processed,
                    subset=processing_config.get('duplicate_subset'),
                    keep=processing_config.get('duplicate_keep', 'first')
                )
                deleted_count = original_count - len(df_processed)
                
                if deleted_count > 0:
                    for i in range(deleted_count):
                        anomaly_records.append(AnomalyRecord(
                            row_index=-1,
                            column_name='全行',
                            anomaly_type=AnomalyType.DUPLICATE,
                            original_value=None,
                            confidence_score=1.0,
                            detection_method='exact_match',
                            suggested_strategy=RepairStrategy.DELETE,
                            is_repaired=True,
                            repair_timestamp=time.time()
                        ))
            
            # 2. 处理缺失值
            if processing_config.get('handle_missing', True):
                missing_columns = processing_config.get('missing_columns')
                missing_strategy = RepairStrategy(processing_config.get('missing_strategy', 'interpolate'))
                
                df_processed = self.missing_handler.handle_missing_values(
                    df_processed,
                    strategy=missing_strategy,
                    columns=missing_columns
                )
            
            # 3. 处理异常值
            if processing_config.get('handle_outliers', True):
                outlier_columns = processing_config.get('outlier_columns')
                outlier_method = processing_config.get('outlier_method', 'iqr')
                outlier_strategy = RepairStrategy(processing_config.get('outlier_strategy', 'replace_median'))
                
                df_processed = self.outlier_processor.process_outliers(
                    df_processed,
                    columns=outlier_columns,
                    method=outlier_method,
                    strategy=outlier_strategy
                )
            
            # 4. 处理时间序列异常
            if processing_config.get('handle_temporal', False):
                time_column = processing_config.get('time_column')
                value_columns = processing_config.get('temporal_value_columns', [])
                
                if time_column and value_columns:
                    df_processed = self.temporal_processor.process_temporal_anomalies(
                        df_processed,
                        time_column=time_column,
                        value_columns=value_columns,
                        window_size=processing_config.get('temporal_window_size', 10)
                    )
            
            # 计算处理结果
            processing_time = time.time() - start_time
            
            repair_result = RepairResult(
                total_anomalies=len(anomaly_records),
                repaired_count=sum(1 for r in anomaly_records if r.is_repaired and r.suggested_strategy != RepairStrategy.DELETE),
                deleted_count=sum(1 for r in anomaly_records if r.suggested_strategy == RepairStrategy.DELETE),
                failed_count=sum(1 for r in anomaly_records if not r.is_repaired),
                repair_details=anomaly_records,
                processing_time=processing_time
            )
            
            # 记录处理历史
            with self._lock:
                self.processing_history.append(repair_result)
            
            self.logger.info(f"异常数据处理完成，耗时 {processing_time:.2f}s，"
                           f"修复 {repair_result.repaired_count} 个异常，"
                           f"删除 {repair_result.deleted_count} 个记录")
            
            return df_processed, repair_result
            
        except Exception as e:
            self.logger.error(f"异常数据处理失败: {e}")
            raise
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认处理配置"""
        return {
            'handle_duplicates': True,
            'duplicate_keep': 'first',
            'handle_missing': True,
            'missing_strategy': 'interpolate',
            'handle_outliers': True,
            'outlier_method': 'iqr',
            'outlier_strategy': 'replace_median',
            'handle_temporal': False
        }
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        with self._lock:
            if not self.processing_history:
                return {'total_processed': 0}
            
            total_processed = len(self.processing_history)
            total_anomalies = sum(result.total_anomalies for result in self.processing_history)
            total_repaired = sum(result.repaired_count for result in self.processing_history)
            total_deleted = sum(result.deleted_count for result in self.processing_history)
            avg_processing_time = sum(result.processing_time for result in self.processing_history) / total_processed
            
            return {
                'total_processed': total_processed,
                'total_anomalies_found': total_anomalies,
                'total_repaired': total_repaired,
                'total_deleted': total_deleted,
                'average_processing_time': avg_processing_time,
                'success_rate': total_repaired / max(1, total_anomalies)
            }


# 全局实例
anomaly_data_processor = AnomalyDataProcessor()
